import { onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";

export interface KeyboardShortcutCallbacks {
  onCreateNew?: () => void;
  onFocusSearch?: () => void;
  onDeleteSelected?: () => void;
  onDuplicateSelected?: () => void;
  onRenameSelected?: () => void;
  onClearSelection?: () => void;
  onSelectAll?: () => void;
  onOpenSelected?: () => void;
  onRefresh?: () => void;
  onToggleView?: () => void;
  onShowFilters?: () => void;
  onSave?: () => void;
  onGoBack?: () => void;
}

export function useKeyboardShortcuts(
  callbacks: KeyboardShortcutCallbacks = {}
) {
  const router = useRouter();

  // 获取按键组合字符串
  const getKeyCombo = (event: KeyboardEvent): string => {
    const parts: string[] = [];

    if (event.ctrlKey) parts.push("ctrl");
    if (event.altKey) parts.push("alt");
    if (event.shiftKey) parts.push("shift");
    if (event.metaKey) parts.push("meta");

    // 处理特殊键
    let key = event.key.toLowerCase();
    if (key === " ") key = "space";
    if (key === "escape") key = "esc";

    parts.push(key);

    return parts.join("+");
  };

  // 检查是否在输入框中
  const isInInputElement = (): boolean => {
    const activeElement = document.activeElement;
    if (!activeElement) return false;

    const tagName = activeElement.tagName.toLowerCase();
    const inputTypes = ["input", "textarea", "select"];
    const isContentEditable =
      activeElement.getAttribute("contenteditable") === "true";

    return inputTypes.includes(tagName) || isContentEditable;
  };

  // 快捷键映射
  const shortcuts: Record<string, () => void> = {
    // 创建和编辑
    "ctrl+n": () => {
      if (!isInInputElement() && callbacks.onCreateNew) {
        callbacks.onCreateNew();
      }
    },

    // 搜索
    "ctrl+f": () => {
      if (callbacks.onFocusSearch) {
        callbacks.onFocusSearch();
      }
    },

    // 删除
    delete: () => {
      if (!isInInputElement() && callbacks.onDeleteSelected) {
        callbacks.onDeleteSelected();
      }
    },

    // 复制/复制
    "ctrl+d": () => {
      if (!isInInputElement() && callbacks.onDuplicateSelected) {
        callbacks.onDuplicateSelected();
      }
    },

    // 重命名
    f2: () => {
      if (!isInInputElement() && callbacks.onRenameSelected) {
        callbacks.onRenameSelected();
      }
    },

    // 清除选择
    esc: () => {
      if (callbacks.onClearSelection) {
        callbacks.onClearSelection();
      }
    },

    // 全选
    "ctrl+a": () => {
      if (!isInInputElement() && callbacks.onSelectAll) {
        callbacks.onSelectAll();
      }
    },

    // 打开选中项
    enter: () => {
      if (!isInInputElement() && callbacks.onOpenSelected) {
        callbacks.onOpenSelected();
      }
    },

    // 刷新
    f5: () => {
      if (callbacks.onRefresh) {
        callbacks.onRefresh();
      }
    },

    "ctrl+r": () => {
      if (callbacks.onRefresh) {
        callbacks.onRefresh();
      }
    },

    // 切换视图
    "ctrl+shift+v": () => {
      if (callbacks.onToggleView) {
        callbacks.onToggleView();
      }
    },

    // 显示筛选器
    "ctrl+shift+f": () => {
      if (callbacks.onShowFilters) {
        callbacks.onShowFilters();
      }
    },

    // 保存
    "ctrl+s": () => {
      if (callbacks.onSave) {
        callbacks.onSave();
      }
    },

    // 返回
    "alt+arrowleft": () => {
      if (callbacks.onGoBack) {
        callbacks.onGoBack();
      } else {
        router.back();
      }
    },

    // 导航快捷键
    "ctrl+1": () => switchTab(0),
    "ctrl+2": () => switchTab(1),
    "ctrl+3": () => switchTab(2),
    "ctrl+4": () => switchTab(3),
    "ctrl+5": () => switchTab(4)
  };

  // 切换标签页
  const switchTab = (index: number) => {
    const tabs = document.querySelectorAll(".el-tabs__item");
    if (tabs[index]) {
      (tabs[index] as HTMLElement).click();
    }
  };

  // 处理键盘事件
  const handleKeydown = (event: KeyboardEvent) => {
    const keyCombo = getKeyCombo(event);

    if (shortcuts[keyCombo]) {
      event.preventDefault();
      event.stopPropagation();
      shortcuts[keyCombo]();
    }
  };

  // 显示快捷键帮助
  const showShortcutHelp = () => {
    const helpContent = `
      <div style="text-align: left; line-height: 1.6;">
        <h3>键盘快捷键</h3>
        <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 8px;">
          <strong>Ctrl + N</strong><span>新建零件</span>
          <strong>Ctrl + F</strong><span>搜索</span>
          <strong>Delete</strong><span>删除选中项</span>
          <strong>Ctrl + D</strong><span>复制选中项</span>
          <strong>F2</strong><span>重命名</span>
          <strong>Esc</strong><span>清除选择</span>
          <strong>Ctrl + A</strong><span>全选</span>
          <strong>Enter</strong><span>打开选中项</span>
          <strong>F5 / Ctrl + R</strong><span>刷新</span>
          <strong>Ctrl + Shift + V</strong><span>切换视图</span>
          <strong>Ctrl + Shift + F</strong><span>高级筛选</span>
          <strong>Ctrl + S</strong><span>保存</span>
          <strong>Alt + ←</strong><span>返回</span>
          <strong>Ctrl + 1-5</strong><span>切换标签页</span>
        </div>
      </div>
    `;

    // 这里可以使用 ElMessageBox 或自定义对话框显示帮助
    console.log("快捷键帮助:", helpContent);
  };

  // 添加帮助快捷键
  shortcuts["f1"] = showShortcutHelp;
  shortcuts["ctrl+shift+?"] = showShortcutHelp;

  // 生命周期钩子
  onMounted(() => {
    document.addEventListener("keydown", handleKeydown, true);
  });

  onUnmounted(() => {
    document.removeEventListener("keydown", handleKeydown, true);
  });

  return {
    shortcuts,
    showShortcutHelp,
    getKeyCombo,
    isInInputElement
  };
}
