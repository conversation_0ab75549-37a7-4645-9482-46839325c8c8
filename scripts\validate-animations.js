#!/usr/bin/env node

/**
 * 动画验证脚本
 * 检查 Tailwind CSS 配置中定义的动画与代码中使用的动画是否匹配
 */

const fs = require('fs');
const path = require('path');

// 读取 tailwind.config.ts 文件
function getTailwindAnimations() {
  const configPath = path.join(__dirname, '../tailwind.config.ts');
  const configContent = fs.readFileSync(configPath, 'utf-8');
  
  // 提取动画定义
  const animationMatch = configContent.match(/animation:\s*{([^}]+)}/s);
  if (!animationMatch) {
    console.error('❌ 无法找到动画配置');
    return [];
  }
  
  const animationContent = animationMatch[1];
  const animations = [];
  
  // 匹配所有动画名称
  const animationRegex = /"([^"]+)":\s*"[^"]+"/g;
  let match;
  while ((match = animationRegex.exec(animationContent)) !== null) {
    animations.push(match[1]);
  }
  
  return animations;
}

// 搜索代码中使用的动画类
function getUsedAnimations() {
  const usedAnimations = new Set();
  
  // 搜索的文件类型和目录
  const searchPaths = [
    'src/**/*.vue',
    'src/**/*.css',
    'src/**/*.scss',
    'src/**/*.ts',
    'src/**/*.js'
  ];
  
  const glob = require('glob');
  
  searchPaths.forEach(pattern => {
    const files = glob.sync(pattern, { cwd: path.join(__dirname, '..') });
    
    files.forEach(file => {
      const filePath = path.join(__dirname, '..', file);
      const content = fs.readFileSync(filePath, 'utf-8');
      
      // 匹配 animate- 开头的类名
      const animateRegex = /animate-([a-z-]+)/g;
      let match;
      while ((match = animateRegex.exec(content)) !== null) {
        usedAnimations.add(match[1]);
      }
    });
  });
  
  return Array.from(usedAnimations);
}

// 主验证函数
function validateAnimations() {
  console.log('🔍 开始验证动画配置...\n');
  
  const definedAnimations = getTailwindAnimations();
  const usedAnimations = getUsedAnimations();
  
  console.log('📋 已定义的动画:');
  definedAnimations.forEach(anim => {
    console.log(`  ✅ ${anim}`);
  });
  
  console.log('\n🎯 代码中使用的动画:');
  usedAnimations.forEach(anim => {
    const isDefined = definedAnimations.includes(anim);
    console.log(`  ${isDefined ? '✅' : '❌'} ${anim}`);
  });
  
  // 检查未定义的动画
  const undefinedAnimations = usedAnimations.filter(anim => 
    !definedAnimations.includes(anim)
  );
  
  // 检查未使用的动画
  const unusedAnimations = definedAnimations.filter(anim => 
    !usedAnimations.includes(anim)
  );
  
  console.log('\n📊 验证结果:');
  
  if (undefinedAnimations.length > 0) {
    console.log('\n❌ 未定义的动画:');
    undefinedAnimations.forEach(anim => {
      console.log(`  - ${anim}`);
    });
  }
  
  if (unusedAnimations.length > 0) {
    console.log('\n⚠️  未使用的动画:');
    unusedAnimations.forEach(anim => {
      console.log(`  - ${anim}`);
    });
  }
  
  if (undefinedAnimations.length === 0 && unusedAnimations.length === 0) {
    console.log('✅ 所有动画配置正确！');
  }
  
  // 生成修复建议
  if (undefinedAnimations.length > 0) {
    console.log('\n🔧 修复建议:');
    console.log('在 tailwind.config.ts 的 animation 部分添加以下动画:');
    undefinedAnimations.forEach(anim => {
      console.log(`"${anim}": "${anim} 0.3s ease-in-out",`);
    });
  }
  
  return undefinedAnimations.length === 0;
}

// 运行验证
if (require.main === module) {
  try {
    const isValid = validateAnimations();
    process.exit(isValid ? 0 : 1);
  } catch (error) {
    console.error('❌ 验证过程中出错:', error.message);
    process.exit(1);
  }
}

module.exports = { validateAnimations, getTailwindAnimations, getUsedAnimations };
