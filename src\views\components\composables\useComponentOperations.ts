import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { addComponent, getComponentByID } from "@/api/componentType";
import type { ComponentInfoDto } from "@/types/componentType";

export function useComponentOperations() {
  // 创建对话框状态
  const createDialogVisible = ref(false);
  const copyComponentId = ref<number | null>(null);

  // 新组件表单数据
  const newComponent = ref({
    name: "",
    tags: [] as string[],
    description: "",
    version: "1.0.0",
    versionDescription: ""
  });

  // 表单验证规则
  const formRules = {
    name: [
      { required: true, message: "请输入零件名称", trigger: "blur" },
      { min: 2, max: 50, message: "名称长度在 2 到 50 个字符", trigger: "blur" }
    ],
    tags: [{ required: true, message: "请选择零件分组", trigger: "change" }],
    version: [
      { required: true, message: "请输入版本号", trigger: "blur" },
      {
        pattern: /^\d+\.\d+\.\d+$/,
        message: "版本号格式应为 x.y.z",
        trigger: "blur"
      }
    ],
    versionDescription: [
      { required: true, message: "请输入版本说明", trigger: "blur" }
    ]
  };

  // 重置表单
  const resetForm = () => {
    newComponent.value = {
      name: "",
      tags: [],
      description: "",
      version: "1.0.0",
      versionDescription: ""
    };
    copyComponentId.value = null;
  };

  // 处理创建组件
  const handleCreateComponent = () => {
    resetForm();
    createDialogVisible.value = true;
  };

  // 处理复制组件
  const handleCopyComponent = async (id: number) => {
    try {
      // 获取要复制的组件详情
      const componentData = await getComponentByID(id);
      if (!componentData) {
        ElMessage.error("获取零件详情失败");
        return;
      }

      // 填充表单数据
      newComponent.value = {
        name: `${componentData.basicInfo.name}_副本`,
        tags: [...(componentData.basicInfo.tags || [])],
        description: componentData.basicInfo.description || "",
        version: "1.0.0", // 新版本从 1.0.0 开始
        versionDescription: `复制自 ${componentData.basicInfo.name} v${componentData.basicInfo.version}`
      };

      copyComponentId.value = id;
      createDialogVisible.value = true;
    } catch (error) {
      console.error("复制零件失败:", error);
      ElMessage.error("复制零件失败");
    }
  };

  // 创建组件
  const createComponent = async (
    formData: any,
    refreshCallback?: () => void
  ) => {
    try {
      let componentData: ComponentInfoDto;

      if (copyComponentId.value) {
        // 复制模式：获取原组件数据并修改基本信息
        componentData = await getComponentByID(copyComponentId.value);
        if (!componentData) {
          ElMessage.error("获取原零件数据失败");
          return false;
        }

        // 更新基本信息
        componentData.basicInfo = {
          ...componentData.basicInfo,
          name: formData.name,
          tags: formData.tags,
          version: formData.version,
          versionDescription: formData.versionDescription,
          description: formData.description,
          createTime: new Date().toISOString(),
          author: "当前用户" // 这里应该从用户信息获取
        };
      } else {
        // 新建模式：创建全新的组件数据
        componentData = {
          basicInfo: {
            name: formData.name,
            version: formData.version,
            description: formData.description,
            versionDescription: formData.versionDescription,
            tags: formData.tags,
            createTime: new Date().toISOString(),
            author: "当前用户" // 这里应该从用户信息获取
          },
          attributes: [],
          subcomponents: [],
          alarms: [],
          functions: [],
          renderCondition: []
        };
      }

      // 模拟保存：添加到 localStorage
      const componentListStore = JSON.parse(
        localStorage.getItem("componentList") || "[]"
      );
      const newId =
        Math.max(0, ...componentListStore.map((c: any) => c.id || 0)) + 1;

      const newComponent = {
        id: newId,
        name: componentData.basicInfo.name,
        tags: componentData.basicInfo.tags,
        version: componentData.basicInfo.version,
        createTime: componentData.basicInfo.createTime,
        author: componentData.basicInfo.author,
        description: componentData.basicInfo.description,
        isFolder: false
      };

      componentListStore.push(newComponent);
      localStorage.setItem("componentList", JSON.stringify(componentListStore));

      // 也尝试调用 API（如果可用）
      try {
        await addComponent(componentData);
      } catch (apiError) {
        console.warn("API 调用失败，但本地保存成功:", apiError);
      }

      ElMessage.success(
        copyComponentId.value ? "复制零件成功" : "创建零件成功"
      );

      createDialogVisible.value = false;
      resetForm();

      // 刷新列表
      if (refreshCallback) {
        refreshCallback();
      }

      return true;
    } catch (error) {
      console.error("创建零件失败:", error);
      ElMessage.error("创建零件失败");
      return false;
    }
  };

  // 删除组件
  const handleDeleteComponent = async (
    id: number,
    refreshCallback?: () => void
  ) => {
    try {
      await ElMessageBox.confirm("此操作将永久删除该零件，是否继续？", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      });

      // 这里应该调用删除 API
      // await deleteComponent(id);

      // 模拟删除：从本地存储的组件列表中移除
      const componentListStore = JSON.parse(
        localStorage.getItem("componentList") || "[]"
      );
      const updatedList = componentListStore.filter(
        (comp: any) => comp.id !== id
      );
      localStorage.setItem("componentList", JSON.stringify(updatedList));

      console.log("删除组件 ID:", id);
      ElMessage.success("删除成功");

      if (refreshCallback) {
        refreshCallback();
      }
    } catch (error) {
      if (error !== "cancel") {
        console.error("删除零件失败:", error);
        ElMessage.error("删除零件失败");
      }
    }
  };

  // 批量删除组件
  const handleBatchDelete = async (
    ids: number[],
    refreshCallback?: () => void
  ) => {
    try {
      await ElMessageBox.confirm(
        `此操作将永久删除选中的 ${ids.length} 个零件，是否继续？`,
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      );

      // 这里应该调用批量删除 API
      // await batchDeleteComponents(ids);

      ElMessage.success(`成功删除 ${ids.length} 个零件`);

      if (refreshCallback) {
        refreshCallback();
      }
    } catch (error) {
      if (error !== "cancel") {
        console.error("批量删除零件失败:", error);
        ElMessage.error("批量删除零件失败");
      }
    }
  };

  // 导出组件
  const handleExportComponent = async (id: number) => {
    try {
      const componentData = await getComponentByID(id);
      if (!componentData) {
        ElMessage.error("获取零件数据失败");
        return;
      }

      // 创建下载链接
      const dataStr = JSON.stringify(componentData, null, 2);
      const dataBlob = new Blob([dataStr], { type: "application/json" });
      const url = URL.createObjectURL(dataBlob);

      const link = document.createElement("a");
      link.href = url;
      link.download = `${componentData.basicInfo.name}_v${componentData.basicInfo.version}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(url);
      ElMessage.success("导出成功");
    } catch (error) {
      console.error("导出零件失败:", error);
      ElMessage.error("导出零件失败");
    }
  };

  // 批量导出组件
  const handleBatchExport = async (ids: number[]) => {
    try {
      const components = await Promise.all(ids.map(id => getComponentByID(id)));

      const validComponents = components.filter(comp => comp !== null);

      if (validComponents.length === 0) {
        ElMessage.error("没有有效的零件数据");
        return;
      }

      // 创建批量导出数据
      const exportData = {
        exportTime: new Date().toISOString(),
        components: validComponents
      };

      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: "application/json" });
      const url = URL.createObjectURL(dataBlob);

      const link = document.createElement("a");
      link.href = url;
      link.download = `零件批量导出_${new Date().toISOString().split("T")[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(url);
      ElMessage.success(`成功导出 ${validComponents.length} 个零件`);
    } catch (error) {
      console.error("批量导出零件失败:", error);
      ElMessage.error("批量导出零件失败");
    }
  };

  return {
    // 状态
    createDialogVisible,
    newComponent,
    copyComponentId,
    formRules,

    // 方法
    resetForm,
    handleCreateComponent,
    handleCopyComponent,
    createComponent,
    handleDeleteComponent,
    handleBatchDelete,
    handleExportComponent,
    handleBatchExport
  };
}
