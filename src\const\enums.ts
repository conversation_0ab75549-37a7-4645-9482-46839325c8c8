export const valueType = [
  {
    value: 0,
    label: "未知",
    en: "unknown",
    component: "InputConfigurator"
  },
  {
    value: 1,
    label: "整数",
    en: "integer",
    component: "NumberConfigurator"
  },
  {
    value: 2,
    label: "浮点数",
    en: "float",
    component: "NumberConfigurator"
  },
  {
    value: 3,
    label: "字符串",
    en: "string",
    component: "InputConfigurator"
  },
  {
    value: 4,
    label: "布尔值",
    en: "boolean",
    component: "SwitchConfigurator"
  },
  {
    value: 5,
    label: "数组",
    en: "array",
    component: "CodeConfigurator"
  },
  {
    value: 6,
    label: "对象",
    en: "object",
    component: "CodeConfigurator"
  }
];

export type AlarmLevel = "Info" | "Warning" | "Alarm" | "Unknown";
