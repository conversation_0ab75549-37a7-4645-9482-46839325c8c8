# Component API 接口使用文档

本文档详细介绍了组件(Component)模块的API接口使用方法，包括接口功能、参数说明、返回值类型以及使用示例。

## 基础信息

- 基础URL: `http://*************:6888`
- 所有接口返回的数据结构均为标准响应格式：
  ```typescript
  {
    Record: number; // 记录数
    Message: string; // 消息
    Success: boolean; // 是否成功
    Attach: T; // 附加数据，泛型T表示具体的数据类型
  }
  ```

## 接口列表

### 1. 获取全部组件信息

获取系统中所有组件的详细信息。

```typescript
import { getAllComponent } from "@/api/component";

// 使用示例
async function fetchAllComponents() {
  try {
    const response = await getAllComponent();
    if (response.Success) {
      const components = response.Attach;
      console.log("获取到的组件列表:", components);
      return components;
    } else {
      console.error("获取组件列表失败:", response.Message);
      return [];
    }
  } catch (error) {
    console.error("请求异常:", error);
    return [];
  }
}
```

### 2. 根据ID获取单个组件详细信息

根据组件ID获取单个组件的详细信息。

```typescript
import { getComponentByID } from "@/api/component";

// 使用示例
async function fetchComponentDetail(componentId: string) {
  try {
    const response = await getComponentByID(componentId);
    if (response.Success) {
      const componentDetail = response.Attach;
      console.log("组件详情:", componentDetail);
      return componentDetail;
    } else {
      console.error("获取组件详情失败:", response.Message);
      return null;
    }
  } catch (error) {
    console.error("请求异常:", error);
    return null;
  }
}
```

### 3. 添加新组件

添加一个新的组件到系统中。

```typescript
import { addComponent } from "@/api/component";
import type { FullComponentInfoDto } from "@/api/component/types";

// 使用示例
async function createNewComponent(componentData: FullComponentInfoDto) {
  try {
    const response = await addComponent(componentData);
    if (response.Success) {
      console.log("组件创建成功!");
      return true;
    } else {
      console.error("组件创建失败:", response.Message);
      return false;
    }
  } catch (error) {
    console.error("请求异常:", error);
    return false;
  }
}
```

### 4. 更新组件信息

更新现有组件的信息。

```typescript
import { updateComponent } from "@/api/component";
import type { FullComponentInfoDto } from "@/api/component/types";

// 使用示例
async function updateExistingComponent(componentData: FullComponentInfoDto) {
  try {
    const response = await updateComponent(componentData);
    if (response.Success) {
      console.log("组件更新成功!");
      return true;
    } else {
      console.error("组件更新失败:", response.Message);
      return false;
    }
  } catch (error) {
    console.error("请求异常:", error);
    return false;
  }
}
```

### 5. 根据ID删除组件

根据组件ID删除组件。

```typescript
import { deleteComponentByID } from "@/api/component";

// 使用示例
async function removeComponent(componentId: string) {
  try {
    const response = await deleteComponentByID(componentId);
    if (response.Success) {
      console.log("组件删除成功!");
      return true;
    } else {
      console.error("组件删除失败:", response.Message);
      return false;
    }
  } catch (error) {
    console.error("请求异常:", error);
    return false;
  }
}
```

### 6. 根据ID获取组件属性

获取指定组件的属性列表。

```typescript
import { getAttributesByID } from "@/api/component";

// 使用示例
async function fetchComponentAttributes(componentId: string) {
  try {
    const response = await getAttributesByID(componentId);
    if (response.Success) {
      const attributes = response.Attach;
      console.log("组件属性:", attributes);
      return attributes;
    } else {
      console.error("获取组件属性失败:", response.Message);
      return [];
    }
  } catch (error) {
    console.error("请求异常:", error);
    return [];
  }
}
```

### 7. 根据名称获取组件属性

根据组件名称获取组件属性列表。

```typescript
import { getAttributesByName } from "@/api/component";

// 使用示例
async function fetchComponentAttributesByName(componentName: string) {
  try {
    const response = await getAttributesByName(componentName);
    if (response.Success) {
      const attributes = response.Attach;
      console.log("组件属性:", attributes);
      return attributes;
    } else {
      console.error("获取组件属性失败:", response.Message);
      return [];
    }
  } catch (error) {
    console.error("请求异常:", error);
    return [];
  }
}
```

### 8. 根据ID获取组件功能信息

获取指定组件的功能列表。

```typescript
import { getFunctionsByID } from "@/api/component";

// 使用示例
async function fetchComponentFunctions(componentId: string) {
  try {
    const response = await getFunctionsByID(componentId);
    if (response.Success) {
      const functions = response.Attach;
      console.log("组件功能:", functions);
      return functions;
    } else {
      console.error("获取组件功能失败:", response.Message);
      return [];
    }
  } catch (error) {
    console.error("请求异常:", error);
    return [];
  }
}
```

### 9. 根据名称获取组件功能信息

根据组件名称获取组件功能列表。

```typescript
import { getFunctionsByName } from "@/api/component";

// 使用示例
async function fetchComponentFunctionsByName(componentName: string) {
  try {
    const response = await getFunctionsByName(componentName);
    if (response.Success) {
      const functions = response.Attach;
      console.log("组件功能:", functions);
      return functions;
    } else {
      console.error("获取组件功能失败:", response.Message);
      return [];
    }
  } catch (error) {
    console.error("请求异常:", error);
    return [];
  }
}
```

### 10. 根据ID获取组件告警信息

获取指定组件的告警信息列表。

```typescript
import { getAlarmsByID } from "@/api/component";

// 使用示例
async function fetchComponentAlarms(componentId: string) {
  try {
    const response = await getAlarmsByID(componentId);
    if (response.Success) {
      const alarms = response.Attach;
      console.log("组件告警:", alarms);
      return alarms;
    } else {
      console.error("获取组件告警失败:", response.Message);
      return [];
    }
  } catch (error) {
    console.error("请求异常:", error);
    return [];
  }
}
```

### 11. 根据名称获取组件告警信息

根据组件名称获取组件告警信息列表。

```typescript
import { getAlarmsByName } from "@/api/component";

// 使用示例
async function fetchComponentAlarmsByName(componentName: string) {
  try {
    const response = await getAlarmsByName(componentName);
    if (response.Success) {
      const alarms = response.Attach;
      console.log("组件告警:", alarms);
      return alarms;
    } else {
      console.error("获取组件告警失败:", response.Message);
      return [];
    }
  } catch (error) {
    console.error("请求异常:", error);
    return [];
  }
}
```

### 12. 根据ID获取子组件列表

获取指定组件的子组件列表。

```typescript
import { getSubcomponentsByID } from "@/api/component";

// 使用示例
async function fetchSubcomponents(componentId: string) {
  try {
    const response = await getSubcomponentsByID(componentId);
    if (response.Success) {
      const subcomponents = response.Attach;
      console.log("子组件列表:", subcomponents);
      return subcomponents;
    } else {
      console.error("获取子组件列表失败:", response.Message);
      return [];
    }
  } catch (error) {
    console.error("请求异常:", error);
    return [];
  }
}
```

### 13. 根据名称获取子组件列表

根据组件名称获取子组件列表。

```typescript
import { getSubcomponentsByName } from "@/api/component";

// 使用示例
async function fetchSubcomponentsByName(componentName: string) {
  try {
    const response = await getSubcomponentsByName(componentName);
    if (response.Success) {
      const subcomponents = response.Attach;
      console.log("子组件列表:", subcomponents);
      return subcomponents;
    } else {
      console.error("获取子组件列表失败:", response.Message);
      return [];
    }
  } catch (error) {
    console.error("请求异常:", error);
    return [];
  }
}
```

### 14. 获取新建组件的默认配置信息

获取新建组件的默认配置信息。

```typescript
import { getNewComponentDefaultInfo } from "@/api/component";
import type { SubcomponentInfoDto } from "@/api/component/types";

// 使用示例
async function fetchNewComponentDefaultInfo(
  subcomponentInfo: SubcomponentInfoDto
) {
  try {
    const response = await getNewComponentDefaultInfo(subcomponentInfo);
    if (response.Success) {
      const defaultInfo = response.Attach;
      console.log("新组件默认配置:", defaultInfo);
      return defaultInfo;
    } else {
      console.error("获取新组件默认配置失败:", response.Message);
      return null;
    }
  } catch (error) {
    console.error("请求异常:", error);
    return null;
  }
}
```

## 类型定义

以下是API接口使用的主要类型定义：

### 组件基本信息 (ComponentBasicInfo)

```typescript
interface ComponentBasicInfo {
  guid?: string; // 组件ID
  name: string; // 组件名称
  version: string; // 版本
  versionDesc?: string; // 版本描述
  description: string; // 描述
  tags: string[]; // 标签
  createTime: string; // 创建时间
  author: string; // 作者
}
```

### 组件属性 (ComponentAttributeDto)

```typescript
interface ComponentAttributeDto {
  name: string; // 属性名称
  displayName: string; // 显示名称
  monitorInterval: number; // 监控间隔
  valueType: string; // 值类型
  description: string; // 描述
  unit: string; // 单位
  defaultValue: any; // 默认值
  rules: any; // 规则
}
```

### 组件告警 (ComponentAlarmDto)

```typescript
interface ComponentAlarmDto {
  id: string; // 告警ID
  name: string; // 告警名称
  description: string; // 告警描述
  level: string; // 告警等级
  availableHandlingMethods: string[]; // 可用处理方法
}
```

### 组件功能 (ComponentFunctionDto)

```typescript
interface ComponentFunctionDto {
  name: string; // 功能名称
  inputParameters: FunctionParameter[]; // 输入参数
  description: string; // 描述
  processes: ProcessNode[]; // 流程
}
```

### 子组件信息 (SubcomponentInfoDto)

```typescript
interface SubcomponentInfoDto {
  name: string; // 子组件名称
  componentTypeName: string; // 组件类型名称
  componentTypeVersion: string; // 组件类型版本
  paramTransferRule: string; // 参数传递规则
  attributeTransferRules: AttributeTransferRule[]; // 属性传递规则列表
}
```

### 完整组件信息 (FullComponentInfoDto)

```typescript
interface FullComponentInfoDto {
  basicInfo: ComponentBasicInfo; // 基本信息
  attributes: ComponentAttributeDto[]; // 属性列表
  subcomponents: SubcomponentInfoDto[]; // 子组件列表
  alarms: ComponentAlarmDto[]; // 告警列表
  functions: ComponentFunctionDto[]; // 功能列表
  renderConditions: any[]; // 渲染条件
}
```

## 注意事项

1. 所有接口都返回标准的响应格式，请先检查 `success` 字段确认请求是否成功
2. 错误信息会在 `message` 字段中返回
3. 实际数据在 `attach` 字段中返回
4. 所有接口都使用 `returnRawData: true` 参数，直接返回原始响应数据而不是只返回 `attach` 字段
