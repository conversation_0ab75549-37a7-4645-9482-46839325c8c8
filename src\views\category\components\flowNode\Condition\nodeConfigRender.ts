// 操作符映射
const OPERATOR_LABELS: Record<string, string> = {
  eq: "等于",
  ne: "不等于",
  gt: "大于",
  lt: "小于",
  gte: "大于等于",
  lte: "小于等于",
  contains: "包含",
  not_contains: "不包含",
  is_null: "为空",
  is_not_null: "不为空"
};

// 逻辑操作符映射
const LOGIC_LABELS: Record<string, string> = {
  AND: "且",
  OR: "或"
};

interface Condition {
  attribute: string;
  operator: string;
  value: any;
  logic?: string;
}

/**
 * 解析配置值中的条件数据
 * @param configValue 节点配置值
 * @returns 条件数组
 */
const parseConditions = (configValue: Record<string, any>): Condition[] => {
  const conditions: Condition[] = [];
  const conditionIndices = new Set<number>();

  // 提取所有条件索引
  Object.keys(configValue).forEach(key => {
    const match = key.match(/condition_(\d+)_attribute/);
    if (match) {
      conditionIndices.add(parseInt(match[1]));
    }
  });

  // 构建条件数组
  Array.from(conditionIndices)
    .sort((a, b) => a - b)
    .forEach((index, i) => {
      const attributeConfig = configValue[`condition_${index}_attribute`];
      const operatorConfig = configValue[`condition_${index}_operator`];
      const valueConfig = configValue[`condition_${index}_value`];
      const logicConfig = configValue[`condition_${index}_logic`];

      // 提取实际值
      const attribute = attributeConfig?.value || "";
      const operator = operatorConfig?.value || "";
      const value = valueConfig?.value;
      const logic = logicConfig?.value;

      if (attribute && operator) {
        const condition: Condition = {
          attribute,
          operator,
          value:
            operator === "is_null" || operator === "is_not_null" ? null : value
        };

        // 第一个条件不需要逻辑操作符
        if (i > 0 && logic) {
          condition.logic = logic;
        }

        conditions.push(condition);
      }
    });

  return conditions;
};

/**
 * 格式化单个条件为可读字符串
 * @param condition 条件对象
 * @returns 格式化的条件字符串
 */
const formatCondition = (condition: Condition, nodes: any[]): string => {
  const operatorLabel =
    OPERATOR_LABELS[condition.operator] || condition.operator;

  const formatValue = (value: any) => {
    if (typeof value !== "string") return value;
    const parts = value.split(".");
    if (parts.length === 2) {
      const [nodeId, paramName] = parts;
      const node = nodes.find(n => n.id === nodeId);
      if (node) {
        const nodeName = node.data?.configValue?.name?.value || node.data?.name;
        return `${nodeName}.${paramName}`;
      }
    }
    return value;
  };

  const attributeDisplay = formatValue(condition.attribute);

  if (
    condition.operator === "is_null" ||
    condition.operator === "is_not_null"
  ) {
    return `${attributeDisplay} ${operatorLabel}`;
  }

  let valueDisplay = formatValue(condition.value);
  if (typeof valueDisplay === "string") {
    valueDisplay = `"${valueDisplay}"`;
  } else if (valueDisplay === null || valueDisplay === undefined) {
    valueDisplay = "null";
  }

  return `${attributeDisplay} ${operatorLabel} ${valueDisplay}`;
};

/**
 * 条件判断节点配置渲染函数
 * @param configValue 节点配置值
 * @returns 格式化的条件表达式字符串
 */
export default (configValue: Record<string, any>, nodes: any[]): string => {
  if (!configValue || typeof configValue !== "object") {
    return "暂无条件配置";
  }

  const conditions = parseConditions(configValue);

  if (conditions.length === 0) {
    return "暂无条件配置";
  }

  // 格式化条件表达式
  const conditionStrings = conditions.map((condition, index) => {
    const conditionStr = formatCondition(condition, nodes);

    if (index === 0) {
      return conditionStr;
    } else {
      const logicLabel = LOGIC_LABELS[condition.logic || "AND"] || "且";
      return `${logicLabel} ${conditionStr}`;
    }
  });

  // 如果条件较多，进行分行显示
  if (conditionStrings.length > 2) {
    return conditionStrings.join("\n");
  } else {
    return conditionStrings.join(" ");
  }
};
