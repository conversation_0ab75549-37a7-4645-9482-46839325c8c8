<template>
  <div :class="['component-control-node', { selected }]" :style="nodeStyle">
    <div class="node-header">
      <div class="node-header-main">
        <div class="node-title">{{ getParamValue("name") }}</div>
      </div>
      <div class="node-description">
        {{ getParamValue("description") }}
      </div>
    </div>

    <div v-if="nodeConfigRender[nodeData.component]" class="node-content">
      <div class="node-config-render">
        <div class="config-render-content">
          {{
            nodeConfigRender[nodeData.component](
              nodeData.configValue,
              componentCategoryStore.flowData.nodes
            )
          }}
        </div>
      </div>
    </div>
    <div v-else-if="nodeConfigList.length" class="node-content">
      <!-- 显示配置信息 -->
      <div class="node-config-info">
        <div
          v-for="item in nodeConfigList"
          :key="item.field"
          class="config-item"
        >
          <span class="config-label">{{ item.title }}</span>
          <span class="config-value">{{ relativeNode(item) }}</span>
        </div>
      </div>
    </div>

    <!-- 显示输出参数 -->
    <div
      v-if="nodeData.outputParams && nodeData.outputParams.length"
      class="node-content"
    >
      <div class="node-output-params">
        <div class="output-params-title">输出参数</div>
        <div
          v-for="(param, index) in nodeData.outputParams"
          :key="index"
          class="output-param-item"
        >
          <div class="output-param-header">
            <span class="output-param-name">{{ param.name }}</span>
            <span class="output-param-type">
              {{ getValueTypeLabel(param.valueType) }}
            </span>
          </div>
          <div v-if="param.description" class="output-param-desc">
            {{ param.description }}
          </div>
        </div>
      </div>
    </div>

    <!-- 入参连接点 -->
    <Handle
      v-if="nodeData.component !== 'Start'"
      id="target"
      type="target"
      :position="Position.Left"
      :connectable="true"
      data-param-type="any"
      title="输入: 流程触发"
    />

    <template v-if="nodeData.sourceHandleList">
      <Handle
        v-for="(handle, index) in nodeData.sourceHandleList"
        :id="`source-${handle}`"
        :key="index"
        type="source"
        :position="Position.Right"
        :style="{ top: `${index * 25 + 30}px` }"
        :connectable="true"
        data-param-type="any"
        class="multiple"
        :title="handle"
      >
        <span>{{ handle }}</span>
      </Handle>
    </template>

    <!-- 出参连接点 -->
    <Handle
      v-else-if="nodeData.component !== 'End'"
      id="source"
      type="source"
      :position="Position.Right"
      :connectable="true"
      data-param-type="any"
      title="输出: 流程继续"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, toRefs } from "vue";
import type { WorkflowNodeData } from "@/types/componentType";
import { Handle, Position } from "@vue-flow/core";
import { isNil } from "lodash-es";
import { nodeConfigInfo, nodeSchemas, nodeConfigRender } from "../flowNode";
import { useComponentCategory } from "@/store/modules/componentCategory";
import { valueType } from "@/const/enums";

const props = defineProps<{
  id: string;
  nodeData: WorkflowNodeData;
  selected: boolean;
}>();

const { nodeData, selected } = toRefs(props);

const componentCategoryStore = useComponentCategory();

const relativeNode = computed(() => {
  return (item: any) => {
    if (!nodeData.value?.configValue || !nodeData.value.configValue[item.field])
      return "-";
    const value = nodeData.value.configValue[item.field].value;

    // 引用值
    if (typeof value === "string" && value.indexOf(".") === 36) {
      const [id, param] = value.split(".");
      const node = componentCategoryStore.flowData.nodes.find(
        node => node.id === id
      );
      if (node) {
        return `${node.data.configValue.name.value}.${param}`;
      } else {
        return value;
      }
    } else {
      return value;
    }
  };
});

const nodeConfigList = computed(() => {
  return nodeSchemas[nodeData.value.component];
});

// 节点整体样式
const nodeStyle = computed(() => {
  const nodeColor = nodeData.value.color || "#409eff";
  const adjustedColor = adjustColor(nodeColor, 90);
  // 计算半透明的边框颜色和阴影颜色
  const borderColor = hexToRgba(nodeColor, 0.5);
  const shadowColor = hexToRgba(nodeColor, 0.15);
  return {
    // 使用渐变边框代替单色边框顶部
    backgroundImage: `linear-gradient(to right, ${nodeColor}, ${adjustedColor})`,
    // 设置 CSS 变量供选中状态使用
    "--node-border-color": borderColor,
    "--node-shadow-color": shadowColor
  };
});

// 调整颜色亮度的辅助函数
const adjustColor = (color: string, percent: number) => {
  // 解析颜色
  const hex = color.replace("#", "");
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  // 调整亮度
  const adjustR = Math.min(255, Math.max(0, r + percent));
  const adjustG = Math.min(255, Math.max(0, g + percent));
  const adjustB = Math.min(255, Math.max(0, b + percent));
  // 转回十六进制
  const rr = Math.round(adjustR).toString(16).padStart(2, "0");
  const gg = Math.round(adjustG).toString(16).padStart(2, "0");
  const bb = Math.round(adjustB).toString(16).padStart(2, "0");
  return `#${rr}${gg}${bb}`;
};

// 将十六进制颜色转换为 rgba 格式
const hexToRgba = (hex: string, alpha: number) => {
  const color = hex.replace("#", "");
  const r = parseInt(color.substring(0, 2), 16);
  const g = parseInt(color.substring(2, 4), 16);
  const b = parseInt(color.substring(4, 6), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

const getParamValue = computed(() => {
  return (param: string) => {
    if (!nodeData.value) return "";
    if (
      nodeData.value.configValue &&
      typeof nodeData.value.configValue[param] === "object" &&
      !isNil(nodeData.value.configValue[param].value)
    ) {
      return nodeData.value.configValue[param].value || "";
    } else {
      const nodeConfig = nodeConfigInfo[nodeData.value.component];
      const paramValue = nodeConfig[param];
      return paramValue || "";
    }
  };
});

// 获取valueType的标签
const getValueTypeLabel = (type: number) => {
  const typeItem = valueType.find(item => item.value === type);
  return typeItem ? typeItem.label : "未知";
};
</script>

<style scoped>
.component-control-node {
  position: relative;
  width: 220px;
  padding: 16px;
  background-color: white;
  border: 1px solid rgba(220, 223, 230, 0.6);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  z-index: 1;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;
  /* 使用渐变边框代替单色边框顶部 */
  background-size: 100% 3px;
  background-repeat: no-repeat;
  background-position: top;
}

.component-control-node.selected {
  border: 1px solid var(--node-border-color, rgba(64, 158, 255, 0.5));
  box-shadow:
    0 0 0 4px var(--node-shadow-color, rgba(64, 158, 255, 0.15)),
    0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.node-header {
  padding-bottom: 12px;
  margin-bottom: 10px;
  border-bottom: 1px solid rgba(240, 242, 245, 0.8);
  position: relative;
}

.node-header-main {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.node-title {
  flex-grow: 1;
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: 0.3px;
}

.node-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
  word-break: break-word;
  max-height: 40px;
  overflow-y: auto;
  margin-right: 60px;
  position: relative;
  padding: 4px 8px;
  transition: all 0.2s ease;
  border-radius: 4px;
  background-color: rgba(245, 247, 250, 0.6);
  border-left: 2px solid rgba(144, 147, 153, 0.2);
}

.node-content {
  min-height: 20px;
  margin-top: 12px;
  position: relative;
}

/* 自定义配置渲染样式 */
.node-config-render {
  margin-top: 4px;
  font-size: 12px;
  background-color: rgba(249, 250, 252, 0.7);
  border-radius: 8px;
  padding: 10px;
  box-shadow: inset 0 0 0 1px rgba(220, 223, 230, 0.3);
}

.config-render-title::before {
  content: "";
  display: inline-block;
  width: 4px;
  height: 12px;
  background-color: #f56c6c;
  margin-right: 6px;
  border-radius: 2px;
}

.config-render-content {
  white-space: pre-line;
  word-break: break-word;
  line-height: 1.6;
  color: #303133;
  font-family: "Monaco", "Menlo", monospace;
  font-size: 11px;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 4px;
  border-radius: 4px;
}

/* 配置信息样式 */
.node-config-info {
  margin-top: 4px;
  font-size: 12px;
  background-color: rgba(249, 250, 252, 0.7);
  border-radius: 8px;
  padding: 10px;
  box-shadow: inset 0 0 0 1px rgba(220, 223, 230, 0.3);
}

.config-title {
  font-weight: 600;
  margin-bottom: 8px;
  color: #606266;
  font-size: 13px;
  display: flex;
  align-items: center;
}

.config-title::before {
  content: "";
  display: inline-block;
  width: 4px;
  height: 12px;
  background-color: #409eff;
  margin-right: 6px;
  border-radius: 2px;
}

.config-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  padding: 4px 6px;
  border-bottom: 1px dotted rgba(235, 238, 245, 0.8);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.config-item:hover {
  background-color: rgba(236, 245, 255, 0.3);
}

.config-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.config-label {
  color: #606266;
  font-weight: 500;
  position: relative;
  padding-left: 12px;
}

.config-label::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #67c23a;
  opacity: 0.7;
}

.config-value {
  color: #409eff;
  max-width: 60%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: right;
  font-weight: 500;
  padding: 1px 8px;
  background-color: rgba(236, 245, 255, 0.5);
  border-radius: 12px;
  font-family: "Monaco", "Menlo", monospace;
  font-size: 11px;
}

/* 输出参数样式 */
.node-output-params {
  margin-top: 4px;
  font-size: 12px;
  background-color: rgba(249, 250, 252, 0.7);
  border-radius: 8px;
  padding: 10px;
  box-shadow: inset 0 0 0 1px rgba(220, 223, 230, 0.3);
  position: relative;
}

.output-params-title {
  font-weight: 600;
  margin-bottom: 10px;
  color: #606266;
  font-size: 13px;
  border-bottom: 1px solid rgba(235, 238, 245, 0.6);
  padding-bottom: 6px;
  display: flex;
  align-items: center;
}

.output-params-title::before {
  content: "";
  display: inline-block;
  width: 4px;
  height: 12px;
  background: linear-gradient(to bottom, #409eff, #67c23a);
  margin-right: 6px;
  border-radius: 2px;
}

.output-param-item {
  margin-bottom: 8px;
  padding: 8px 10px;
  background-color: #ffffff;
  border-radius: 6px;
  border: 1px solid rgba(235, 238, 245, 0.6);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
  transition: all 0.2s ease;
}

.output-param-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06);
  border-color: rgba(64, 158, 255, 0.2);
}

.output-param-item:last-child {
  margin-bottom: 0;
}

.output-param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.output-param-name {
  font-weight: 600;
  color: #303133;
  position: relative;
  padding-left: 14px;
  font-size: 13px;
}

.output-param-name::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 2px;
  background-color: #409eff;
  opacity: 0.8;
}

.output-param-type {
  font-size: 11px;
  padding: 2px 8px;
  background-color: rgba(236, 245, 255, 0.7);
  color: #409eff;
  border-radius: 12px;
  font-weight: 500;
  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2);
  display: inline-flex;
  align-items: center;
  height: 20px;
}

.output-param-desc {
  margin-top: 6px;
  font-size: 11px;
  color: #909399;
  line-height: 1.5;
  word-break: break-word;
  padding: 4px 8px;
  background-color: rgba(245, 247, 250, 0.6);
  border-radius: 4px;
  border-left: 2px solid rgba(144, 147, 153, 0.2);
  font-style: italic;
}

/* 自定义Handle样式覆盖 */
:deep(.vue-flow__handle) {
  width: 14px;
  height: 14px;
  border-radius: 4px;
  border: none;
  z-index: 10;
  transition: all 0.2s ease;
  box-shadow:
    0 0 0 2px rgba(255, 255, 255, 0.8),
    0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.vue-flow__handle-left) {
  transform: translate(-50%, -50%);
  border-radius: 4px 12px 12px 4px;
  top: 30px;
  left: 5px;
  width: 12px;
  height: 12px;
}

:deep(.vue-flow__handle-right) {
  transform: translate(50%, -50%);
  border-radius: 12px 4px 4px 12px;
  top: 30px;
  right: 5px;
  width: 12px;
  height: 12px;
}

:deep(.vue-flow__handle-right.multiple) {
  width: auto;
  transform: translateY(-50%) translateX(10px);
  padding: 2px 6px;
  right: 9px;
  font-size: 10px;
  line-height: 9px;
  color: #fff;
}

:deep(.vue-flow__handle-right) {
  background: linear-gradient(to right, #409eff, #67c23a);
  opacity: 0.8;
}

:deep(.vue-flow__handle-right:hover) {
  opacity: 1;
}

:deep(.vue-flow__handle-left) {
  background: linear-gradient(to right, #67c23a, #409eff);
  opacity: 0.9;
}

:deep(.vue-flow__handle-left:hover) {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
}

:deep(.param-type-any) {
  background: linear-gradient(135deg, #409eff, #67c23a);
  box-shadow:
    0 0 6px rgba(64, 158, 255, 0.5),
    0 0 0 2px rgba(255, 255, 255, 0.8);
}
</style>
