import type { ComponentListItem } from "@/types/componentType";

/**
 * 按标签分组组件
 */
export const groupComponentsByTag = (components: ComponentListItem[]) => {
  const grouped: Record<string, ComponentListItem[]> = {};

  components.forEach(component => {
    component.tags.forEach(tag => {
      if (!grouped[tag]) {
        grouped[tag] = [];
      }
      grouped[tag].push(component);
    });
  });

  return grouped;
};

/**
 * 按创建时间分组组件
 */
export const groupComponentsByDate = (components: ComponentListItem[]) => {
  const grouped: Record<string, ComponentListItem[]> = {};

  components.forEach(component => {
    if (component.createTime) {
      const date = new Date(component.createTime);
      const dateKey = date.toISOString().split("T")[0]; // YYYY-MM-DD

      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }
      grouped[dateKey].push(component);
    }
  });

  return grouped;
};

/**
 * 按作者分组组件
 */
export const groupComponentsByAuthor = (components: ComponentListItem[]) => {
  const grouped: Record<string, ComponentListItem[]> = {};

  components.forEach(component => {
    const author = component.author || "未知作者";

    if (!grouped[author]) {
      grouped[author] = [];
    }
    grouped[author].push(component);
  });

  return grouped;
};

/**
 * 筛选组件
 */
export const filterComponents = (
  components: ComponentListItem[],
  filters: {
    name?: string;
    tags?: string[];
    author?: string;
    version?: string;
    dateRange?: [Date, Date] | null;
  }
) => {
  return components.filter(component => {
    // 名称筛选
    if (filters.name) {
      const nameMatch = component.name
        .toLowerCase()
        .includes(filters.name.toLowerCase());
      if (!nameMatch) return false;
    }

    // 标签筛选
    if (filters.tags && filters.tags.length > 0) {
      const tagMatch = filters.tags.some(tag => component.tags.includes(tag));
      if (!tagMatch) return false;
    }

    // 作者筛选
    if (filters.author) {
      const authorMatch = component.author
        ?.toLowerCase()
        .includes(filters.author.toLowerCase());
      if (!authorMatch) return false;
    }

    // 版本筛选
    if (filters.version) {
      const versionMatch = component.version
        .toLowerCase()
        .includes(filters.version.toLowerCase());
      if (!versionMatch) return false;
    }

    // 日期范围筛选
    if (filters.dateRange && component.createTime) {
      const componentDate = new Date(component.createTime);
      const [startDate, endDate] = filters.dateRange;

      if (componentDate < startDate || componentDate > endDate) {
        return false;
      }
    }

    return true;
  });
};

/**
 * 排序组件
 */
export const sortComponents = (
  components: ComponentListItem[],
  sortBy: string,
  sortOrder: "asc" | "desc" = "asc"
) => {
  const sorted = [...components];

  sorted.sort((a, b) => {
    let aValue = a[sortBy as keyof ComponentListItem];
    let bValue = b[sortBy as keyof ComponentListItem];

    // 处理日期类型
    if (sortBy === "createTime") {
      aValue = aValue ? new Date(aValue as string).getTime() : 0;
      bValue = bValue ? new Date(bValue as string).getTime() : 0;
    }

    // 处理字符串类型
    if (typeof aValue === "string") {
      aValue = aValue.toLowerCase();
      bValue = (bValue as string).toLowerCase();
    }

    // 处理数组类型（如 tags）
    if (Array.isArray(aValue)) {
      aValue = aValue.join(",").toLowerCase();
      bValue = (bValue as string[]).join(",").toLowerCase();
    }

    if (sortOrder === "asc") {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  return sorted;
};

/**
 * 搜索组件
 */
export const searchComponents = (
  components: ComponentListItem[],
  keyword: string,
  searchFields: string[] = ["name", "tags", "version", "description"]
) => {
  if (!keyword.trim()) return components;

  const lowerKeyword = keyword.toLowerCase();

  return components.filter(component => {
    return searchFields.some(field => {
      const value = component[field as keyof ComponentListItem];

      if (Array.isArray(value)) {
        return value.some(item =>
          item.toString().toLowerCase().includes(lowerKeyword)
        );
      }

      if (typeof value === "string") {
        return value.toLowerCase().includes(lowerKeyword);
      }

      return false;
    });
  });
};

/**
 * 获取组件统计信息
 */
export const getComponentStatistics = (components: ComponentListItem[]) => {
  const stats = {
    total: components.length,
    byTag: {} as Record<string, number>,
    byAuthor: {} as Record<string, number>,
    byMonth: {} as Record<string, number>,
    recentlyCreated: 0 // 最近7天创建的
  };

  const now = new Date();
  const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

  components.forEach(component => {
    // 按标签统计
    component.tags.forEach(tag => {
      stats.byTag[tag] = (stats.byTag[tag] || 0) + 1;
    });

    // 按作者统计
    const author = component.author || "未知作者";
    stats.byAuthor[author] = (stats.byAuthor[author] || 0) + 1;

    // 按月份统计
    if (component.createTime) {
      const date = new Date(component.createTime);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}`;
      stats.byMonth[monthKey] = (stats.byMonth[monthKey] || 0) + 1;

      // 最近创建统计
      if (date > sevenDaysAgo) {
        stats.recentlyCreated++;
      }
    }
  });

  return stats;
};

/**
 * 验证组件名称
 */
export const validateComponentName = (
  name: string
): { valid: boolean; message?: string } => {
  if (!name.trim()) {
    return { valid: false, message: "组件名称不能为空" };
  }

  if (name.length < 2) {
    return { valid: false, message: "组件名称至少需要2个字符" };
  }

  if (name.length > 50) {
    return { valid: false, message: "组件名称不能超过50个字符" };
  }

  // 检查特殊字符
  const invalidChars = /[<>:"/\\|?*]/;
  if (invalidChars.test(name)) {
    return {
      valid: false,
      message: '组件名称不能包含特殊字符 < > : " / \\ | ? *'
    };
  }

  return { valid: true };
};

/**
 * 验证版本号
 */
export const validateVersion = (
  version: string
): { valid: boolean; message?: string } => {
  if (!version.trim()) {
    return { valid: false, message: "版本号不能为空" };
  }

  const versionPattern = /^\d+\.\d+\.\d+$/;
  if (!versionPattern.test(version)) {
    return { valid: false, message: "版本号格式应为 x.y.z（如：1.0.0）" };
  }

  return { valid: true };
};

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

/**
 * 格式化日期
 */
export const formatDate = (
  date: string | Date,
  format: "short" | "long" = "short"
): string => {
  const d = new Date(date);

  if (isNaN(d.getTime())) {
    return "无效日期";
  }

  if (format === "short") {
    return d.toLocaleDateString("zh-CN");
  } else {
    return d.toLocaleString("zh-CN");
  }
};

/**
 * 生成唯一ID
 */
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

/**
 * 深拷贝对象
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }

  if (typeof obj === "object") {
    const cloned = {} as T;
    Object.keys(obj).forEach(key => {
      cloned[key as keyof T] = deepClone((obj as any)[key]);
    });
    return cloned;
  }

  return obj;
};
