import type { Api } from "@form-create/element-ui";
import { initComponentAttributes } from "../utils";

export default [
  {
    type: "switch",
    field: "isGlobal",
    title: "全局属性",
    value: false,
    valueType: 0,
    col: { span: 24 }
  },
  {
    type: "select",
    field: "AttributeName",
    title: "属性名称",
    value: "",
    valueType: 3,
    props: {
      filterable: true,
      clearable: true,
      placeholder: "请选择属性名称"
    },
    validate: [{ required: true, message: "请选择属性名称" }],
    col: { span: 24 },
    effect: {
      loadData: {
        // 数据名
        attr: "attributes",
        // 插入位置
        to: "options"
      }
    },
    inject: true,
    on: {
      change(inject: Api, value: any) {
        // 当属性名称改变时，可以在这里处理相关逻辑
        // 比如根据属性类型调整值输入框的类型
        console.log("Selected attribute:", value);
      }
    },
    hook: {
      mounted(inject) {
        const { setData, getData } = inject.$f;
        initComponentAttributes(setData, getData);
      }
    }
  }
];
