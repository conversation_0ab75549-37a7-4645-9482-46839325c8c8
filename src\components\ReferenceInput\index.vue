<template>
  <div class="reference-input-container">
    <div class="reference-input-header">
      <el-radio-group
        v-model="inputType"
        size="small"
        class="reference-type-selector"
        @change="handleTypeChange"
      >
        <el-radio-button label="static">静态值</el-radio-button>
        <el-radio-button label="reference">引用值</el-radio-button>
      </el-radio-group>
    </div>
    <div class="reference-input-content">
      <!-- 静态值输入 -->
      <template v-if="inputType === 'static'">
        <el-input
          v-model="localValue"
          :placeholder="placeholder || '请输入值'"
          @input="handleStaticInput"
        />
      </template>
      <!-- 引用值选择 -->
      <template v-else>
        <el-select
          v-model="selectedReference"
          filterable
          :placeholder="placeholder || '请选择引用值'"
          class="reference-select"
          @change="handleReferenceChange"
        >
          <el-option-group
            v-for="node in previousNodes"
            :key="node.id"
            :label="node.label"
          >
            <el-option
              v-for="param in node.outputParams"
              :key="`${node.id}-${param.name}`"
              :label="`${param.name} (${param.type})`"
              :value="`${node.id}.${param.name}`"
            />
          </el-option-group>
        </el-select>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, computed } from "vue";
import {
  ElInput,
  ElSelect,
  ElOptionGroup,
  ElOption,
  ElRadioGroup,
  ElRadioButton
} from "element-plus";
import { useVueFlow } from "@vue-flow/core";

// 定义组件的 props
const props = defineProps({
  // 表单值
  modelValue: {
    type: [String, Number],
    default: ""
  },
  // 占位符文本
  placeholder: {
    type: String,
    default: ""
  },
  // FormCreate 注入的属性
  formCreateInject: {
    type: Object,
    default: () => ({})
  }
});

// 定义组件的事件
const emit = defineEmits(["update:modelValue", "change"]);

// 本地状态
const inputType = ref("static"); // 'static' 或 'reference'
const localValue = ref("");
const selectedReference = ref("");

// 获取 Vue Flow 实例
const { getNodes, findNode } = useVueFlow();

// 获取当前节点和之前的节点
const currentNodeId = computed(() => {
  const field = props.formCreateInject?.field;
  const rule = props.formCreateInject?.rule;

  // 从 formCreateInject 中获取当前节点 ID
  if (rule && rule.nodeId) {
    return rule.nodeId;
  }

  return null;
});

// 获取所有之前的节点
const previousNodes = computed(() => {
  const nodes = getNodes.value || [];
  const currentNode = currentNodeId.value
    ? findNode(currentNodeId.value)
    : null;

  if (!currentNode) return [];

  // 找到当前节点之前的所有节点
  return nodes
    .filter(node => {
      // 过滤掉当前节点和特殊节点类型
      if (node.id === currentNodeId.value) return false;
      if (!node.data || !node.data.outputParams) return false;

      // 检查节点是否在当前节点之前
      // 这里可以根据实际需求调整逻辑，例如检查节点的位置或连接关系
      return true;
    })
    .map(node => ({
      id: node.id,
      label: node.data.name || node.id,
      outputParams: node.data.outputParams || []
    }));
});

// 初始化组件状态
onMounted(() => {
  initializeFromValue(props.modelValue);
});

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  newValue => {
    initializeFromValue(newValue);
  }
);

// 根据值初始化组件状态
const initializeFromValue = (value: string | number) => {
  const strValue = String(value);

  // 检查是否为引用格式 (例如 "ref:nodeId.paramName")
  if (strValue.startsWith("ref:")) {
    inputType.value = "reference";
    selectedReference.value = strValue.substring(4); // 移除 "ref:" 前缀
    localValue.value = "";
  } else {
    inputType.value = "static";
    localValue.value = strValue;
    selectedReference.value = "";
  }
};

// 处理输入类型变化
const handleTypeChange = (type: string) => {
  if (type === "static") {
    emit("update:modelValue", localValue.value);
    emit("change", localValue.value);
  } else {
    if (selectedReference.value) {
      const refValue = `ref:${selectedReference.value}`;
      emit("update:modelValue", refValue);
      emit("change", refValue);
    }
  }
};

// 处理静态值输入
const handleStaticInput = (value: string) => {
  emit("update:modelValue", value);
  emit("change", value);
};

// 处理引用值选择
const handleReferenceChange = (value: string) => {
  const refValue = `ref:${value}`;
  emit("update:modelValue", refValue);
  emit("change", refValue);
};
</script>

<style scoped>
.reference-input-container {
  width: 100%;
}

.reference-input-header {
  margin-bottom: 8px;
}

.reference-type-selector {
  width: 100%;
}

.reference-input-content {
  width: 100%;
}

.reference-select {
  width: 100%;
}
</style>
