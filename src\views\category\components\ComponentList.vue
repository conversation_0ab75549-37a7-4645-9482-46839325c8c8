<template>
  <el-aside
    :width="asideWidth"
    class="border-r border-gray-200 relative transition-all duration-300"
  >
    <div class="component-list" :class="{ collapsed: isCollapsed }">
      <div v-if="!isCollapsed" class="header-section mb-4">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-lg font-medium text-gray-800">零件库</h3>
          <div class="flex items-center">
            <el-button
              type="primary"
              class="create-btn mr-2"
              @click="handleCreateComponent()"
            >
              <el-icon class="mr-1"><Plus /></el-icon>
              <span>新建零件</span>
            </el-button>
            <div class="collapse-button-wrapper">
              <SidebarCenterCollapse
                :is-active="!isCollapsed"
                class="inline-block"
                @toggle-click="handleToggleCollapse"
              />
            </div>
          </div>
        </div>
        <div class="search-bar flex items-center mb-3 relative">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索零件"
            :prefix-icon="Search"
            clearable
            class="flex-1 search-input"
          />
          <el-button
            type="default"
            class="ml-2 refresh-btn"
            title="刷新列表"
            @click="refreshList"
          >
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
        <div>
          <el-input
            v-model="typeFilter"
            placeholder="按标签筛选"
            clearable
            class="w-full filter-select"
          />
        </div>
      </div>

      <div v-if="isCollapsed" class="collapsed-header">
        <el-tooltip content="展开侧边栏" placement="right">
          <div class="collapsed-toggle-btn" @click="handleToggleCollapse">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </el-tooltip>
      </div>

      <div
        v-if="!isCollapsed"
        class="component-count-container flex items-center justify-between text-sm text-gray-500 mb-2"
      >
        <div>共 {{ filteredComponentList.length }} 个零件</div>
        <div class="view-toggle-container flex items-center">
          <span class="text-xs text-gray-500 mr-2">视图:</span>
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button label="category" value="category"
              >分类</el-radio-button
            >
            <el-radio-button label="list" value="list">列表</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <div class="component-list-scroll-container">
        <div class="component-list-container">
          <template v-if="viewMode === 'category'">
            <!-- 分组显示 -->
            <div
              v-for="folder in groupedComponentList"
              :key="folder.name"
              class="folder-container mb-4"
            >
              <!-- 文件夹标题 -->
              <div
                class="folder-title flex items-center justify-between py-2 px-4 cursor-pointer rounded-t-lg transition-colors duration-200"
                :class="{
                  'bg-primary-50 border-l-2 border-r-2 border-t-2 border-primary-500':
                    folderStates[folder.name]?.isOpen,
                  'bg-gray-50 hover:bg-gray-100':
                    !folderStates[folder.name]?.isOpen
                }"
                @click="toggleFolder(folder.name)"
              >
                <div class="flex items-center">
                  <el-icon class="mr-2 text-primary-500">
                    <component
                      :is="
                        folderStates[folder.name]?.isOpen
                          ? 'folder-opened'
                          : 'folder'
                      "
                    />
                  </el-icon>
                  <span class="font-medium text-gray-700">{{
                    folder.name
                  }}</span>
                </div>
                <div class="flex items-center">
                  <span class="text-xs text-gray-500 mr-2">
                    {{ folder.children?.length || 0 }}个
                  </span>
                  <el-icon
                    class="transition-transform duration-300"
                    :class="{
                      'transform rotate-180': folderStates[folder.name]?.isOpen
                    }"
                  >
                    <arrow-down />
                  </el-icon>
                </div>
              </div>
              <!-- 文件夹内容 -->
              <div
                v-if="folderStates[folder.name]?.isOpen"
                class="folder-content pl-3 pr-1 py-2 bg-white border border-gray-100 rounded-b-lg shadow-sm"
              >
                <div
                  v-for="item in folder.children"
                  :key="item.id"
                  class="component-item flex items-center justify-between py-3 px-4 cursor-pointer mb-2 rounded-lg transition-all duration-200 hover:shadow-md hover:translate-x-1"
                  :class="{
                    'selected-item': selectedComponentId === item.id,
                    'collapsed-item': isCollapsed
                  }"
                  @click="selectComponent(item.id)"
                >
                  <div
                    v-if="!isCollapsed"
                    class="flex items-center overflow-hidden"
                  >
                    <div class="flex flex-col overflow-hidden">
                      <div class="flex items-center">
                        <span
                          class="component-name text-gray-800 font-medium truncate"
                        >
                          {{ item.name }}
                        </span>
                      </div>
                      <span class="component-version text-xs text-gray-500 mt-1"
                        >版本: {{ item.version }}</span
                      >
                    </div>
                  </div>
                  <div v-if="isCollapsed" class="collapsed-item-content">
                    <el-tooltip :content="item.name" placement="right">
                      <span class="component-initial">
                        {{ item.name.charAt(0) }}
                      </span>
                    </el-tooltip>
                  </div>
                  <div v-if="!isCollapsed" class="flex items-center">
                    <el-button
                      type="primary"
                      size="small"
                      circle
                      class="copy-btn"
                      title="复制零件"
                      @click.stop="handleCopyComponent(item.id)"
                    >
                      <el-icon><CopyDocument /></el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <!-- 列表显示 -->
            <div
              v-for="item in filteredComponentList"
              :key="item.id"
              class="component-item flex items-center justify-between py-3 px-4 cursor-pointer mb-2 rounded-lg transition-all duration-200 hover:shadow-md hover:translate-x-1"
              :class="{
                'selected-item': selectedComponentId === item.id,
                'collapsed-item': isCollapsed
              }"
              @click="selectComponent(item.id)"
            >
              <div
                v-if="!isCollapsed"
                class="flex items-center overflow-hidden"
              >
                <div class="flex flex-col overflow-hidden">
                  <div class="flex items-center">
                    <span
                      class="component-name text-gray-800 font-medium truncate"
                    >
                      {{ item.name }}
                    </span>
                    <span
                      v-for="tag in item.tags"
                      :key="tag"
                      class="component-type-tag ml-2"
                    >
                      {{ tag }}
                    </span>
                  </div>
                  <span class="component-version text-xs text-gray-500 mt-1"
                    >版本: {{ item.version }}</span
                  >
                </div>
              </div>
              <div v-if="isCollapsed" class="collapsed-item-content">
                <el-tooltip :content="item.name" placement="right">
                  <span class="component-initial">{{
                    item.name.charAt(0)
                  }}</span>
                </el-tooltip>
              </div>
              <div v-if="!isCollapsed" class="flex items-center">
                <el-button
                  type="primary"
                  size="small"
                  circle
                  class="copy-btn"
                  title="复制零件"
                  @click.stop="handleCreateComponent"
                >
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- 创建零件对话框 -->
      <el-dialog
        v-model="createDialogVisible"
        :title="copyComponentId ? '复制零件' : '创建零件'"
        width="500px"
      >
        <el-form
          ref="formRef"
          :model="newComponent"
          label-width="100px"
          :rules="rules"
        >
          <el-form-item label="零件名称" prop="name">
            <el-input v-model="newComponent.name" />
          </el-form-item>
          <el-form-item label="零件标签" prop="tags">
            <el-select
              v-model="newComponent.tags"
              multiple
              filterable
              allow-create
              default-first-option
              placeholder="请输入或选择标签"
              class="w-full"
            >
              <el-option
                v-for="tag in availableTags"
                :key="tag"
                :label="tag"
                :value="tag"
              />
            </el-select>
          </el-form-item>
          <el-form-item prop="version" label="版本号">
            <el-input v-model="newComponent.version" />
          </el-form-item>
          <el-form-item prop="versionDescription" label="版本描述">
            <el-input
              v-model="newComponent.versionDescription"
              type="textarea"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="createDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleCreate">创建</el-button>
        </template>
      </el-dialog>
    </div>
  </el-aside>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import {
  Plus,
  Refresh,
  Search,
  CopyDocument,
  ArrowRight,
  ArrowDown
} from "@element-plus/icons-vue";
import SidebarCenterCollapse from "@/layout/components/lay-sidebar/components/SidebarCenterCollapse.vue";
import {
  getAllComponent,
  addComponent,
  getComponentByID
} from "@/api/componentType";
import type {
  ComponentInfoDto,
  ComponentListItem
} from "@/types/componentType";
import { useComponentCategory } from "@/store/modules/componentCategory";

// 侧边栏是否折叠
const isCollapsed = ref(false);

// 侧边栏宽度
const asideWidth = computed(() => (isCollapsed.value ? "64px" : "280px"));

const componentStore = useComponentCategory();
// 零件列表
const componentList = ref<ComponentListItem[]>([]);
// 标签过滤
const typeFilter = ref<string>("");
// 可用标签列表
const availableTags = ref<string[]>([]);
// 视图模式：category-分类视图，list-列表视图
const viewMode = ref<"category" | "list">("category");

// 选中的零件ID
const selectedComponentId = ref(componentStore.selectedComponentId);
// 搜索关键字
const searchKeyword = ref("");
// 创建对话框可见性
const createDialogVisible = ref(false);
// 新零件信息
const newComponent = ref({
  name: null,
  tags: [],
  description: null,
  version: null,
  versionDescription: null
});

// 文件夹状态管理
const folderStates = ref<Record<string, { isOpen: boolean }>>({});

// 表单引用
const formRef = ref<FormInstance>();

const copyComponentId = ref<string>(null);

// 表单验证规则
const rules = {
  name: [{ required: true, message: "请输入零件名称", trigger: "blur" }],
  tags: [{ required: true, message: "请选择零件分组", trigger: "change" }],
  version: [{ required: true, message: "请输入版本号", trigger: "blur" }],
  versionDescription: [
    { required: true, message: "请输入版本说明", trigger: "blur" }
  ]
};

// 过滤后的零件列表
const filteredComponentList = computed(() => {
  let result = componentList.value;

  // 按标签过滤
  if (typeFilter.value) {
    const filterValue = typeFilter.value.toLowerCase();
    result = result.filter(item =>
      item.tags.some(tag => tag.toLowerCase().includes(filterValue))
    );
  }

  // 按关键字过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(item => {
      return (
        item.name.toLowerCase().includes(keyword) ||
        item.tags.some(tag => tag.toLowerCase().includes(keyword)) ||
        item.version.toLowerCase().includes(keyword)
      );
    });
  }

  return result;
});

// 按分类分组的零件列表
const groupedComponentList = computed(() => {
  if (viewMode.value !== "category") {
    return filteredComponentList.value;
  }

  // 按类型分组
  const grouped: {
    name: string;
    isFolder: true;
    children: ComponentListItem[];
  }[] = [];
  const tagGroups: Record<string, ComponentListItem[]> = {};

  // 将零件按类型分组
  filteredComponentList.value.forEach(item => {
    item.tags.forEach(tag => {
      if (!tagGroups[tag]) {
        tagGroups[tag] = [];
      }
      tagGroups[tag].push(item);
    });
  });

  // 创建分类文件夹
  Object.entries(tagGroups).forEach(([tag, items]) => {
    grouped.push({
      name: tag,
      isFolder: true,
      children: items
    });
  });

  return grouped;
});

// 选择零件
const selectComponent = async (id: string) => {
  selectedComponentId.value = id;
  await componentStore.fetchComponentInfo(id);
};

// 刷新列表
const refreshList = async () => {
  try {
    const response = await getAllComponent();
    const components = response.map(comp => ({
      id: comp.basicInfo.id || null,
      name: comp.basicInfo.name,
      tags: comp.basicInfo.tags || [],
      version: comp.basicInfo.version,
      isFolder: false
    }));

    componentList.value = components;

    // 收集所有可用标签
    const tagSet = new Set<string>();
    components.forEach(comp => {
      comp.tags.forEach(tag => tagSet.add(tag));
    });
    availableTags.value = Array.from(tagSet);

    // 初始化文件夹状态，默认全部打开
    const newFolderStates: Record<string, { isOpen: boolean }> = {};
    groupedComponentList.value.forEach(folder => {
      newFolderStates[folder.tag] = { isOpen: true };
    });
    folderStates.value = newFolderStates;
  } catch (error) {
    ElMessage.error("获取零件列表失败");
    console.error("获取零件列表失败:", error);
  }
};

// 切换文件夹展开状态
const toggleFolder = (tagName: string) => {
  if (!folderStates.value[tagName]) {
    folderStates.value[tagName] = { isOpen: true };
  } else {
    folderStates.value[tagName].isOpen = !folderStates.value[tagName].isOpen;
  }
};

// 处理折叠切换
const handleToggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 处理创建零件
const handleCreateComponent = () => {
  newComponent.value = {
    name: null,
    tags: [],
    description: null,
    version: null,
    versionDescription: null
  };
  createDialogVisible.value = true;
};

// 处理复制零件
const handleCopyComponent = async (id: string) => {
  copyComponentId.value = id;
  try {
    // 获取要复制的零件详情
    const componentData = await getComponentByID(id);
    if (!componentData) {
      ElMessage.error("获取零件详情失败");
      return;
    }
    handleCreateComponent();
  } catch (error) {
    console.error("复制零件失败:", error);
    ElMessage.error("复制零件失败");
  }
};

// 创建零件
const handleCreate = async () => {
  formRef.value.validate(async valid => {
    if (!valid) {
      ElMessage.warning("请填写必填字段");
      return;
    } else {
      let componentData: ComponentInfoDto;
      if (copyComponentId.value) {
        // 复制零件
        componentData = await getComponentByID(copyComponentId.value);
        if (!componentData) {
          ElMessage.error("获取零件详情失败");
          return;
        }
        componentData.basicInfo = {
          ...newComponent.value,
          description: componentData.basicInfo.description
        };
      } else {
        componentData = {
          basicInfo: {
            name: newComponent.value.name,
            version: newComponent.value.version,
            description:
              newComponent.value.description ||
              newComponent.value.versionDescription,
            versionDescription: newComponent.value.versionDescription,
            tags: newComponent.value.tags
          },
          attributes: [],
          subcomponents: [],
          alarms: [],
          functions: [],
          renderCondition: []
        };
      }

      try {
        await addComponent(componentData);
        ElMessage.success(
          copyComponentId.value ? "复制零件成功" : "创建零件成功"
        );
        createDialogVisible.value = false;

        // 刷新列表
        await refreshList();

        // 选中新创建的零件
        const newComponents = componentList.value.filter(
          c => c.name === `${newComponent.value.name}`
        );
        if (newComponents.length > 0) {
          selectComponent(newComponents[0].id);
        }
        copyComponentId.value = null;
      } catch (error) {
        ElMessage.error("创建零件失败");
        console.error("创建零件失败:", error);
      }
    }
  });
};

// 零件挂载时获取零件列表
onMounted(() => {
  refreshList();
});
</script>

<style scoped lang="scss">
.collapse-button-wrapper {
  display: inline-flex;
  align-items: center;

  :deep(.center-collapse) {
    position: relative;
    right: 0;
    height: 32px;
    margin-top: 0;
    transform: none;
  }
}

.component-count-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 0;
}

.view-toggle-container {
  .el-radio-group {
    --el-radio-button-checked-bg-color: var(--el-color-primary);
    --el-radio-button-checked-text-color: white;
    --el-radio-button-checked-border-color: var(--el-color-primary);
  }
}

.collapsed-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  cursor: pointer;
  background-color: var(--el-color-primary-light-9);
  border: 1px solid var(--el-color-primary-light-5);
  border-radius: 50%;
  transition: all 0.3s;

  &:hover {
    background-color: var(--el-color-primary-light-8);
    transform: scale(1.05);
  }

  .el-icon {
    font-size: 16px;
    color: var(--el-color-primary);
  }
}

.component-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 12px;
  padding-right: 6px;
  background-color: #f9fafc;
  border-right: 1px solid #ebeef5;
  transition: all 0.3s ease;

  &.collapsed {
    align-items: center;
    padding: 10px;

    .component-list-scroll-container {
      width: 100%;
    }
  }

  .collapsed-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
  }

  .collapsed-item-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }

  .component-initial {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    font-size: 16px;
    font-weight: bold;
    color: white;
    background-color: #409eff;
    border-radius: 50%;
  }

  .header-section {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #f9fafc;
  }

  .component-list-scroll-container {
    flex: 1;
    padding: 0 5px;
    margin: 0 -5px;
    overflow-y: auto;
  }

  .search-input {
    .el-input__wrapper {
      border-radius: 8px;
      box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
      transition: all 0.3s;

      &:hover,
      &:focus-within {
        box-shadow: 0 3px 10px rgb(0 0 0 / 8%);
      }
    }
  }

  .filter-select {
    .el-input__wrapper {
      border-radius: 8px;
      box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
      transition: all 0.3s;

      &:hover,
      &:focus-within {
        box-shadow: 0 3px 10px rgb(0 0 0 / 8%);
      }
    }
  }

  .refresh-btn {
    color: #606266;
    background-color: #f0f2f5;
    border: none;
    border-radius: 8px;
    transition: all 0.2s;

    &:hover {
      color: #409eff;
      background-color: #e4e7ed;
    }
  }

  .create-btn {
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s;
  }

  .component-item {
    background-color: white;
    border-left: 3px solid transparent;
    box-shadow: 0 1px 3px rgb(0 0 0 / 5%);
    will-change: transform;
    transform: translateZ(0);

    &.selected-item {
      background-color: var(--el-color-primary-light-9);
      border-left-color: var(--el-color-primary);
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    &.collapsed-item {
      display: flex;
      justify-content: center;
      width: 44px;
      height: 44px;
      padding: 8px 0;
      margin-right: auto;
      margin-left: auto;
      border-left: none;
      border-radius: 50%;

      &.selected-item {
        background-color: #ecf5ff;
        border: 2px solid #409eff;
      }
    }

    .component-type-tag {
      display: inline-block;
      padding: 2px 6px;
      margin-right: 4px;
      margin-bottom: 2px;
      font-size: 10px;
      font-weight: 500;
      color: white;
      background-color: var(--el-color-primary);
      border-radius: 10px;

      &.type-aligner {
        color: #f56c6c;
        background-color: #fef0f0;
      }
    }

    .component-name {
      max-width: 180px;
      font-weight: 500;
    }

    .copy-btn {
      opacity: 0.8;
      transition: all 0.2s;

      &:hover {
        opacity: 1;
        transform: scale(1.05);
      }
    }
  }
}
</style>
