<template>
  <div class="business-basic-info">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">基本信息</h2>
      <div class="page-actions">
        <el-button
          v-if="!isEditing"
          type="primary"
          @click="handleEdit"
          class="edit-btn"
        >
          <el-icon><Edit /></el-icon>
          编辑信息
        </el-button>
        <template v-else>
          <el-button @click="cancelEdit" class="cancel-btn">取消</el-button>
          <el-button type="primary" @click="saveBasicInfo" class="save-btn">
            <el-icon><Check /></el-icon>
            保存
          </el-button>
        </template>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-container">
      <!-- 基础信息区域 -->
      <div class="form-section">
        <h3 class="section-title">基础信息</h3>
        <div class="form-grid">
          <div class="form-item">
            <label class="form-label">组件ID</label>
            <el-input v-model="formData.id" disabled class="form-input" />
          </div>
          <div class="form-item">
            <label class="form-label">组件名称</label>
            <el-input
              v-model="formData.name"
              :disabled="!isEditing"
              class="form-input"
              placeholder="请输入组件名称"
            />
          </div>
          <div class="form-item">
            <label class="form-label">版本号</label>
            <el-input
              v-model="formData.version"
              :disabled="!isEditing"
              class="form-input"
              placeholder="如: 1.0.0"
            />
          </div>
          <div class="form-item">
            <label class="form-label">作者</label>
            <el-input v-model="formData.author" disabled class="form-input" />
          </div>
        </div>
      </div>

      <!-- 描述信息区域 -->
      <div class="form-section">
        <h3 class="section-title">描述信息</h3>
        <div class="form-grid">
          <div class="form-item full-width">
            <label class="form-label">版本说明</label>
            <el-input
              v-model="formData.versionDescription"
              type="textarea"
              :rows="3"
              :disabled="!isEditing"
              class="form-textarea"
              placeholder="请输入版本更新说明..."
            />
          </div>
          <div class="form-item full-width">
            <label class="form-label">组件描述</label>
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="4"
              :disabled="!isEditing"
              class="form-textarea"
              placeholder="请输入组件的详细描述..."
            />
          </div>
        </div>
      </div>

      <!-- 标签和时间信息区域 -->
      <div class="form-section">
        <h3 class="section-title">标签与时间</h3>
        <div class="form-grid">
          <div class="form-item full-width">
            <label class="form-label">标签</label>
            <div class="tags-container">
              <div class="tags-list">
                <el-tag
                  v-for="tag in formData.tags"
                  :key="tag"
                  :closable="isEditing"
                  :disable-transitions="false"
                  class="tag-item"
                  @close="handleRemoveTag(tag)"
                >
                  {{ tag }}
                </el-tag>
              </div>
              <div v-if="isEditing" class="tag-input-section">
                <el-input
                  v-if="inputTagVisible"
                  ref="tagInputRef"
                  v-model="inputTagValue"
                  class="tag-input"
                  size="small"
                  placeholder="输入标签名称"
                  @keyup.enter="handleInputConfirm"
                  @blur="handleInputConfirm"
                />
                <el-button
                  v-else
                  size="small"
                  @click="showTagInput"
                  class="add-tag-btn"
                >
                  <el-icon><Plus /></el-icon>
                  添加标签
                </el-button>
              </div>
            </div>
          </div>
          <div class="form-item">
            <label class="form-label">创建时间</label>
            <el-input
              v-model="formData.createTime"
              disabled
              class="form-input"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, watch, computed } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import {
  Edit,
  Check,
  InfoFilled,
  Document,
  PriceTag,
  Plus
} from "@element-plus/icons-vue";
import type { ComponentBasicInfoDto } from "@/types/componentType";
import { useComponentCategory } from "@/store/modules/componentCategory";

// Pinia store
const componentStore = useComponentCategory();

// 获取basicInfo
const storeBasicInfo = computed(
  () => componentStore.selectedComponent?.basicInfo || {}
);

// 是否处于编辑状态
const isEditing = ref(false);

// 表单数据
const formData = reactive<ComponentBasicInfoDto>({
  id: "",
  name: "",
  version: "",
  versionDescription: "",
  description: "",
  tags: [],
  createTime: "",
  author: ""
});

// 标签输入相关
const inputTagVisible = ref(false);
const inputTagValue = ref("");
const tagInputRef = ref();

// 监听store中的basicInfo变化
watch(
  storeBasicInfo,
  newBasicInfo => {
    if (newBasicInfo) {
      Object.assign(formData, {
        id: newBasicInfo.id || "",
        name: newBasicInfo.name || "",
        version: newBasicInfo.version || "",
        versionDescription: newBasicInfo.versionDescription || "",
        description: newBasicInfo.description || "",
        tags: newBasicInfo.tags || [],
        createTime: newBasicInfo.createTime || "",
        author: newBasicInfo.author || ""
      });
    }
  },
  { immediate: true, deep: true }
);

// 编辑处理
const handleEdit = () => {
  isEditing.value = true;
};

const cancelEdit = () => {
  isEditing.value = false;
  // 重置表单数据
  Object.assign(formData, storeBasicInfo.value);
};

const saveBasicInfo = async () => {
  try {
    await componentStore.updateBasicInfo(formData);
    isEditing.value = false;
    ElMessage.success("基本信息保存成功");
  } catch (error) {
    console.error("保存基本信息失败:", error);
    ElMessage.error("保存基本信息失败");
  }
};

// 标签处理
const handleRemoveTag = (tag: string) => {
  formData.tags = formData.tags.filter(t => t !== tag);
};

const showTagInput = () => {
  inputTagVisible.value = true;
  nextTick(() => {
    tagInputRef.value?.focus();
  });
};

const handleInputConfirm = () => {
  if (inputTagValue.value && !formData.tags.includes(inputTagValue.value)) {
    formData.tags.push(inputTagValue.value);
  }
  inputTagVisible.value = false;
  inputTagValue.value = "";
};
</script>

<style scoped lang="scss">
.business-basic-info {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;

  .page-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
  }

  .page-actions {
    display: flex;
    gap: 12px;

    .edit-btn,
    .save-btn {
      background: #3b82f6;
      border: 1px solid #3b82f6;
      color: white;
      transition: all 0.2s ease;

      &:hover {
        background: #2563eb;
        border-color: #2563eb;
      }
    }

    .cancel-btn {
      border: 1px solid #d1d5db;
      background: white;
      color: #374151;
      transition: all 0.2s ease;

      &:hover {
        border-color: #9ca3af;
        background: #f9fafb;
      }
    }
  }
}

.form-container {
  flex: 1;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 40px;

  &:last-child {
    margin-bottom: 0;
  }

  .section-title {
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    padding-bottom: 8px;
    border-bottom: 1px solid #f3f4f6;
  }
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;

  &.full-width {
    grid-column: 1 / -1;
  }

  .form-label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 4px;
  }

  .form-input,
  .form-textarea {
    :deep(.el-input__wrapper) {
      border-radius: 6px;
      border: 1px solid #d1d5db;
      transition: all 0.2s ease;

      &:hover {
        border-color: #9ca3af;
      }

      &.is-focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }

    :deep(.el-textarea__inner) {
      border-radius: 6px;
      border: 1px solid #d1d5db;
      transition: all 0.2s ease;

      &:hover {
        border-color: #9ca3af;
      }

      &:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }
  }
}

.tags-container {
  .tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;

    .tag-item {
      border-radius: 4px;
      font-size: 12px;
      padding: 4px 8px;
      background: #f3f4f6;
      border: 1px solid #e5e7eb;
      color: #374151;
    }
  }

  .tag-input-section {
    display: flex;
    gap: 8px;
    align-items: center;

    .tag-input {
      width: 120px;
    }

    .add-tag-btn {
      border: 1px dashed #d1d5db;
      background: #f9fafb;
      color: #6b7280;
      transition: all 0.2s ease;

      &:hover {
        border-color: #3b82f6;
        color: #3b82f6;
        background: #eff6ff;
      }
    }
  }
}
</style>
