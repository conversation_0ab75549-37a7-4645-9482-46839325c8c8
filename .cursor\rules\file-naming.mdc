---
description:
globs:
alwaysApply: false
---
- Vue单文件组件使用PascalCase命名法，如`UserProfile.vue`
- 可复用组件应添加前缀，如`Re`前缀表示通用组件
- TypeScript类型定义文件使用`.d.ts`后缀，如`user.d.ts`
- API请求模块应使用业务领域命名，如`user.ts`、`project.ts`
- 工具函数文件使用功能命名，如`formatter.ts`、`validator.ts`
- Store模块使用功能命名，如`userStore.ts`、`permissionStore.ts`
- 路由模块使用业务功能命名，如`dashboard.ts`、`settings.ts`
- 样式文件使用kebab-case命名法，如`element-override.scss`
- 测试文件使用原文件名加`.spec`或`.test`后缀
- 常量文件使用全大写下划线分隔命名，如`API_CONSTANTS.ts`
- Hook函数文件使用`use`前缀，如`usePermission.ts`
- 工具函数应使用动词或者动词+名词的形式，如`formatDate.ts`
