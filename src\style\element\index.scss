/* Element Plus 工业现代化主题定制 */
@forward "element-plus/theme-chalk/src/common/var.scss" with (
  $colors: (
    "primary": (
      "base": #0056b3,
      "light-3": #4d8dce,
      "light-5": #80b1e0,
      "light-7": #b3d4f0,
      "light-8": #cce0f5,
      "light-9": #e6f0fa,
      "dark-2": #004590
    ),
    "success": (
      "base": #2a9d8f,
      "light-3": #6fb8ae,
      "light-5": #95ccc5,
      "light-7": #bce0dc,
      "light-9": #e8f5f3
    ),
    "warning": (
      "base": #e9c46a,
      "light-3": #f0d695,
      "light-5": #f4e2b5,
      "light-7": #f9eed5,
      "light-9": #fdf9f0
    ),
    "danger": (
      "base": #e76f51,
      "light-3": #ee9a83,
      "light-5": #f3b8a8,
      "light-7": #f8d6cd,
      "light-9": #fcf0ec
    ),
    "error": (
      "base": #e76f51,
      "light-3": #ee9a83,
      "light-5": #f3b8a8,
      "light-7": #f8d6cd,
      "light-9": #fcf0ec
    ),
    "info": (
      "base": #457b9d,
      "light-3": #7aa0b7,
      "light-5": #9fbbc9,
      "light-7": #c5d6de,
      "light-9": #eaf0f3
    )
  ),

  // 背景色
  $bg-color: (
      "page": #f8f9fa,
      "overlay": #ffffff
    ),

  // 文字颜色
  $text-color: (
      "primary": #1e3a5f,
      "regular": #2c4a6b,
      "secondary": #4a6583,
      "placeholder": #8da2b8,
      "disabled": #c2d0dd
    ),

  // 边框颜色
  $border-color: (
      "base": #dde5ed,
      "light": #e8eef3,
      "lighter": #f2f6f9,
      "extra-light": #ffffff,
      "dark": #c2d0dd,
      "darker": #8da2b8
    ),

  // 填充色
  $fill-color: (
      "base": #f2f6f9,
      "light": #e8eef3,
      "lighter": #f2f6f9,
      "extra-light": #ffffff,
      "dark": #dde5ed,
      "darker": #c2d0dd,
      "blank": #ffffff
    ),

  // 字体设置
  $font-family: (
      "base":
        "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
    ),

  // 边框圆角
  $border-radius: (
      "base": 4px,
      "small": 2px,
      "round": 20px,
      "circle": 100%
    ),

  // 组件尺寸
  $common-component-size: (
      "large": 42px,
      "default": 38px,
      "small": 32px
    ),

  // 按钮
  $button: (
      "font-weight": 500,
      "border-radius": 4px,
      "padding-horizontal": 20px,
      "hover-text-color": "var(--el-color-white)",
      "hover-bg-color": "var(--el-color-primary-dark-2)",
      "hover-border-color": "var(--el-color-primary-dark-2)",
      "active-text-color": "var(--el-color-white)"
    ),

  // 输入框
  $input: (
      "border-radius": 4px,
      "hover-border-color": #0056b3,
      "focus-border-color": #0056b3,
      "disabled-bg-color": #f8f9fa,
      "disabled-border-color": #e8eef3,
      "disabled-color": #c2d0dd
    ),

  // 下拉菜单
  $dropdown: (
      "menu-box-shadow": 0 3px 6px -4px rgba(0, 0, 0, 0.12) 0 6px 16px 0
        rgba(0, 0, 0, 0.08) 0 9px 28px 8px rgba(0, 0, 0, 0.05),
      "menu-item-hover-fill": #f2f6f9,
      "menu-item-hover-color": #0056b3
    ),

  // 卡片
  $card: (
      "border-radius": 6px,
      "padding": 20px,
      "box-shadow": 0 2px 12px 0 rgba(0, 0, 0, 0.08),
      "border-color": #e8eef3,
      "header-padding": 18px 20px
    ),

  // 表格
  $table: (
      "header-bg-color": #e8eef3,
      "header-text-color": #1e3a5f,
      "row-hover-bg-color": #f2f6f9,
      "border-color": #dde5ed,
      "stripe-bg-color": #f8f9fa,
      "fixed-box-shadow": 0 0 10px rgba(0, 0, 0, 0.1),
      "header-font-weight": 600
    ),

  // 标签
  $tag: (
      "border-radius": 4px,
      "font-size": 12px,
      "bg-color": #f2f6f9,
      "border-color": #dde5ed,
      "color": #1e3a5f,
      "info-bg-color": #eaf0f3,
      "info-border-color": #c5d6de,
      "info-color": #457b9d
    ),

  // 弹窗
  $dialog: (
      "border-radius": 6px,
      "box-shadow": 0 3px 18px 0 rgba(0, 0, 0, 0.15),
      "title-font-size": 18px,
      "title-font-weight": 600,
      "header-padding": 20px 20px 10px,
      "padding-primary": 20px
    ),

  // 分页
  $pagination: (
      "button-bg-color": #ffffff,
      "button-hover-bg-color": #f2f6f9,
      "button-disabled-bg-color": #f8f9fa,
      "button-disabled-color": #c2d0dd,
      "font-size": 14px,
      "border-radius": 4px
    ),

  // 消息提示
  $message: (
      "border-radius": 6px,
      "padding": 12px 20px,
      "bg-color": #ffffff,
      "box-shadow": 0 3px 12px 0 rgba(0, 0, 0, 0.1)
    ),

  // 开关
  $switch: (
      "on-color": #0056b3,
      "off-color": #c2d0dd
    ),

  // 日期选择器
  $datepicker: (
      "border-color": #dde5ed,
      "off-text-color": #c2d0dd,
      "header-text-color": #1e3a5f,
      "icon-color": #8da2b8,
      "hover-bg-color": #f2f6f9,
      "active-color": #0056b3
    ),

  // 菜单
  $menu: (
      "hover-bg-color": #f2f6f9,
      "hover-text-color": #0056b3,
      "active-color": #0056b3,
      "text-color": #2c4a6b,
      "text-color-light": #4a6583,
      "item-height": 56px
    ),

  // 滑块
  $slider: (
      "main-bg-color": #0056b3,
      "runway-bg-color": #dde5ed,
      "disabled-color": #c2d0dd,
      "border-radius": 3px,
      "height": 6px,
      "button-size": 18px,
      "button-wrapper-size": 36px
    )
);

// 导入Element Plus的基础样式
@use "element-plus/theme-chalk/src/index.scss" as *;

// 自定义工业风格组件样式增强
:root {
  // 工业主题变量
  --industrial-grid-color: #e8eef3;
  --industrial-highlight-color: #0056b3;
  --industrial-shadow-light: 0 2px 12px rgba(0, 0, 0, 0.08);
  --industrial-shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
  --industrial-gradient-blue: linear-gradient(135deg, #0056b3 0%, #457b9d 100%);
  --industrial-gradient-gray: linear-gradient(135deg, #f8f9fa 0%, #e8eef3 100%);
}

// 工业风格数据展示增强
.el-card.industrial-dashboard {
  border: 1px solid #dde5ed;
  box-shadow: var(--industrial-shadow-light);
  transition:
    box-shadow 0.3s,
    transform 0.3s;

  &:hover {
    box-shadow: var(--industrial-shadow-medium);
    transform: translateY(-2px);
  }

  .el-card__header {
    background: var(--industrial-gradient-gray);
    border-bottom: 1px solid #dde5ed;
    font-weight: 600;
  }
}

// 工业风格按钮增强
.el-button.industrial-btn {
  &--primary {
    background: var(--industrial-gradient-blue);
    border: none;
    font-weight: 500;
    letter-spacing: 0.5px;

    &:hover,
    &:focus {
      background: linear-gradient(135deg, #004590 0%, #3d6d8c 100%);
      box-shadow: 0 4px 12px rgba(0, 86, 179, 0.3);
    }
  }

  &--action {
    border-radius: 50%;
    width: 42px;
    height: 42px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-2px);
    }
  }
}

// 工业风格表格增强
.el-table.industrial-table {
  border: 1px solid #dde5ed;
  border-radius: 6px;
  overflow: hidden;

  th {
    background-color: #e8eef3 !important;
    font-weight: 600;
    color: #1e3a5f;
  }

  .el-table__row:hover > td {
    background-color: #f2f6f9 !important;
  }

  .el-table__row:nth-child(even) {
    background-color: #f8f9fa;
  }
}

// 工业风格表单增强
.el-form.industrial-form {
  .el-form-item__label {
    font-weight: 600;
    color: #1e3a5f;
  }

  .el-input__wrapper {
    box-shadow: 0 0 0 1px #dde5ed inset;
    transition: box-shadow 0.3s;

    &:hover {
      box-shadow: 0 0 0 1px #8da2b8 inset;
    }

    &.is-focus {
      box-shadow: 0 0 0 1px #0056b3 inset;
    }
  }
}

// 工业风格标签增强
.el-tag.industrial-tag {
  border-radius: 16px;
  padding: 0 12px;
  height: 28px;
  line-height: 26px;
  font-weight: 500;

  &--primary {
    background-color: #e6f0fa;
    border-color: #cce0f5;
    color: #0056b3;
  }

  &--success {
    background-color: #e8f5f3;
    border-color: #bce0dc;
    color: #2a9d8f;
  }

  &--warning {
    background-color: #fdf9f0;
    border-color: #f9eed5;
    color: #e9c46a;
  }

  &--danger {
    background-color: #fcf0ec;
    border-color: #f8d6cd;
    color: #e76f51;
  }

  &--info {
    background-color: #eaf0f3;
    border-color: #c5d6de;
    color: #457b9d;
  }
}
