<template>
  <div class="subcomponent-manager">
    <div class="header flex justify-between items-center mb-4">
      <h2 class="text-lg font-medium">子零件管理</h2>
      <el-button type="primary" @click="handleAddSubcomponent">
        <el-icon class="mr-1"><Plus /></el-icon>添加子零件
      </el-button>
    </div>

    <el-table :data="subcomponents" border style="width: 100%">
      <el-table-column prop="name" label="名称" min-width="120" />
      <el-table-column label="零件类型名" min-width="120">
        <template #default="{ row }">
          {{ row.className || row.componentTypeName }}
        </template>
      </el-table-column>
      <el-table-column label="零件类型版本" min-width="120">
        <template #default="{ row }">
          {{ row.classVersion || row.componentTypeVersion }}
        </template>
      </el-table-column>
      <el-table-column label="参数传递规则" min-width="120">
        <template #default="{ row }">
          <el-tag
            v-if="
              row.inheritRule === 0 ||
              row.inheritRule === '0' ||
              row.inheritRule === 'sameName' ||
              row.inheritRule === 0 ||
              row.inheritRule === '0' ||
              row.inheritRule === 'sameName'
            "
            type="success"
            >同名继承</el-tag
          >
          <el-tag v-else type="warning">自定义规则</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="属性传递规则" min-width="120">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleViewRules(row)">
            查看规则 ({{ (row.attributeTransferRule || []).length }})
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEditSubcomponent(row)">
            编辑
          </el-button>
          <el-button type="danger" link @click="handleDeleteSubcomponent(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 子零件编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑子零件' : '添加子零件'"
      width="650px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="subcomponentForm"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="subcomponentForm.name" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="零件类型名" prop="className">
          <el-select
            v-model="subcomponentForm.className"
            filterable
            class="w-full"
          >
            <el-option
              v-for="item in componentTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :disabled="item.disabled"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="零件类型版本" prop="classVersion">
          <el-select v-model="subcomponentForm.classVersion" class="w-full">
            <el-option
              v-for="item in currentVersionOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="参数传递规则" prop="inheritRule">
          <el-radio-group v-model="subcomponentForm.inheritRule">
            <el-radio :value="0">同名继承</el-radio>
            <el-radio :value="1">自定义规则</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-divider>属性传递规则</el-divider>

        <div v-if="subcomponentForm.inheritRule === 1">
          <div
            v-for="(rule, index) in subcomponentForm.attributeTransferRule"
            :key="index"
            class="mb-4 p-3 border border-gray-200 rounded-md"
          >
            <div class="flex justify-end mb-2">
              <el-button
                type="danger"
                size="small"
                circle
                @click="removeRule(index)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
            <el-form-item
              :label="'子零件属性'"
              :prop="`attributeTransferRule.${index}.subAttribute`"
              :rules="{
                required: true,
                message: '请选择子零件属性',
                trigger: 'change'
              }"
            >
              <el-select
                v-model="rule.subAttribute"
                class="w-full"
                filterable
                placeholder="选择子零件属性"
                :loading="childAttributesLoading"
              >
                <el-option
                  v-for="attr in childAttributes"
                  :key="attr.name"
                  :label="attr.displayName || attr.name"
                  :value="attr.name"
                />
                <template #empty>
                  <div
                    v-if="childAttributesLoading"
                    class="text-center py-2 text-gray-500"
                  >
                    加载中...
                  </div>
                  <div v-else class="text-center py-2 text-gray-500">
                    没有可用的子零件属性
                  </div>
                </template>
              </el-select>
            </el-form-item>
            <el-form-item
              :label="'传递规则'"
              :prop="`attributeTransferRule.${index}.transferRule`"
              :rules="{
                required: true,
                message: '请选择传递规则',
                trigger: 'change'
              }"
            >
              <el-select v-model="rule.transferRule" class="w-full">
                <el-option label="固定值" :value="0" />
                <el-option label="继承值" :value="1" />
                <el-option label="计算值" :value="2" />
              </el-select>
            </el-form-item>

            <el-form-item
              v-if="rule.transferRule !== 0"
              :label="'当前零件属性'"
              :prop="`attributeTransferRule.${index}.currentAttribute`"
              :rules="{
                required: true,
                message: '请选择当前零件属性',
                trigger: 'change'
              }"
            >
              <el-select
                v-model="rule.currentAttribute"
                class="w-full"
                filterable
                placeholder="选择当前零件属性"
              >
                <el-option
                  v-for="attr in parentAttributes"
                  :key="attr.name"
                  :label="attr.displayName || attr.name"
                  :value="attr.name"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="rule.transferRule === 0"
              :label="'固定值'"
              :prop="`attributeTransferRule.${index}.fixedValue`"
              :rules="{
                required: true,
                message: '请输入固定值',
                trigger: 'blur'
              }"
            >
              <el-input v-model="rule.fixedValue" />
            </el-form-item>
            <el-form-item
              v-if="rule.transferRule === 2"
              :label="'计算表达式'"
              :prop="`attributeTransferRule.${index}.calculationFormula`"
              :rules="{
                required: true,
                message: '请输入计算表达式',
                trigger: 'blur'
              }"
            >
              <el-input
                v-model="rule.calculationFormula"
                type="textarea"
                placeholder="例如: parent.attr1 * 2 + 10"
              />
            </el-form-item>
          </div>
          <div class="flex justify-center mt-4">
            <el-button type="primary" plain @click="addRule">
              <el-icon class="mr-1"><Plus /></el-icon>添加规则
            </el-button>
          </div>
        </div>
        <div v-else class="text-gray-500 text-center py-4">
          使用同名继承时，子零件将自动继承父零件中同名的属性值
        </div>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveSubcomponent">保存</el-button>
      </template>
    </el-dialog>

    <!-- 属性传递规则查看对话框 -->
    <el-dialog v-model="rulesDialogVisible" title="属性传递规则" width="700px">
      <el-table :data="currentRules" border>
        <el-table-column
          prop="currentAttribute"
          label="当前零件属性"
          min-width="120"
        />
        <el-table-column
          prop="subAttribute"
          label="子零件属性"
          min-width="120"
        />
        <el-table-column label="传递规则" min-width="100">
          <template #default="{ row }">
            <el-tag v-if="row.transferRule === 1" type="success">继承值</el-tag>
            <el-tag v-if="row.transferRule === 0" type="warning">固定值</el-tag>
            <el-tag v-if="row.transferRule === 2" type="info">计算值</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="值/表达式" min-width="150">
          <template #default="{ row }">
            <span v-if="row.transferRule === 0">{{ row.fixedValue }}</span>
            <span v-else-if="row.transferRule === 2">{{
              row.calculationFormula
            }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog v-model="deleteDialogVisible" title="删除子零件" width="400px">
      <p>
        确定要删除子零件 "{{ currentSubcomponent?.name }}" 吗？此操作不可恢复。
      </p>
      <template #footer>
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="confirmDelete">确定删除</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, computed } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { Plus, Delete } from "@element-plus/icons-vue";
import type {
  SubcomponentInfoDto,
  ComponentAttributeDto,
  AttributeTransferRuleDto
} from "@/types/componentType";

import { getAttributesByID } from "@/api/componentType";
import { getAllComponent } from "@/api/componentType";
import { useComponentCategory } from "@/store/modules/componentCategory";

// 从store获取数据
const componentStore = useComponentCategory();
const subcomponents = computed(
  () => componentStore.selectedComponent?.subcomponents || []
);
const currentComponentId = computed(
  () => componentStore.selectedComponent?.basicInfo.id
);
const parentAttributes = computed(
  () => componentStore.selectedComponent?.attributes || []
);

// 对话框可见性
const dialogVisible = ref(false);
const deleteDialogVisible = ref(false);
const rulesDialogVisible = ref(false);

// 是否是编辑模式
const isEdit = ref(false);

// 当前操作的子零件
const currentSubcomponent = ref<SubcomponentInfoDto>();

// 当前查看的规则
const currentRules = ref<AttributeTransferRuleDto[]>([]);

// 表单引用
const formRef = ref<FormInstance>();

// 子零件表单数据
const subcomponentForm = reactive<{
  name: string;
  className: string;
  classVersion: string;
  inheritRule: number;
  attributeTransferRule: AttributeTransferRuleDto[];
}>({
  name: "",
  className: "",
  classVersion: "",
  inheritRule: 0, // 0 = 同名继承
  attributeTransferRule: []
});

// 表单验证规则
const formRules = reactive<FormRules>({
  name: [
    { required: true, message: "请输入子零件名称", trigger: "blur" },
    {
      pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/,
      message: "名称只能包含字母、数字和下划线，且必须以字母开头",
      trigger: "blur"
    }
  ],
  className: [{ required: true, message: "请选择零件类型", trigger: "change" }],
  classVersion: [
    { required: true, message: "请输入零件类型版本", trigger: "blur" }
  ],
  inheritRule: [
    { required: true, message: "请选择参数传递规则", trigger: "change" }
  ]
});

// 零件类型列表
const componentTypes = ref<
  { label: string; value: string; disabled?: boolean; id?: number }[]
>([]);

// 零件类型版本映射表 {零件类型名: 版本列表}
const componentVersions = ref<
  Record<string, { label: string; value: string; id?: number }[]>
>({});

// 当前选择的零件类型对应的版本列表
const currentVersionOptions = ref<
  { label: string; value: string; id?: number }[]
>([]);

// 当前选择的零件类型的ID
const selectedComponentId = ref<number>(null);

// 获取零件类型列表
const fetchComponentTypes = async () => {
  try {
    const components = await getAllComponent();

    // 创建零件类型映射，保存每个零件的ID
    const componentMap = new Map<string, Map<string, number>>();

    components.forEach(component => {
      const typeName = component.basicInfo.name;
      const typeVersion = component.basicInfo.version;
      const typeId = component.basicInfo.id || null;

      if (!componentMap.has(typeName)) {
        componentMap.set(typeName, new Map<string, number>());
      }
      componentMap.get(typeName)?.set(typeVersion, typeId);
    });

    // 转换为组件需要的格式
    const types: {
      label: string;
      value: string;
      disabled?: boolean;
      id?: number;
    }[] = [];
    const versions: Record<
      string,
      { label: string; value: string; id?: number }[]
    > = {};

    componentMap.forEach((versionMap, typeName) => {
      // 如果是当前零件，则禁用该选项
      const isCurrentComponent =
        typeName === componentStore.selectedComponent.basicInfo.name;

      // 获取该类型的第一个版本的ID（用于类型列表）
      const firstVersionEntry = Array.from(versionMap.entries())[0];
      const firstVersionId = firstVersionEntry ? firstVersionEntry[1] : null;

      types.push({
        label: typeName,
        value: typeName,
        disabled: isCurrentComponent, // 当前零件禁用
        id: firstVersionId // 保存ID
      });

      const versionOptions = Array.from(versionMap.entries()).map(
        ([version, id]) => ({
          label: version,
          value: version,
          id: id // 保存每个版本的ID
        })
      );
      versions[typeName] = versionOptions;
    });

    componentTypes.value = types;
    componentVersions.value = versions;
  } catch (error) {
    ElMessage.error("获取零件类型列表失败");
    console.error("获取零件类型列表失败:", error);
  }
};

// 监听零件类型变化，更新版本选项和子零件属性
watch(
  () => subcomponentForm.className,
  newClassName => {
    if (newClassName && componentVersions.value[newClassName]) {
      currentVersionOptions.value = componentVersions.value[newClassName];

      // 如果当前版本不在可选版本列表中，则选择第一个版本
      const versionExists = currentVersionOptions.value.some(
        option => option.value === subcomponentForm.classVersion
      );

      if (!versionExists && currentVersionOptions.value.length > 0) {
        subcomponentForm.classVersion = currentVersionOptions.value[0].value;
      }

      // 找到对应类型的第一个版本的ID
      const typeOption = componentTypes.value.find(
        type => type.value === newClassName
      );
      if (typeOption && typeOption.id) {
        selectedComponentId.value = typeOption.id;
      }

      // 如果已选择版本，则更新ID
      if (subcomponentForm.classVersion) {
        updateSelectedComponentId();
      }
    } else {
      currentVersionOptions.value = [];
      subcomponentForm.classVersion = "";
      childAttributes.value = [];
      selectedComponentId.value = null;
    }
  }
);

watch(
  () => currentComponentId.value,
  newComponentId => {
    // 当当前组件ID变化时，更新componentTypes中的disabled状态
    if (componentStore.selectedComponent?.basicInfo?.name) {
      const currentComponentName =
        componentStore.selectedComponent.basicInfo.name;
      componentTypes.value = componentTypes.value.map(type => ({
        ...type,
        disabled: type.value === currentComponentName
      }));
    }
  }
);

// 监听零件类型版本变化，更新子零件属性
watch(
  () => subcomponentForm.classVersion,
  newVersion => {
    // 当版本变化时，更新选中的零件ID
    if (subcomponentForm.className && newVersion) {
      updateSelectedComponentId();
    }
  }
);

// 更新选中的零件ID
const updateSelectedComponentId = () => {
  if (subcomponentForm.className && subcomponentForm.classVersion) {
    const versionOption = currentVersionOptions.value.find(
      option => option.value === subcomponentForm.classVersion
    );

    if (versionOption && versionOption.id) {
      selectedComponentId.value = versionOption.id;
      // 获取子零件属性
      fetchChildAttributes();
    }
  }
};

// 组件挂载时获取零件类型列表
onMounted(() => {
  fetchComponentTypes();
});

// 子零件属性列表
const childAttributes = ref<ComponentAttributeDto[]>([]);

// 子零件属性加载状态
const childAttributesLoading = ref(false);

// 获取子零件属性列表
const fetchChildAttributes = async () => {
  if (!selectedComponentId.value) {
    childAttributes.value = [];
    return;
  }

  childAttributesLoading.value = true;
  try {
    const attributes = await getAttributesByID(selectedComponentId.value);
    // 将API返回的属性转换为组件需要的格式
    childAttributes.value = attributes.map((attr: ComponentAttributeDto) => {
      // 将API返回的type转换为AttributeValueType
      let valueType = "string";
      if (attr.type !== undefined) {
        // 如果没有valueType，则根据type字段转换
        switch (attr.type) {
          case 1: // Int
          case 2: // Double
            valueType = "Int";
            break;
          case 4: // Bool
            valueType = "bool";
            break;
          case 5: // Array
            valueType = "array";
            break;
          case 6: // Object
            valueType = "object";
            break;
          case 3: // String
          default:
            valueType = "string";
        }
      }

      return {
        name: attr.name || "",
        displayName: attr.displayName || attr.name || "",
        valueType: valueType,
        description: attr.description || "",
        unit: attr.unit || "",
        defaultValue: attr.defaultValue || "",
        rules: attr.limitRule || {}
      };
    });
  } catch (error) {
    ElMessage.error("获取子零件属性失败");
    console.error("获取子零件属性失败:", error);
    childAttributes.value = [];
  } finally {
    childAttributesLoading.value = false;
  }
};

// 处理添加子零件
const handleAddSubcomponent = () => {
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
};

// 处理编辑子零件
const handleEditSubcomponent = (row: SubcomponentInfoDto) => {
  isEdit.value = true;
  currentSubcomponent.value = row;

  // 填充表单数据
  subcomponentForm.name = row.name;
  subcomponentForm.className = row.className;
  subcomponentForm.classVersion = row.classVersion;

  // 设置当前版本选项
  if (
    subcomponentForm.className &&
    componentVersions.value[subcomponentForm.className]
  ) {
    currentVersionOptions.value =
      componentVersions.value[subcomponentForm.className];

    // 更新选中的零件ID
    const versionOption = currentVersionOptions.value.find(
      option => option.value === subcomponentForm.classVersion
    );

    if (versionOption && versionOption.id) {
      selectedComponentId.value = versionOption.id;
      // 获取子零件属性
      fetchChildAttributes();
    }
  } else {
    currentVersionOptions.value = [];
    childAttributes.value = [];
    selectedComponentId.value = null;
  }

  // 将字符串类型的参数传递规则转换为数字类型
  if (typeof row.inheritRule === "string") {
    subcomponentForm.inheritRule = row.inheritRule === "sameName" ? 0 : 1;
  } else if (typeof row.inheritRule === "number") {
    subcomponentForm.inheritRule = row.inheritRule;
  } else {
    subcomponentForm.inheritRule = 0; // 默认为同名继承
  }

  // 确保属性传递规则完整
  const rules = JSON.parse(JSON.stringify(row.attributeTransferRule || []));
  subcomponentForm.attributeTransferRule = rules.map((rule: any) => {
    return {
      currentAttribute: rule.currentAttribute || "",
      subAttribute: rule.subAttribute || "",
      transferRule: rule.transferRule,
      fixedValue: rule.fixedValue !== undefined ? rule.fixedValue : null,
      calculationFormula: rule.calculationFormula || ""
    };
  });

  dialogVisible.value = true;
};

// 处理查看规则
const handleViewRules = (row: SubcomponentInfoDto) => {
  currentRules.value = row.attributeTransferRule || [];
  rulesDialogVisible.value = true;
};

// 处理删除子零件
const handleDeleteSubcomponent = (row: SubcomponentInfoDto) => {
  currentSubcomponent.value = row;
  deleteDialogVisible.value = true;
};

// 确认删除
const confirmDelete = async () => {
  if (!currentSubcomponent.value) return;

  try {
    // 移除要删除的子零件
    const updatedSubcomponents = subcomponents.value.filter(
      sub => sub.name !== currentSubcomponent.value?.name
    );

    // 使用Pinia store更新组件
    const response = await componentStore.updateCurrentComponent({
      subcomponents: updatedSubcomponents
    });

    if (response.success) {
      ElMessage.success("删除成功");
      deleteDialogVisible.value = false;
    } else {
      ElMessage.error(`删除失败: ${response.message}`);
    }
  } catch (error) {
    ElMessage.error("删除失败");
    console.error("删除子零件失败:", error);
  }
};

// 添加规则
const addRule = () => {
  subcomponentForm.attributeTransferRule.push({
    currentAttribute: "",
    subAttribute: "",
    transferRule: 0,
    fixedValue: null,
    calculationFormula: ""
  });
};

// 移除规则
const removeRule = (index: number) => {
  subcomponentForm.attributeTransferRule.splice(index, 1);
};

// 清理属性传递规则中的空属性
const cleanAttributeTransferRules = (
  rules: AttributeTransferRuleDto[]
): AttributeTransferRuleDto[] => {
  return rules.map(rule => {
    // 创建一个新对象，不修改原对象
    const cleanedRule: AttributeTransferRuleDto = {
      currentAttribute: rule.currentAttribute,
      subAttribute: rule.subAttribute,
      transferRule: rule.transferRule
    };

    // 根据传递规则类型添加相应属性
    if (rule.transferRule === 0) {
      // 固定值
      // 只有当fixedValue不为null且不为空字符串时才添加
      if (rule.fixedValue !== null && rule.fixedValue !== "") {
        cleanedRule.fixedValue = rule.fixedValue;
      }
    } else if (rule.transferRule === 2) {
      // 计算值
      // 只有当calculationFormula不为null且不为空字符串时才添加
      if (rule.calculationFormula !== null && rule.calculationFormula !== "") {
        cleanedRule.calculationFormula = rule.calculationFormula;
      }
    }
    // 对于 transferRule === 1 (继承值)，不需要这两个属性

    return cleanedRule;
  });
};

// 保存子零件
const saveSubcomponent = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async valid => {
    if (valid) {
      try {
        // 处理属性传递规则
        let attributeRules: AttributeTransferRuleDto[] = [];
        if (subcomponentForm.inheritRule === 1) {
          // 1 = 自定义规则
          // 清理属性传递规则中的空属性
          attributeRules = cleanAttributeTransferRules(
            subcomponentForm.attributeTransferRule
          );
        }

        // 构造子零件数据
        const subcomponentData: SubcomponentInfoDto = {
          name: subcomponentForm.name,
          className: subcomponentForm.className,
          classVersion: subcomponentForm.classVersion,
          inheritRule: subcomponentForm.inheritRule,
          attributeTransferRule: attributeRules
        };

        let response;
        if (isEdit.value) {
          // 更新子零件 - 使用Pinia store
          // 需要获取当前组件子零件列表并更新特定子零件
          const updatedSubcomponents = [...subcomponents.value];
          const subcomponentIndex = updatedSubcomponents.findIndex(
            sub => sub.name === subcomponentForm.name
          );

          if (subcomponentIndex >= 0) {
            updatedSubcomponents[subcomponentIndex] = subcomponentData;
          } else {
            updatedSubcomponents.push(subcomponentData);
          }

          // 调用store更新组件
          response = await componentStore.updateCurrentComponent({
            subcomponents: updatedSubcomponents
          });
        } else {
          // 添加子零件 - 使用Pinia store
          const updatedSubcomponents = [
            ...subcomponents.value,
            subcomponentData
          ];

          // 调用store更新组件
          response = await componentStore.updateCurrentComponent({
            subcomponents: updatedSubcomponents
          });
        }

        if (response.success) {
          ElMessage.success(isEdit.value ? "更新成功" : "添加成功");
          dialogVisible.value = false;
        } else {
          ElMessage.error(
            `${isEdit.value ? "更新" : "添加"}失败: ${response.message}`
          );
        }
      } catch (error) {
        ElMessage.error(isEdit.value ? "更新失败" : "添加失败");
        console.error(
          isEdit.value ? "更新子零件失败:" : "添加子零件失败:",
          error
        );
      }
    }
  });
};

// 重置表单
const resetForm = () => {
  subcomponentForm.name = "";
  subcomponentForm.className = "";
  subcomponentForm.classVersion = "";
  subcomponentForm.inheritRule = 0; // 0 = 同名继承
  subcomponentForm.attributeTransferRule = [];
  currentVersionOptions.value = [];

  // 重置表单验证
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 监听参数传递规则变化
watch(
  () => subcomponentForm.inheritRule,
  newRule => {
    if (newRule === 0) {
      // 0 = 同名继承
      subcomponentForm.attributeTransferRule = [];
    }
  }
);
</script>

<style scoped lang="scss">
.subcomponent-manager {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}
</style>
