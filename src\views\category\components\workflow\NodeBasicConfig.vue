<template>
  <div class="basic-config-form">
    <template v-if="dynamicRules && dynamicRules.length && !processing">
      <formCreate
        v-model:api="formApi"
        v-model="internalModel"
        :rule="dynamicRules"
        :labelWidth="labelWidth"
        :option="formOptions"
        @change="handleValueUpdate"
      />
    </template>
    <el-empty v-else description="此节点无需配置" :image-size="60">
      <template #image>
        <div class="empty-form-icon">
          <el-icon :size="30" color="#909399"><InfoFilled /></el-icon>
        </div>
      </template>
    </el-empty>
  </div>
</template>

<script lang="ts">
import { ref, defineComponent, computed, onMounted, nextTick } from "vue";
import formCreate from "@form-create/element-ui";
import type { Api, Options, Rule } from "@form-create/element-ui";
import { InfoFilled } from "@element-plus/icons-vue";
import { cloneDeep } from "lodash-es";
import { useVueFlow } from "@vue-flow/core";
import { getNodeSchema } from "../flowNode/index";

const commonParams = [
  {
    type: "input",
    field: "name",
    title: "节点名称",
    props: {
      placeholder: "请输入节点名称"
    },
    validate: [
      { required: true, message: "此项为必填项" } // 验证规则
    ]
  },
  {
    type: "input",
    field: "description",
    title: "节点说明",
    props: {
      placeholder: "请输入节点说明"
    },
    validate: [
      { required: true, message: "此项为必填项" } // 验证规则
    ]
  }
];

export default defineComponent({
  name: "NodeBasicConfig",
  components: {
    formCreate,
    InfoFilled
  },
  props: {
    currentNodeId: {
      type: String,
      default: null
    },
    nodeType: {
      type: String,
      default: null
    },
    modelValue: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({})
    },
    labelWidth: {
      type: String,
      default: "120px"
    }
  },
  emits: ["update:modelValue", "change", "config-change"],
  setup(props, { emit }) {
    const formApi = ref<Api | null>(null);
    const processing = ref(true);
    const internalModel = ref<Record<string, any>>({
      ...props.modelValue
    });
    const { getNodes, findNode } = useVueFlow();
    const dynamicRules = ref<Rule[]>([]);

    // 获取前序节点及其输出参数
    const previousNodesWithParams = computed(() => {
      const nodes = getNodes.value || [];
      const currentNode = props.currentNodeId
        ? findNode(props.currentNodeId)
        : null;

      if (!currentNode) return [];

      return nodes
        .filter(node => {
          if (node.id === props.currentNodeId) return false;
          if (!node.data || !node.data.outputParams) return false;
          return true;
        })
        .map(node => ({
          id: node.id,
          name: node.data.name || node.id,
          label: node.data.configValue.name.value,
          outputParams: node.data.outputParams || []
        }));
    });

    // 处理表单规则，为每个规则添加引用功能
    const processRules = (rules: Rule[]) => {
      if (!rules || !Array.isArray(rules)) return [];

      const processedRules = rules.map(rule => {
        if (rule.withoutReference) {
          return rule;
        }
        // 为其他字段添加引用功能
        return {
          type: "div",
          field: rule.field,
          title: rule.title,
          col: { span: 24 },
          style: { width: "100%" },
          native: true,
          customEvent: rule.customEvent,
          children: [
            {
              type: "ReferenceToggle",
              props: {
                modelValue: props.modelValue[rule.field],
                field: rule.field,
                valueType: rule.valueType,
                label: rule.title,
                nodeId: props.currentNodeId
                // visible: props
              },
              on: {
                "update:modelValue": (_formCreateInstance: any, value: any) => {
                  internalModel.value[rule.field] = value;
                  formApi.value.setValue(rule.field, value);
                }
              },
              inject: true,
              native: true,
              children: [rule]
            }
          ]
        };
      });

      return [...commonParams, ...processedRules];
    };

    // 使用计算属性定义表单选项
    const formOptions = computed<Options>(() => ({
      submitBtn: false,
      resetBtn: false,
      formClass: "node-form",
      formItemClass: "node-form-item",
      inlineMessage: true,
      validateOnRuleChange: false,
      col: {
        span: 24
      },
      itemProps: {
        labelPosition: "top"
      },
      formData: {
        currentNodeId: props.currentNodeId,
        previousNodesOutput: previousNodesWithParams.value
          .map(node => {
            return node.outputParams.map(param => ({
              label: node.label + "." + param.name,
              value: `${node.id}.${param.name}`,
              type: param.valueType,
              description: param.description
            }));
          })
          .flat()
      }
    }));

    const handleValueUpdate = async (field: string, value: any) => {
      console.log(field, value, "handleValueUpdate");
      formApi.value.setValue(field, value);
      const nodeSchema = getNodeSchema(props.nodeType);
      const rule = nodeSchema.find(rule => rule.field === field);
      const model = cloneDeep(internalModel.value);
      // 静态值
      let transformValue = {
        name: field,
        value,
        valueType: rule?.valueType || 0,
        paramType: "static"
      };

      if (value && typeof value === "object" && value.paramType === "ref") {
        transformValue = value;
        model[field] = value;
      } else {
        model[field] = transformValue;
      }
      emit("config-change", transformValue);
    };

    onMounted(() => {
      // 清空初始状态，避免任何旧数据残留
      internalModel.value = {};

      // 克隆modelValue数据
      const data = cloneDeep(props.modelValue || {});

      // 处理静态值的转换
      Object.keys(data).forEach(key => {
        const val = data[key];
        if (
          typeof val === "object" &&
          val &&
          (val.paramType === "static" || val.paramType === "nodeRef")
        ) {
          data[key] = val.value;
        } else if (typeof val === "object" && val && val.paramType === "ref") {
          // 不对引用值进行特殊处理
        } else {
          // 确保所有未知类型的值都被清空
          data[key] = val === undefined ? undefined : val;
        }
      });

      internalModel.value = { ...data };

      processing.value = false;

      // 获取并处理节点表单规则
      const nodeSchema = getNodeSchema(props.nodeType);
      dynamicRules.value = processRules(nodeSchema);
      nextTick(() => {
        // 强制重置所有字段，确保没有旧值残留
        if (formApi.value) {
          formApi.value.resetFields();
          if (Object.keys(data).length > 0) {
            formApi.value.setValue(data);
          }
        }
      });
    });

    return {
      formApi,
      dynamicRules,
      processing,
      internalModel,
      formOptions,
      handleValueUpdate,
      previousNodesWithParams
    };
  }
});
</script>

<style scoped>
.basic-config-form {
  position: relative;
  min-height: 100px;
}

.empty-form-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--el-fill-color-light);
  margin: 0 auto;
}
</style>
