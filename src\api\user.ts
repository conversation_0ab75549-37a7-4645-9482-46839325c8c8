import { http } from "@/utils/http";

export type UserResult = {
  /** 记录数 */
  Record: number;
  /** 附加数据 */
  Attach: {
    /** token类型 */
    TokenType: string;
    /** 访问令牌 */
    AccessToken: string;
    /** 过期时间（秒） */
    ExpiresIn: number;
    /** 刷新令牌 */
    RefreshToken: string;
  };
  /** 消息 */
  Message: string;
  /** 是否成功 */
  Success: boolean;
};

export type PlatformMenu = {
  /** 菜单代码 */
  MenuCode: string;
  /** 菜单名称 */
  MenuName: string;
  /** 菜单类型 */
  MenuType: number;
  /** 图标 */
  Icon: string;
  /** URL */
  Url: string;
  /** 是否可见 */
  IsVisible: boolean;
  /** 层级 */
  LevelLayer: number;
  /** 排序 */
  SortOrder: number;
  /** 路由出口 */
  RouterOutlet: string;
  /** 目标视图 */
  TargetView: string;
  /** 自定义数据 */
  CustomData: string;
  /** 是否启用 */
  IsEnable: boolean;
  /** 是否使用功能权限 */
  IsUseFeaturePermission: boolean;
  /** 产品名称 */
  ProductName: string;
  /** 显示名称 */
  DisplayName: string;
  /** ID键 */
  IdKey: string;
  /** 父菜单ID */
  MenuParentId: string;
  /** 是否使用 */
  IsUse: boolean;
  /** 组织结构代码 */
  OrganizationStructureCode: string;
  /** 父ID */
  ParentId: number;
  /** 父键 */
  ParentKey: string;
  /** 父组合代码 */
  ParentCombineCode: string;
  /** 组合代码 */
  CombineCode: string;
  /** 子菜单 */
  Children: Array<any>;
  /** 创建者用户ID */
  CreatorUserId: number;
  /** 创建者用户名 */
  CreatorUserName: string;
  /** 创建者用户真实姓名 */
  CreatorUserRealName: string;
  /** 创建时间 */
  CreationTime: string;
  /** 公司代码 */
  CompanyCode: string;
  /** 工厂代码 */
  FactoryCode: string;
  /** 需要更新的字段 */
  NeedUpdateFields: Record<string, any>;
  /** ID */
  Id: number;
};

export type UserInfo = {
  /** 记录数 */
  Record: number;
  /** 附加数据 */
  Attach: {
    /** 用户信息 */
    User: {
      /** 用户代码 */
      UserCode: string;
      /** 用户名 */
      UserName: string;
      /** 名称 */
      Name: string;
      /** 邮箱地址 */
      EmailAddress: string;
      /** 手机号 */
      PhoneNumber: string;
      /** 是否激活 */
      IsActive: boolean;
      /** 登录失败次数 */
      AccessFailedCount: number;
      /** 是否启用锁定 */
      IsLockoutEnabled: boolean;
      /** 用户类型 */
      UserType: string;
      /** 组织结构代码 */
      OrganizationStructureCode: string;
      /** 组织结构名称 */
      OrganizationStructureName: string;
      /** 时区代码 */
      TimeZoneCode: string;
      /** 时区名称 */
      TimeZoneName: string;
      /** 最后登录时间 */
      LastLoginTime: string;
      /** 创建者用户ID */
      CreatorUserId: number;
      /** 创建者用户名 */
      CreatorUserName: string;
      /** 创建者用户真实姓名 */
      CreatorUserRealName: string;
      /** 创建时间 */
      CreationTime: string;
      /** 最后修改者真实姓名 */
      LastModifierUserRealName: string;
      /** 最后修改时间 */
      LastModificationTime: string;
      /** 公司代码 */
      CompanyCode: string;
      /** 工厂代码 */
      FactoryCode: string;
      /** 需要更新的字段 */
      NeedUpdateFields: Record<string, any>;
      /** ID */
      Id: number;
      /** 备注 */
      Remark: string;
    };
    /** 用户概要信息 */
    Profile: {
      /** 用户代码 */
      UserCode: string;
      /** 名称 */
      Name: string;
      /** 创建者用户ID */
      CreatorUserId: number;
      /** 创建者用户名 */
      CreatorUserName: string;
      /** 创建时间 */
      CreationTime: string;
      /** 公司代码 */
      CompanyCode: string;
      /** 工厂代码 */
      FactoryCode: string;
      /** 需要更新的字段 */
      NeedUpdateFields: Record<string, any>;
      /** ID */
      Id: number;
    };
    /** 角色列表 */
    Roles: Array<{
      /** 角色代码 */
      RoleCode: string;
      /** 角色名称 */
      RoleName: string;
      /** 是否启用 */
      IsEnable: boolean;
      /** 组织结构代码 */
      OrganizationStructureCode: string;
      /** 组织结构名称 */
      OrganizationStructureName: string;
      /** 创建者用户ID */
      CreatorUserId: number;
      /** 创建者用户名 */
      CreatorUserName: string;
      /** 创建者用户真实姓名 */
      CreatorUserRealName: string;
      /** 创建时间 */
      CreationTime: string;
      /** 公司代码 */
      CompanyCode: string;
      /** 工厂代码 */
      FactoryCode: string;
      /** 需要更新的字段 */
      NeedUpdateFields: Record<string, any>;
      /** ID */
      Id: number;
      /** 备注 */
      Remark: string;
    }>;
    /** 菜单列表 */
    Menus: PlatformMenu[];
    /** 菜单功能列表 */
    MenuFunctions: Array<any>;
    /** 功能列表 */
    Functions: Array<any>;
  };
  /** 消息 */
  Message: string;
  /** 是否成功 */
  Success: boolean;
};

export type RefreshTokenResult = {
  /** 记录数 */
  Record: number;
  /** 附加数据 */
  Attach: {
    /** token类型 */
    TokenType: string;
    /** 访问令牌 */
    AccessToken: string;
    /** 过期时间（秒） */
    ExpiresIn: number;
    /** 刷新令牌 */
    RefreshToken: string;
  };
  /** 消息 */
  Message: string;
  /** 是否成功 */
  Success: boolean;
};

export type GetMenuResult = {
  success: boolean;
  data: Array<any>;
};

const BASE_URL = "http://192.168.0.135:8288";

/**
 * 登录
 * @param data 登录数据
 * @returns 登录结果
 */
export function getLogin(data?: object) {
  return http.post<UserResult, any>(
    `${BASE_URL}/account/login`,
    { data },
    { returnRawData: true }
  );
}

/** 刷新`token` */
export const refreshTokenApi = (data?: object) => {
  return http.request<RefreshTokenResult>("post", "/refresh-token", { data });
};

export const getUserInfo = () => {
  return http.post<UserInfo, any>(
    `${BASE_URL}/Account/PostLoginInfo?ProductName=DCP`,
    { data: {} },
    { returnRawData: true }
  );
};

// export const getUserRoutes = (role: string) => {
//   return http.post<GetMenuResult, any>(`${BASE_URL}/MenuApi/GetMenuDatas`, {
//     data: {
//       ProductName: "DCP",
//       Role: role
//     }
//   });
// };
