---
description:
globs:
alwaysApply: false
---
- 使用Vue Router 4进行路由管理，支持历史模式和哈希模式
- 路由配置应模块化，按功能分组放置在`src/router/modules`目录
- 使用路由元数据(meta)定义路由属性，如权限、标题、图标等
- 实现路由守卫(Guards)进行权限控制和页面跳转拦截
- 使用动态导入(Dynamic Import)进行代码分割，提高首屏加载速度
- 多级路由应使用嵌套路由(Nested Routes)表达层次结构
- 复杂页面切换应使用过渡动画增强用户体验
- 使用路由别名(alias)提供路径映射，优化用户导航
- 实现404页面处理无效路由
- 对参数必须的路由实现参数验证
- 路由跳转尽量使用命名路由，而不是硬编码URL
- 使用路由参数(params)和查询参数(query)传递数据
