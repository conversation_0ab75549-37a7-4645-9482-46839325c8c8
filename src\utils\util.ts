import { valueType } from "@/const/enums";

// 简单的UUID生成函数
export const generateId = () => {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

export const getParamTypeLabel = (type: number): string => {
  return valueType[type] ? valueType[type].label : "未知类型";
};

export const downloadFile = (data: any, filename: string) => {
  // JSON.stringify 的第三个参数用于格式化输出，这里设置为 2 表示缩进两个空格，使文件更易读
  const jsonString = JSON.stringify(data, null, 2);

  // 创建一个 Blob 对象，指定内容和 MIME 类型
  // MIME 类型 'application/json' 是标准的 JSON 文件类型
  const blob = new Blob([jsonString], { type: "application/json" });

  // 为 Blob 对象创建一个临时的 URL
  const url = URL.createObjectURL(blob);

  // 创建一个隐藏的 a 标签用于下载
  const link = document.createElement("a");
  link.href = url;
  // 设置 download 属性和文件名
  link.setAttribute("download", filename); // 指定下载时显示的文件名

  // 模拟点击该 a 标签来触发下载
  document.body.appendChild(link); // 需要将元素添加到 DOM 中才能点击（某些浏览器需要）
  link.click(); // 触发点击下载

  // 清理：下载完成后移除 a 标签并释放 Blob URL
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};
