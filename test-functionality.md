# 复制和删除功能测试指南

## 功能概述

已成功实现了组件的复制和删除功能，支持以下操作方式：

### 1. 复制功能

#### 触发方式：
- **表格视图**：点击操作列中的"复制"按钮
- **网格视图**：点击卡片右上角的"..."菜单，选择"复制"
- **右键菜单**：右键点击组件，选择"复制"
- **键盘快捷键**：选中组件后按 `Ctrl+D`

#### 功能特点：
- 打开创建/复制对话框
- 自动填充原组件信息
- 组件名称自动添加"_副本"后缀
- 版本号重置为 "1.0.0"
- 版本说明自动生成为"复制自 [原组件名] v[原版本]"
- 支持修改所有字段后创建新组件

### 2. 删除功能

#### 触发方式：
- **表格视图**：点击操作列中的"删除"按钮
- **网格视图**：点击卡片右上角的"..."菜单，选择"删除"
- **右键菜单**：右键点击组件，选择"删除"
- **键盘快捷键**：选中组件后按 `Delete`
- **批量删除**：选中多个组件后点击"批量删除"

#### 功能特点：
- 显示确认对话框防止误删
- 从本地存储中移除组件数据
- 自动刷新列表显示
- 显示成功提示消息

## 数据存储

### localStorage 集成
- 组件数据存储在 `localStorage` 的 `componentList` 键中
- 支持离线操作，无需后端 API
- 自动生成新的组件 ID
- 数据持久化保存

### 初始数据
- 首次访问时自动创建 5 个示例组件
- 包含不同类型的组件：Robot、LoadPort、Buffer、Chamber、EFEM
- 每个组件都有完整的元数据：名称、标签、版本、创建时间、作者、描述

## 测试步骤

### 测试复制功能：
1. 打开组件列表页面
2. 选择任意一个组件
3. 点击"复制"按钮（表格视图）或通过菜单选择复制
4. 在弹出的对话框中修改组件信息
5. 点击"复制"按钮确认
6. 验证新组件出现在列表中

### 测试删除功能：
1. 选择要删除的组件
2. 点击"删除"按钮或通过菜单选择删除
3. 在确认对话框中点击"确定"
4. 验证组件从列表中消失
5. 刷新页面验证删除持久化

### 测试批量删除：
1. 在表格视图中选中多个组件（勾选复选框）
2. 点击顶部的"批量删除"按钮
3. 确认删除操作
4. 验证所有选中的组件都被删除

## 技术实现

### 组件架构：
- `ComponentTable.vue`：表格视图的复制删除按钮
- `ComponentGrid.vue`：网格视图的下拉菜单操作
- `ComponentTree.vue`：树形视图的右键菜单支持
- `ContextMenu.vue`：统一的右键菜单组件
- `QuickActions.vue`：批量操作工具栏

### 状态管理：
- `useComponentList.ts`：组件列表数据管理
- `useComponentOperations.ts`：组件操作逻辑
- `useKeyboardShortcuts.ts`：键盘快捷键支持

### 数据流：
1. 用户触发操作 → 组件事件
2. 父组件处理 → 调用 composable 函数
3. 更新 localStorage → 刷新列表
4. UI 更新 → 显示结果

## 注意事项

- 删除操作不可撤销，请谨慎操作
- 复制的组件会自动分配新的 ID
- 所有操作都会立即保存到 localStorage
- 页面刷新后数据依然保持
