import { defineFakeRoute } from "vite-plugin-fake-server/client";
import { faker } from "@faker-js/faker/locale/zh_CN";

// 零件类型
const componentTypes = [
  { id: "sensor", name: "传感器" },
  { id: "robot", name: "机器臂" },
  { id: "loadport", name: "Loadport" },
  { id: "chamber", name: "Chamber" },
  { id: "aligner", name: "对中器" }
];

// 生成GUID
const generateGuid = () => {
  return faker.string.uuid();
};

// 生成时间戳
const _generateTimestamp = () => {
  return (
    faker.date.recent().toISOString().split("T")[0] +
    " " +
    faker.date.recent().toTimeString().split(" ")[0]
  );
};

// 生成零件列表
const generateComponentList = () => {
  const list = [];

  // 为每种零件类型生成示例
  for (const type of componentTypes) {
    // 为每种类型生成2-5个零件
    const count = faker.number.int({ min: 2, max: 5 });
    for (let i = 0; i < count; i++) {
      list.push({
        id: generateGuid(),
        name: `${type.name}${i + 1}`,
        type: type.id,
        version: `${faker.number.int({ min: 1, max: 3 })}.${faker.number.int({ min: 0, max: 9 })}.${faker.number.int({ min: 0, max: 9 })}`
      });
    }
  }

  return list;
};

// 零件列表数据
const _componentList = generateComponentList();

// 属性值类型
const attributeValueTypes = ["Int", "string", "bool", "array", "object"];

// 生成属性
const _generateAttributes = (count = faker.number.int({ min: 3, max: 8 })) => {
  const attributes = [];

  for (let i = 0; i < count; i++) {
    const valueType = attributeValueTypes[
      faker.number.int({ min: 0, max: 2 })
    ] as "Int" | "string" | "bool" | "array" | "object";
    let defaultValue: any;

    // 根据类型生成默认值
    switch (valueType) {
      case "Int":
        defaultValue = faker.number.int({ min: 0, max: 100 });
        break;
      case "string":
        defaultValue = faker.word.words({ count: { min: 1, max: 3 } });
        break;
      case "bool":
        defaultValue = faker.datatype.boolean();
        break;
      case "array":
        defaultValue = [];
        break;
      case "object":
        defaultValue = {};
        break;
    }

    // 生成规则
    const rules: any = {};
    if (valueType === "Int") {
      rules.min = faker.number.int({ min: 0, max: 10 });
      rules.max = faker.number.int({ min: 90, max: 200 });
      rules.decimalPlaces = faker.number.int({ min: 0, max: 2 });
    } else if (valueType === "string") {
      rules.length = faker.number.int({ min: 10, max: 100 });
      if (faker.datatype.boolean()) {
        rules.regex = "^[a-zA-Z0-9]+$";
      }
    }

    attributes.push({
      name: `attr${i + 1}`,
      displayName: `属性${i + 1}`,
      monitorInterval: faker.datatype.boolean()
        ? faker.number.int({ min: 100, max: 5000 })
        : -1,
      valueType,
      description: `这是${valueType}类型的属性${i + 1}`,
      unit:
        valueType === "Int"
          ? faker.helpers.arrayElement(["mm", "kg", "℃", "V", "A"])
          : "",
      defaultValue,
      rules
    });
  }

  return attributes;
};

// 生成子零件
const _generateSubcomponents = (
  attributes: any[],
  count = faker.number.int({ min: 1, max: 3 })
) => {
  const subcomponents = [];

  for (let i = 0; i < count; i++) {
    // 随机选择一个零件类型
    const type =
      componentTypes[
        faker.number.int({ min: 0, max: componentTypes.length - 1 })
      ];

    // 生成属性传递规则
    const attributeTransferRules = [];
    const attrCount = faker.number.int({
      min: 1,
      max: Math.min(3, attributes.length)
    });

    for (let j = 0; j < attrCount; j++) {
      const attr =
        attributes[faker.number.int({ min: 0, max: attributes.length - 1 })];

      const transferType = faker.helpers.arrayElement([
        "fixed",
        "inherit",
        "calculated"
      ]) as "fixed" | "inherit" | "calculated";

      const rule: any = {
        currentAttribute: attr.name,
        subAttribute: `sub_${attr.name}`,
        transferType
      };

      if (transferType === "fixed") {
        if (attr.valueType === "Int") {
          rule.fixedValue = faker.number.int({ min: 0, max: 100 });
        } else if (attr.valueType === "string") {
          rule.fixedValue = faker.word.words(2);
        } else if (attr.valueType === "bool") {
          rule.fixedValue = faker.datatype.boolean();
        }
      } else if (transferType === "calculated") {
        rule.calculationExpression = `${attr.name} * 2`;
      }

      attributeTransferRules.push(rule);
    }

    subcomponents.push({
      name: `子零件${i + 1}`,
      componentTypeName: type.name,
      componentTypeVersion: `${faker.number.int({ min: 1, max: 3 })}.${faker.number.int({ min: 0, max: 9 })}.${faker.number.int({ min: 0, max: 9 })}`,
      paramTransferRule: faker.helpers.arrayElement(["sameName", "custom"]),
      attributeTransferRules
    });
  }

  return subcomponents;
};

// 生成功能参数
const _generateFunctionParameters = (
  count = faker.number.int({ min: 1, max: 4 })
) => {
  const parameters = [];

  for (let i = 0; i < count; i++) {
    parameters.push({
      name: `param${i + 1}`,
      type: faker.helpers.arrayElement([
        "string",
        "number",
        "boolean",
        "object"
      ]),
      comment: `参数${i + 1}的说明`
    });
  }

  return parameters;
};

// 生成流程节点
const _generateProcessNodes = (
  count = faker.number.int({ min: 2, max: 5 })
) => {
  const nodes = [];

  for (let i = 0; i < count; i++) {
    const nodeId = `node${i + 1}`;
    let previousNodes = [];
    let nextNodes = [];

    if (i > 0) {
      previousNodes = [`node${i}`];
    }

    if (i < count - 1) {
      nextNodes = [`node${i + 2}`];
    }

    nodes.push({
      nodeId,
      nodeComment: `节点${i + 1}`,
      nodeBasicFunctionConfig: {
        type: faker.helpers.arrayElement([
          "start",
          "process",
          "decision",
          "end"
        ]),
        params: {}
      },
      previousNodes,
      nextNodes
    });
  }

  return nodes;
};

// 生成功能
const _generateFunctions = (count = faker.number.int({ min: 1, max: 4 })) => {
  const functions = [];

  for (let i = 0; i < count; i++) {
    functions.push({
      name: `function${i + 1}`,
      inputParameters: _generateFunctionParameters(),
      description: `功能${i + 1}的描述`,
      processes: _generateProcessNodes()
    });
  }

  return functions;
};

// 生成报警
const _generateAlarms = (count = faker.number.int({ min: 1, max: 3 })) => {
  const alarms = [];
  const alarmLevels = ["info", "warning", "error", "critical"];

  for (let i = 0; i < count; i++) {
    alarms.push({
      id: generateGuid(),
      name: `alarm${i + 1}`,
      description: `报警${i + 1}的描述`,
      level:
        alarmLevels[faker.number.int({ min: 0, max: alarmLevels.length - 1 })],

      availableHandlingMethods: ["自动处理", "手动确认", "忽略"].slice(
        0,
        faker.number.int({ min: 1, max: 3 })
      )
    });
  }

  return alarms;
};

// 生成渲染条件
const _generateRenderConditions = (
  attributes: any[],
  count = faker.number.int({ min: 1, max: 3 })
) => {
  const conditions = [];
  const conditionTypes = [
    "equal",
    "greater",
    "less",
    "notEqual",
    "contains",
    "notContains"
  ];

  for (let i = 0; i < count; i++) {
    if (attributes.length === 0) continue;

    const attribute =
      attributes[faker.number.int({ min: 0, max: attributes.length - 1 })];
    let value: any;

    // 根据属性类型生成条件值
    if (attribute.valueType === "Int") {
      value = faker.number.int({ min: 0, max: 100 });
    } else if (attribute.valueType === "string") {
      value = faker.word.words(2);
    } else if (attribute.valueType === "bool") {
      value = faker.datatype.boolean();
    }

    const condition: any = {
      attributeName: attribute.name,
      condition:
        conditionTypes[
          faker.number.int({ min: 0, max: conditionTypes.length - 1 })
        ],
      value,
      valueType:
        attribute.valueType === "Int"
          ? "number"
          : attribute.valueType === "bool"
            ? "boolean"
            : "string",
      renderResource: `resource${i + 1}.png`
    };

    // 有一定概率添加条件组
    if (faker.datatype.boolean() && attributes.length > 1) {
      const subAttribute =
        attributes.find(a => a.name !== attribute.name) || attributes[0];
      let subValue: any;

      if (subAttribute.valueType === "Int") {
        subValue = faker.number.int({ min: 0, max: 100 });
      } else if (subAttribute.valueType === "string") {
        subValue = faker.word.words(2);
      } else if (subAttribute.valueType === "bool") {
        subValue = faker.datatype.boolean();
      }

      condition.subConditions = [
        {
          attributeName: subAttribute.name,
          condition:
            conditionTypes[
              faker.number.int({ min: 0, max: conditionTypes.length - 1 })
            ],
          valueType:
            subAttribute.valueType === "Int"
              ? "number"
              : subAttribute.valueType === "bool"
                ? "boolean"
                : "string",
          value: subValue,
          type: faker.helpers.arrayElement(["and", "or"])
        }
      ];
    }

    conditions.push(condition);
  }

  return conditions;
};

// 生成完整的零件数据
const generateComponent = (id: string) => {
  const attributes = _generateAttributes();
  return {
    basicInfo: {
      guid: id,
      name: faker.lorem.words(3),
      version: `${faker.number.int({ min: 1, max: 3 })}.${faker.number.int({ min: 0, max: 9 })}.${faker.number.int({ min: 0, max: 9 })}`,
      versionDesc: faker.lorem.paragraph(1),
      description: faker.lorem.paragraph(1),
      tags: Array.from({ length: faker.number.int({ min: 1, max: 5 }) }, () =>
        faker.word.noun()
      ),
      createTime: _generateTimestamp(),
      author: faker.person.fullName()
    },
    attributes,
    subcomponents: _generateSubcomponents(attributes),
    alarms: _generateAlarms(),
    functions: _generateFunctions(),
    renderConditions: _generateRenderConditions(attributes)
  };
};

// 生成多个零件数据
const components: Record<string, any> = {};

// 为每个零件生成详细数据
_componentList.forEach(item => {
  components[item.id] = generateComponent(item.id);
});

// 导出 mock 接口
export default defineFakeRoute([
  // 获取零件列表
  {
    url: "/components",
    method: "get",
    response: () => {
      return {
        Success: true,
        Record: 0,
        Attach: _componentList
      };
    }
  },

  // 获取零件详情
  {
    url: "/components/:id",
    method: "get",
    response: (request: { url: string }) => {
      const id = request.url.split("/").pop() || "";

      if (components[id]) {
        return {
          Success: true,
          Record: 0,
          Attach: components[id]
        };
      }

      return {
        record: 404,
        attach: {},
        message: "零件不存在",
        success: false
      };
    }
  },

  // 创建零件
  {
    url: "/components",
    method: "post",
    response: (request: { body: any }) => {
      const id = generateGuid();
      const component = request.body.data || {};
      components[id] = generateComponent(id);

      // 合并用户提供的数据
      if (component.basicInfo) {
        components[id].basicInfo = {
          ...components[id].basicInfo,
          ...component.basicInfo,
          guid: id
        };
      }

      return {
        Success: true,
        Record: 0,
        Attach: { id }
      };
    }
  },

  // 更新零件
  {
    url: "/components/:id",
    method: "put",
    response: (request: { url: string; body: any }) => {
      const id = request.url.split("/").pop() || "";
      const component = request.body.data || {};

      if (components[id]) {
        // 更新基本信息
        if (component.basicInfo) {
          components[id].basicInfo = {
            ...components[id].basicInfo,
            ...component.basicInfo,
            guid: id // 保持ID不变
          };
        }
        return {
          Success: true,
          Record: 0,
          Attach: { success: true }
        };
      }

      return {
        code: 404,
        message: "零件不存在"
      };
    }
  },

  // 删除零件
  {
    url: "/components/:id",
    method: "delete",
    response: (request: { url: string }) => {
      const id = request.url.split("/").pop() || "";

      if (components[id]) {
        // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
        delete components[id];

        return {
          Success: true,
          Record: 0,
          Attach: {
            success: true
          }
        };
      }

      return {
        code: 404,
        message: "零件不存在"
      };
    }
  },

  // 添加属性
  {
    url: "/components/:id/attributes",
    method: "post",
    response: (request: { url: string; body: any }) => {
      const id = request.url.split("/")[3] || "";
      const attribute = request.body.data || {};

      if (components[id]) {
        // 检查属性名是否已存在
        const exists = components[id].attributes.some(
          (attr: any) => attr.name === attribute.name
        );

        if (exists) {
          return {
            code: 400,
            message: "属性名已存在"
          };
        }

        components[id].attributes.push(attribute);

        return {
          Success: true,
          Record: 0,
          Attach: { success: true }
        };
      }

      return {
        code: 404,
        message: "零件不存在"
      };
    }
  },

  // 更新属性
  {
    url: "/components/:id/attributes/:name",
    method: "put",
    response: (request: { url: string; body: any }) => {
      const parts = request.url.split("/");
      const id = parts[3] || "";
      const name = parts[5] || "";
      const attribute = request.body.data || {};

      if (components[id]) {
        const index = components[id].attributes.findIndex(
          (attr: any) => attr.name === name
        );

        if (index !== -1) {
          components[id].attributes[index] = {
            ...components[id].attributes[index],
            ...attribute,
            name // 保持属性名不变
          };

          return {
            Success: true,
            Record: 0,
            Attach: { success: true }
          };
        }

        return {
          code: 404,
          message: "属性不存在"
        };
      }

      return {
        code: 404,
        message: "零件不存在"
      };
    }
  },

  // 删除属性
  {
    url: "/components/:id/attributes/:name",
    method: "delete",
    response: (request: { url: string }) => {
      const parts = request.url.split("/");
      const id = parts[3] || "";
      const name = parts[5] || "";

      if (components[id]) {
        const index = components[id].attributes.findIndex(
          (attr: any) => attr.name === name
        );

        if (index !== -1) {
          components[id].attributes.splice(index, 1);
          return {
            code: 200,
            data: { success: true }
          };
        }

        return {
          code: 404,
          message: "属性不存在"
        };
      }

      return {
        code: 404,
        message: "零件不存在"
      };
    }
  },

  // 添加子零件
  {
    url: "/components/:id/subcomponents",
    method: "post",
    response: (request: { url: string; body: any }) => {
      const id = request.url.split("/")[3] || "";
      const subcomponent = request.body.data || {};

      if (components[id]) {
        // 检查子零件名是否已存在
        const exists = components[id].subcomponents.some(
          (sub: any) => sub.name === subcomponent.name
        );

        if (exists) {
          return {
            code: 400,
            message: "子零件名已存在"
          };
        }

        components[id].subcomponents.push(subcomponent);

        return {
          Success: true,
          Record: 0,
          Attach: { success: true }
        };
      }

      return {
        code: 404,
        message: "零件不存在"
      };
    }
  },

  // 更新子零件
  {
    url: "/components/:id/subcomponents/:name",
    method: "put",
    response: (request: { url: string; body: any }) => {
      const parts = request.url.split("/");
      const id = parts[3] || "";
      const name = parts[5] || "";
      const subcomponent = request.body.data || {};

      if (components[id]) {
        const index = components[id].subcomponents.findIndex(
          (sub: any) => sub.name === name
        );

        if (index !== -1) {
          components[id].subcomponents[index] = {
            ...components[id].subcomponents[index],
            ...subcomponent,
            name // 保持子零件名不变
          };

          return {
            Success: true,
            Record: 0,
            Attach: { success: true }
          };
        }

        return {
          code: 404,
          message: "子零件不存在"
        };
      }

      return {
        code: 404,
        message: "零件不存在"
      };
    }
  },

  // 删除子零件
  {
    url: "/components/:id/subcomponents/:name",
    method: "delete",
    response: (request: { url: string }) => {
      const parts = request.url.split("/");
      const id = parts[3] || "";
      const name = parts[5] || "";

      if (components[id]) {
        const index = components[id].subcomponents.findIndex(
          (sub: any) => sub.name === name
        );

        if (index !== -1) {
          components[id].subcomponents.splice(index, 1);

          return {
            Success: true,
            Record: 0,
            Attach: { success: true }
          };
        }

        return {
          code: 404,
          message: "子零件不存在"
        };
      }

      return {
        code: 404,
        message: "零件不存在"
      };
    }
  },

  // 添加功能
  {
    url: "/components/:id/functions",
    method: "post",
    response: (request: { url: string; body: any }) => {
      const id = request.url.split("/")[3] || "";
      const func = request.body.data || {};

      if (components[id]) {
        // 检查功能名是否已存在
        const exists = components[id].functions.some(
          (f: any) => f.name === func.name
        );

        if (exists) {
          return {
            code: 400,
            message: "功能名已存在"
          };
        }

        components[id].functions.push(func);
        return {
          Success: true,
          Record: 0,
          Attach: { success: true }
        };
      }

      return {
        code: 404,
        message: "零件不存在"
      };
    }
  },

  // 更新功能
  {
    url: "/components/:id/functions/:name",
    method: "put",
    response: (request: { url: string; body: any }) => {
      const parts = request.url.split("/");
      const id = parts[3] || "";
      const name = parts[5] || "";
      const func = request.body.data || {};

      if (components[id]) {
        const index = components[id].functions.findIndex(
          (f: any) => f.name === name
        );

        if (index !== -1) {
          components[id].functions[index] = {
            ...components[id].functions[index],
            ...func,
            name // 保持功能名不变
          };

          return {
            Success: true,
            Record: 0,
            Attach: { success: true }
          };
        }

        return {
          code: 404,
          message: "功能不存在"
        };
      }

      return {
        code: 404,
        message: "零件不存在"
      };
    }
  },

  // 删除功能
  {
    url: "/components/:id/functions/:name",
    method: "delete",
    response: (request: { url: string }) => {
      const parts = request.url.split("/");
      const id = parts[3] || "";
      const name = parts[5] || "";

      if (components[id]) {
        const index = components[id].functions.findIndex(
          (f: any) => f.name === name
        );

        if (index !== -1) {
          components[id].functions.splice(index, 1);
          return {
            Success: true,
            Record: 0,
            Attach: { success: true }
          };
        }

        return {
          code: 404,
          message: "功能不存在"
        };
      }

      return {
        code: 404,
        message: "零件不存在"
      };
    }
  },

  // 添加报警
  {
    url: "/components/:id/alarms",
    method: "post",
    response: (request: { url: string; body: any }) => {
      const id = request.url.split("/")[3] || "";
      const alarm = request.body.data || {};

      if (components[id]) {
        // 检查报警ID是否已存在
        const exists = components[id].alarms.some(
          (a: any) => a.id === alarm.id
        );

        if (exists) {
          return {
            code: 400,
            message: "报警ID已存在"
          };
        }

        components[id].alarms.push(alarm);
        return {
          Success: true,
          Record: 0,
          Attach: { success: true }
        };
      }

      return {
        code: 404,
        message: "零件不存在"
      };
    }
  },

  // 更新报警
  {
    url: "/components/:id/alarms/:alarmId",
    method: "put",
    response: (request: { url: string; body: any }) => {
      const parts = request.url.split("/");
      const id = parts[3] || "";
      const alarmId = parts[5] || "";
      const alarm = request.body.data || {};

      if (components[id]) {
        const index = components[id].alarms.findIndex(
          (a: any) => a.id === alarmId
        );

        if (index !== -1) {
          components[id].alarms[index] = {
            ...components[id].alarms[index],
            ...alarm,
            id: alarmId // 保持报警ID不变
          };

          return {
            Success: true,
            Record: 0,
            Attach: { success: true }
          };
        }

        return {
          code: 404,
          message: "报警不存在"
        };
      }

      return {
        code: 404,
        message: "零件不存在"
      };
    }
  },

  // 删除报警
  {
    url: "/components/:id/alarms/:alarmId",
    method: "delete",
    response: (request: { url: string }) => {
      const parts = request.url.split("/");
      const id = parts[3] || "";
      const alarmId = parts[5] || "";

      if (components[id]) {
        const index = components[id].alarms.findIndex(
          (a: any) => a.id === alarmId
        );

        if (index !== -1) {
          components[id].alarms.splice(index, 1);
          return {
            Success: true,
            Record: 0,
            Attach: { success: true }
          };
        }

        return {
          code: 404,
          message: "报警不存在"
        };
      }

      return {
        code: 404,
        message: "零件不存在"
      };
    }
  },

  // 更新渲染条件
  {
    url: "/components/:id/renderConditions",
    method: "put",
    response: (request: { url: string; body: any }) => {
      const id = request.url.split("/")[3] || "";
      const renderConditions = request.body.data || [];

      if (components[id]) {
        components[id].renderConditions = renderConditions;
        return {
          Success: true,
          Record: 0,
          Attach: { success: true }
        };
      }

      return {
        code: 404,
        message: "零件不存在"
      };
    }
  }
]);
