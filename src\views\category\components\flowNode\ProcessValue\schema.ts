import type { Api } from "@form-create/element-ui";

// 处理类型选项
const PROCESS_TYPES = [
  { label: "数学运算", value: "math" },
  { label: "字符串操作", value: "string" },
  { label: "类型转换", value: "typecast" },
  { label: "逻辑运算", value: "logic" },
  { label: "条件处理", value: "condition" },
  { label: "日期处理", value: "date" }
];

// 数学运算选项
const MATH_OPERATIONS = [
  { label: "加法", value: "add" },
  { label: "减法", value: "subtract" },
  { label: "乘法", value: "multiply" },
  { label: "除法", value: "divide" },
  { label: "取余", value: "modulo" },
  { label: "幂运算", value: "power" },
  { label: "绝对值", value: "abs" },
  { label: "四舍五入", value: "round" },
  { label: "向上取整", value: "ceil" },
  { label: "向下取整", value: "floor" },
  { label: "平方根", value: "sqrt" }
];

// 字符串操作选项
const STRING_OPERATIONS = [
  { label: "字符串拼接", value: "concat" },
  { label: "字符串截取", value: "substring" },
  { label: "字符串替换", value: "replace" },
  { label: "转大写", value: "uppercase" },
  { label: "转小写", value: "lowercase" },
  { label: "去除空格", value: "trim" },
  { label: "获取长度", value: "length" },
  { label: "分割字符串", value: "split" },
  { label: "查找位置", value: "indexOf" }
];

// 类型转换选项
const TYPECAST_OPERATIONS = [
  { label: "转为数字", value: "toNumber" },
  { label: "转为字符串", value: "toString" },
  { label: "转为布尔值", value: "toBoolean" },
  { label: "转为JSON", value: "toJSON" },
  { label: "解析JSON", value: "parseJSON" }
];

// 逻辑运算选项
const LOGIC_OPERATIONS = [
  { label: "逻辑与", value: "and" },
  { label: "逻辑或", value: "or" },
  { label: "逻辑非", value: "not" },
  { label: "等于", value: "equal" },
  { label: "不等于", value: "notEqual" },
  { label: "大于", value: "greater" },
  { label: "小于", value: "less" },
  { label: "大于等于", value: "greaterOrEqual" },
  { label: "小于等于", value: "lessOrEqual" }
];

// 条件处理选项
const CONDITION_OPERATIONS = [
  { label: "三元运算", value: "ternary" },
  { label: "空值合并", value: "nullish" },
  { label: "默认值", value: "default" }
];

// 日期处理选项
const DATE_OPERATIONS = [
  { label: "格式化日期", value: "format" },
  { label: "获取年份", value: "getYear" },
  { label: "获取月份", value: "getMonth" },
  { label: "获取日期", value: "getDate" },
  { label: "获取时间戳", value: "getTimestamp" },
  { label: "日期加法", value: "addDate" },
  { label: "日期减法", value: "subtractDate" }
];

/**
 * 根据处理类型获取对应的操作选项
 */
const getOperationsByType = (processType: string) => {
  switch (processType) {
    case "math":
      return MATH_OPERATIONS;
    case "string":
      return STRING_OPERATIONS;
    case "typecast":
      return TYPECAST_OPERATIONS;
    case "logic":
      return LOGIC_OPERATIONS;
    case "condition":
      return CONDITION_OPERATIONS;
    case "date":
      return DATE_OPERATIONS;
    default:
      return [];
  }
};

/**
 * 生成操作参数表单项
 */
const generateOperationParams = (processType: string, operation: string) => {
  const params: any[] = [];

  // 数学运算参数
  if (processType === "math") {
    if (
      ["add", "subtract", "multiply", "divide", "modulo", "power"].includes(
        operation
      )
    ) {
      params.push({
        type: "input",
        field: "operand",
        title: "操作数",
        value: "",
        valueType: 2,
        props: {
          placeholder: "请输入数值",
          type: "number"
        },
        validate: [{ required: true, message: "请输入操作数" }],
        col: { span: 24 }
      });
    }
    if (operation === "round") {
      params.push({
        type: "input",
        field: "precision",
        title: "精度",
        value: 0,
        valueType: 1,
        props: {
          placeholder: "小数位数",
          type: "number"
        },
        col: { span: 24 }
      });
    }
  }

  // 字符串操作参数
  if (processType === "string") {
    if (operation === "concat") {
      params.push({
        type: "input",
        field: "concatValue",
        title: "拼接值",
        value: "",
        valueType: 3,
        props: {
          placeholder: "请输入要拼接的字符串"
        },
        validate: [{ required: true, message: "请输入拼接值" }],
        col: { span: 24 }
      });
    }
    if (operation === "substring") {
      params.push(
        {
          type: "input",
          field: "startIndex",
          title: "起始位置",
          value: 0,
          valueType: 1,
          props: {
            placeholder: "起始索引",
            type: "number"
          },
          validate: [{ required: true, message: "请输入起始位置" }],
          col: { span: 12 }
        },
        {
          type: "input",
          field: "length",
          title: "截取长度",
          value: "",
          valueType: 1,
          props: {
            placeholder: "截取长度（可选）",
            type: "number"
          },
          col: { span: 12 }
        }
      );
    }
    if (operation === "replace") {
      params.push(
        {
          type: "input",
          field: "searchValue",
          title: "查找值",
          value: "",
          valueType: 3,
          props: {
            placeholder: "要替换的字符串"
          },
          validate: [{ required: true, message: "请输入查找值" }],
          col: { span: 12 }
        },
        {
          type: "input",
          field: "replaceValue",
          title: "替换值",
          value: "",
          valueType: 3,
          props: {
            placeholder: "替换为的字符串"
          },
          validate: [{ required: true, message: "请输入替换值" }],
          col: { span: 12 }
        }
      );
    }
    if (operation === "split") {
      params.push({
        type: "input",
        field: "separator",
        title: "分隔符",
        value: "",
        valueType: 3,
        props: {
          placeholder: "分隔符（如逗号、空格等）"
        },
        validate: [{ required: true, message: "请输入分隔符" }],
        col: { span: 24 }
      });
    }
    if (operation === "indexOf") {
      params.push({
        type: "input",
        field: "searchValue",
        title: "查找值",
        value: "",
        valueType: 3,
        props: {
          placeholder: "要查找的字符串"
        },
        validate: [{ required: true, message: "请输入查找值" }],
        col: { span: 24 }
      });
    }
  }

  // 逻辑运算参数
  if (processType === "logic") {
    if (!["not"].includes(operation)) {
      params.push({
        type: "input",
        field: "compareValue",
        title: "比较值",
        value: "",
        valueType: 0,
        props: {
          placeholder: "请输入比较值"
        },
        validate: [{ required: true, message: "请输入比较值" }],
        col: { span: 24 }
      });
    }
  }

  // 条件处理参数
  if (processType === "condition") {
    if (operation === "ternary") {
      params.push(
        {
          type: "input",
          field: "condition",
          title: "条件表达式",
          value: "",
          valueType: 3,
          props: {
            placeholder: "条件表达式"
          },
          validate: [{ required: true, message: "请输入条件表达式" }],
          col: { span: 24 }
        },
        {
          type: "input",
          field: "trueValue",
          title: "真值",
          value: "",
          valueType: 0,
          props: {
            placeholder: "条件为真时的值"
          },
          validate: [{ required: true, message: "请输入真值" }],
          col: { span: 12 }
        },
        {
          type: "input",
          field: "falseValue",
          title: "假值",
          value: "",
          valueType: 0,
          props: {
            placeholder: "条件为假时的值"
          },
          validate: [{ required: true, message: "请输入假值" }],
          col: { span: 12 }
        }
      );
    }
    if (["nullish", "default"].includes(operation)) {
      params.push({
        type: "input",
        field: "defaultValue",
        title: "默认值",
        value: "",
        valueType: 0,
        props: {
          placeholder: "默认值"
        },
        validate: [{ required: true, message: "请输入默认值" }],
        col: { span: 24 }
      });
    }
  }

  // 日期处理参数
  if (processType === "date") {
    if (operation === "format") {
      params.push({
        type: "input",
        field: "format",
        title: "格式模板",
        value: "YYYY-MM-DD HH:mm:ss",
        valueType: 3,
        props: {
          placeholder: "日期格式（如：YYYY-MM-DD HH:mm:ss）"
        },
        validate: [{ required: true, message: "请输入格式模板" }],
        col: { span: 24 }
      });
    }
    if (["addDate", "subtractDate"].includes(operation)) {
      params.push(
        {
          type: "input",
          field: "amount",
          title: "数量",
          value: "",
          valueType: 1,
          props: {
            placeholder: "增减数量",
            type: "number"
          },
          validate: [{ required: true, message: "请输入数量" }],
          col: { span: 12 }
        },
        {
          type: "select",
          field: "unit",
          title: "单位",
          value: "days",
          valueType: 3,
          props: {
            placeholder: "时间单位"
          },
          options: [
            { label: "天", value: "days" },
            { label: "小时", value: "hours" },
            { label: "分钟", value: "minutes" },
            { label: "秒", value: "seconds" },
            { label: "月", value: "months" },
            { label: "年", value: "years" }
          ],
          col: { span: 12 }
        }
      );
    }
  }

  return params;
};

export default [
  {
    type: "select",
    field: "processType",
    title: "处理类型",
    value: "",
    valueType: 3,
    props: {
      placeholder: "选择处理类型"
    },
    options: PROCESS_TYPES,
    validate: [{ required: true, message: "请选择处理类型" }],
    col: { span: 24 },
    inject: true,
    on: {
      change(inject: Api, value: any) {
        const { updateRule, setValue, removeField } = inject.$f;

        // 清空操作选择
        setValue("operation", "");

        // 清除之前的参数字段
        const formData = inject.$f.form;
        Object.keys(formData).forEach(key => {
          if (
            key.startsWith("operand") ||
            key.startsWith("precision") ||
            key.startsWith("concat") ||
            key.startsWith("start") ||
            key.startsWith("length") ||
            key.startsWith("search") ||
            key.startsWith("replace") ||
            key.startsWith("separator") ||
            key.startsWith("compare") ||
            key.startsWith("condition") ||
            key.startsWith("true") ||
            key.startsWith("false") ||
            key.startsWith("default") ||
            key.startsWith("format") ||
            key.startsWith("amount") ||
            key.startsWith("unit")
          ) {
            removeField(key);
          }
        });

        // 更新操作选项
        const operations = getOperationsByType(value);
        updateRule("operation", { options: operations });
      }
    }
  },
  {
    type: "select",
    field: "operation",
    title: "操作",
    value: "",
    valueType: 3,
    props: {
      placeholder: "选择具体操作"
    },
    options: [],
    validate: [{ required: true, message: "请选择操作" }],
    col: { span: 24 },
    inject: true,
    on: {
      change(inject: Api, value: any) {
        const { removeField, append, refresh } = inject.$f;
        const formData = inject.$f.form;

        // 清除之前的参数字段
        Object.keys(formData).forEach(key => {
          if (
            key.startsWith("operand") ||
            key.startsWith("precision") ||
            key.startsWith("concat") ||
            key.startsWith("start") ||
            key.startsWith("length") ||
            key.startsWith("search") ||
            key.startsWith("replace") ||
            key.startsWith("separator") ||
            key.startsWith("compare") ||
            key.startsWith("condition") ||
            key.startsWith("true") ||
            key.startsWith("false") ||
            key.startsWith("default") ||
            key.startsWith("format") ||
            key.startsWith("amount") ||
            key.startsWith("unit")
          ) {
            removeField(key);
          }
        });

        // 根据选择的操作生成参数表单
        const processType = formData.processType;
        if (processType && value) {
          const params = generateOperationParams(processType, value);
          params.forEach(param => {
            append(param);
          });
          refresh();
        }
      }
    }
  }
];
