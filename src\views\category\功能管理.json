{"screen_name": "流程配置界面 (Workflow Configuration Interface)", "description": "该界面用于创建和配置自动化流程。用户可以通过拖拽左侧的功能模块到中间画布来构建流程，并在右侧配置选中模块的具体参数。", "layout": {"type": "vertical_stack", "elements": [{"area": "顶部信息栏 (Top Information Bar)", "layout": "horizontal_flow", "elements": [{"type": "label", "label_text": "名称 (Name)"}, {"type": "input_field", "placeholder": "请输入内容 (Please enter content)", "identifier": "workflow_name_input", "purpose": "输入流程的名称"}, {"type": "label", "label_text": "描述 (Description)"}, {"type": "input_field", "placeholder": "请输入内容 (Please enter content)", "identifier": "workflow_description_input", "purpose": "输入流程的描述信息"}, {"type": "button", "label_text": "保存 (Save)", "style": "primary", "identifier": "save_button", "action": "保存当前流程配置"}]}, {"area": "流程配置主体区域 (Workflow Configuration Main Area)", "title": "流程配置 (Workflow Configuration)", "layout": "horizontal_split", "panels": [{"panel_id": "left_toolbox_panel", "width_ratio": "narrow", "title": "通用功能 (Common Functions)", "description": "提供可用于构建流程的功能模块列表，用户可拖拽至中间画布。", "elements": [{"type": "draggable_node", "label": "开始 (Start)", "status": "selected_or_focused"}, {"type": "draggable_node", "label": "结束 (End)"}, {"type": "draggable_node", "label": "循环 (Loop)"}, {"type": "draggable_node", "label": "报警 (Alarm)"}, {"type": "draggable_node", "label": "零件控制 (Component Control)"}, {"type": "draggable_node", "label": "写值 (Write Value)"}, {"type": "draggable_node", "label": "读值 (Read Value)"}, {"type": "draggable_node", "label": "监听属性 (Listen Attribute)"}]}, {"panel_id": "center_canvas_panel", "width_ratio": "wide", "description": "流程设计画布，用于可视化地编排和连接流程节点。", "elements": [{"type": "workflow_diagram", "nodes": [{"id": "node_start", "label": "开始 (Start)"}, {"id": "node_read", "label": "读值 (Read Value)"}, {"id": "node_control", "label": "零件控制 (Component Control)"}, {"id": "node_write", "label": "写值 (Write Value)"}, {"id": "node_end", "label": "结束 (End)"}], "connections": [{"from": "node_start", "to": "node_read"}, {"from": "node_read", "to": "node_control"}, {"from": "node_control", "to": "node_write"}, {"from": "node_write", "to": "node_end"}], "interaction": "用户在此区域放置、连接和选择节点进行配置。"}]}, {"panel_id": "right_config_panel", "width_ratio": "narrow", "description": "显示当前在画布中选中的节点的配置选项。", "elements": [{"type": "dropdown", "label": "零件 (Component)", "identifier": "component_selector", "default_value": "零件 1 (Component 1)", "options": ["零件 1", "..."], "purpose": "选择要操作的零件"}, {"type": "dropdown", "label": "功能 (Function)", "identifier": "function_selector", "default_value": "功能 1 (Function 1)", "options": ["功能 1", "..."], "purpose": "选择零件的具体功能"}, {"type": "label_input_group", "label": "参数名称1: (Parameter Name 1:)", "input_field": {"type": "input_field", "placeholder": "参数值 (Parameter Value)", "identifier": "parameter_1_value", "purpose": "输入参数1的值"}}, {"type": "label_input_group", "label": "参数名称2: (Parameter Name 2:)", "input_field": {"type": "input_field", "placeholder": "参数值 (Parameter Value)", "identifier": "parameter_2_value", "purpose": "输入参数2的值"}}, {"type": "label_input_group", "label": "参数名称3: (Parameter Name 3:)", "input_field": {"type": "input_field", "placeholder": "参数值 (Parameter Value)", "identifier": "parameter_3_value", "purpose": "输入参数3的值"}}, {"type": "info_box", "style": "warning/info (yellow background)", "content": ["1. 选择零件-选择功能-配置参数值 (1. Select component - Select function - Configure parameter values)", "2. 通过功能自动生成参数列表 (2. Parameter list is automatically generated based on the selected function)"], "purpose": "提供配置参数的操作指引和说明"}], "dynamic_behavior": "该面板的内容和参数列表会根据中间画布选中的节点类型以及上方下拉框的选择动态变化。"}]}]}, "overall_functionality": "允许用户定义一个包含多个步骤（如读值、零件控制、写值）的自动化工作流程，并为每个步骤配置详细参数。"}