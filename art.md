# The Slow Collapse of Critical Thinking in OSINT due to AI

_OSINT used to be a thinking game. Now it's becoming a trusting game and that should terrify you._

I've seen it firsthand, analysts running solid investigations, then slowly shifting more and more of the thinking to GenAI tools. At first, it's small. You use ChatGPT to summarise a document or translate a foreign post. Then it's helping draft your reports. Then it's generating leads. And eventually, you're not thinking as critically as you used to. You're verifying less, questioning less, relying more.

We tell ourselves we're "working smarter." But somewhere along the way, we stop noticing how much of the actual thinking is being offloaded.

This isn't a rant against AI. I use it daily, ChatGPT, Copilot, <PERSON>, Gemini. They're in my workflow like everyone else's. But the tradecraft is slipping. Analysts are skipping the hard parts. They're trusting GenAI to do the heavy cognitive lifting, and it's changing how we operate at a foundational level.

When OSINT becomes too easy, too efficient, too comfortable… you should be worried. Tradecraft isn't just about speed, it's about judgment. And judgment doesn't come from a language model. If we keep going down this path without pushing back, without actively preserving the critical habits that define our profession, we risk becoming operators of automation instead of investigators.

This blog is a wake-up call. For myself. For anyone working in OSINT. For the people teaching it, and the people just getting started. If we don't reclaim the thinking side of this game, we'll lose the game entirely.

## The Study That Should Alarm You

In early 2025, a team from Carnegie Mellon and Microsoft Research published something every OSINT analyst should read, but most haven't. It was a large-scale survey of 319 knowledge workers using GenAI tools like ChatGPT, Copilot, Claude, and Gemini in their daily work.

What they found is a warning shot.

The study revealed a clear pattern: the more confidence users had in the AI, the less they thought critically. In contrast, the more confident they were in themselves, the more likely they were to question the output, verify the information, and think deeply about the task.

Let that sink in: Confidence in AI replaces confidence in self and with it, the thinking disappears.

Here's the punchline:

High trust in GenAI consistently led to reduced critical thinking and less cognitive effort across the board.

Participants weren't lazy. They were experienced professionals. But when the tool responded quickly, confidently, and clearly they stopped doing the hard part. They stopped questioning. Stopped verifying. Stopped applying the mental friction that separates automation from investigation.

The scary part is that many users still believed they were thinking critically, because GenAI made them feel smart.

The researchers saw a new type of behavior emerge:

• Instead of forming hypotheses, users asked the AI for ideas.

• Instead of validating sources, they assumed the AI had already done so.

• Instead of assessing multiple perspectives, they integrated and edited the AI's summary and moved on.

This isn't hypothetical. This is happening now, in real-world workflows. And if you're in OSINT, you know how dangerous that is.

In our line of work, you can't afford false confidence. You can't afford a hallucinated source, a mistranslated post, or a manipulated summary. But the more trust you place in GenAI, without friction, without skepticism, the more you risk exactly that.

The study didn't focus on OSINT directly. But it doesn't have to. The findings hit home harder here than anywhere else. Because if we lose critical thinking in this field, we don't just lose accuracy, we lose integrity.

## What This Means for OSINT

In OSINT, we deal in fragments. Nothing is handed to us neatly. We build context from chaos: tweets, photos, forums, leaks, metadata, satellite images, dead links, weird file names. Every good analyst knows that the work isn't just collecting data. It's thinking with it.

That's what's at risk.

The Lee et al. study wasn't about OSINT specifically, but it described exactly what's happening in OSINT shops, government teams, threat intel units, and open-source communities around the world. The creeping shift from thinking to prompting, from analyst to editor. Let's talk real.

### Real-World OSINT Scenarios Affected by GenAI Complacency

Scenario 1: Image Verification

You upload a protest photo into a tool like Gemini and ask, "Where was this taken?" It spits out a convincing response: "Paris, near Place de la République." It sounds right. You move on.

But a trained eye would notice the signage is Belgian. The license plates are off. The architecture doesn't match. You trusted the AI and missed the location by a country.

Scenario 2: Person of Interest Profile

You use Claude to summarize a person's online presence. It generates a clean narrative: activist, tech worker, harmless. But it completely omits their links to far-right forums because the model didn't surface the fringe platforms. You never check. That person ends up speaking at a sensitive public event.

Scenario 3: Disinformation Campaign Detection

You feed a stream of Telegram messages into ChatGPT and ask for "summary and patterns." It flags some keywords, but misses the subtle linguistic shift that points to a known Russian influence cell, something only a trained mind would notice by comparing phrasing across sources. But you've stopped reading the raw content. You trusted the summary

These aren't edge cases. These are plausible daily failures in modern OSINT workflows.

And here's the kicker: in each case, the analyst didn't fail because of bad intent or laziness. They failed because the tools were just good enough to feel trustworthy and just wrong enough to be dangerous.

AI doesn't break OSINT. But unquestioned AI does.

When analysts become dependent on outputs instead of building their own reasoning, they lose what makes OSINT powerful: the ability to interpret, interrogate, and pivot. You can't pivot from a hallucinated answer. You can't investigate a lie you believed too quickly.

GenAI doesn't understand context, risk, geopolitical nuance, or how bad actors use language to hide intent. It doesn't know when to doubt itself. That's your job and too many are forgetting that.

## The Creeping Death of Tradecraft

Tradecraft isn't just a list of tools. It's a way of thinking. It's the habit of looking again when something feels off. It's verifying metadata, cross-checking timestamps, spotting a street sign that doesn't match the language in the caption. It's the instinct to question the obvious.

And that instinct is quietly dying.

Not because analysts are getting lazy, but because AI is making the job feel easier than it actually is. You're still working. You're still clicking. But the mental friction is gone.

That friction used to be where tradecraft lived.

Let's get brutally honest about what's happening:

### Then vs. Now: What OSINT Analysts Used to Do

Then:

• Saw a blurry image, opened it in three tools, zoomed in, rotated it, looked for EXIF, cropped landmarks, and reverse searched five times.

• Read a social post in broken Russian, translated it manually, checked slang, looked up associated hashtags, and verified the account's activity history.

• Traced a domain name through WHOIS, looked at subdomains, searched for reused infrastructure, and mapped out connected email addresses.

Now:

• Paste the image into the AI tool, read the suggested location, and move on.

• Dump a thread into ChatGPT for summary.

• Ask Gemini, "Who runs this domain?" and accept the top-line answer.

This isn't about nostalgia, it's about recognising a dangerous shift in behavior. The more we "trust the tool," the less we build the skills that make the tool useful. We're automating our edge away.

And GenAI isn't just fast, it's persuasive. It writes with confidence. It fills in gaps. It doesn't hesitate, and that creates a dangerous illusion of accuracy. Analysts are making decisions based on language model confidence, not evidence. It's happening slowly. Silently. Like rot.

#### What Dies When Tradecraft Goes Passive?

• Contextual reasoning: spotting when something doesn't add up, even if it "looks right."

• Cross-source verification: confirming a fact with at least two or three unrelated sources.

• Hypothesis testing: building and breaking possible explanations for what you're seeing.

• Refusal to settle: the instinct to keep digging, even when the AI gave you a plausible answer.

Without these, OSINT becomes automated guesswork with a shiny UI.

And make no mistake, bad actors know this. They'll test your tools. Feed them poisoned content. Exploit AI's tendency to repeat, simplify, and hallucinate. If your entire workflow is built on trust in the machine, you're walking into a trap.

Tradecraft is slow. Tradecraft is uncomfortable. Tradecraft is what keeps your work accurate, defensible, and trusted. Without it, you're just another person typing prompts and hoping for truth.

### The New Role of the Analyst: AI Overseer, Not AI Believer

Here's the truth: GenAI is here to stay. It's not going away. And for OSINT, it's not the enemy. But it is a liability, if you don't treat it like one.

The analyst's job has changed. Or rather, it needs to.

You're no longer just a researcher, a data miner, a pattern spotter.

You're now an AI overseer. A challenger. A verifier. A filter.

If you treat ChatGPT, Claude, Gemini, or Copilot as reliable assistants, they'll eventually lead you to errors, because they're not assistants. They're high-speed, high-confidence content engines with zero lived experience and no sense of consequence. Your role is to make sure they don't get away with anything.

How the Analyst Mindset Must Shift:

|                     |                               |
| ------------------- | ----------------------------- |
| **Old Role**        | **New Role**                  |
| Ask AI a question   | Interrogate AI answers        |
| Accept summaries    | Dissect summaries             |
| Use suggestions     | Break suggestions apart       |
| Trust clean answers | Trace dirty origins           |
| Generate profiles   | Validate narratives           |
| Draft and ship      | Draft, rip apart, and rebuild |

You're not there to be impressed by what the model says. You're there to break it, test it, and decide what survives. That means:

• Running the AI's claim through manual OSINT methods

• Fact-checking what it didn't cite

• Comparing AI output against real-world source behavior

• Asking "What isn't it telling me?"

AI tools should trigger suspicion, not satisfaction. Every time the answer seems too clean, too simple, too aligned with your bias, you should feel that OSINT tingle in your brain: "Wait… prove it."

You wouldn't blindly trust a witness in an investigation just because they speak confidently. Don't trust a model either. It's not about being anti-AI. It's about preserving cognitive sovereignty. Because the moment you let the model do the thinking for you, you stop being an investigator, you become an operator.

## Reviving Critical Thinking in the AI Era

If critical thinking is dying, it's not because we don't care, it's because we stopped practicing it. The good news? You can take it back. But it won't happen by accident. It requires intention.

Here's how OSINT practitioners can stay sharp in a GenAI world:

**Introduce Friction on Purpose**

GenAI is fast. That's the trap.

You need to slow yourself down (_deliberately_) before trusting anything it gives you.

**Tactics:**

• Pause and ask: _What sources would I have checked without AI?_ Go check them anyway.

• Require yourself to find **one contradiction** to the AI's output before accepting it.

• Use a second model (Claude, Gemini, etc.) and force a contradiction: _"Give me the opposite interpretation."_

**Rebuild Your Source Discipline**

GenAI doesn't cite like an OSINT analyst does. So don't let it train you into laziness.

**Tactics:**

• If a model gives you a name, quote, link, or claim, don't just Google it. Trace it.

• Keep a side-by-side log: AI output vs. verified source. Where's the gap?

• When using summaries, always open the original material anyway. Always.

**Use AI as a Thought Partner, Not an Oracle**

Treat GenAI like a junior analyst: decent ideas, but needs supervision.

**Tactics:**

• Ask it to argue against your current hypothesis.

• Feed it your working notes and ask, "What's missing? What assumptions am I making?"

• Use it to simulate perspectives, not to define reality.

**Cross-Model Interrogation**

Different models have different blind spots. Use that.

**Tactics:**

• Ask the same question across ChatGPT, Claude, Gemini, and Copilot. Compare outputs.

• Note contradictions. Investigate _why_ they differ.

• Treat divergence as signal, not noise.

**Force Failure**

If you're not actively trying to break the model, you're not using it critically.

**Tactics:**

• Intentionally feed it misleading prompts and watch what it hallucinates.

• Track how it behaves under ambiguity, contradiction, or incomplete data.

• Learn its failure modes and build your tradecraft to fill the gaps.

**Keep Doing the Hard Stuff**

The tools should speed you up, but they should never _replace_ the hard parts.

**Tactics:**

• Geolocate manually before checking with AI.

• Write your own summary before reading the AI's.

• Build your own profile first, then ask the AI to challenge it.

## The Quiet Collapse and How We Fight It

The fall of critical thinking in OSINT won't come with a bang. It'll come quietly. It'll look like faster reports. Cleaner narratives. Fewer questions asked. It'll feel efficient. It'll look like progress.

Until it isn't.

Until you miss the real location. Trust the wrong source. Assume the wrong intent. Attribute the wrong actor. And by then, your tradecraft won't save you, because you won't have practiced it.

This is how it starts. It starts with trusting summaries. With accepting citations you didn't check. With replacing your judgment with something that _sounds_ like judgment. The collapse won't be obvious. It will feel convenient. That's what makes it so dangerous. But here's the part that matters: it's reversible.

You don't need to ditch GenAI. You need to confront it. Challenge it. Break it. Question it. Use it, but never trust it without a fight. You're not just a user of tools. You're an investigator.

You think critically. You trace evidence. You challenge assumptions. That's the job.

Don't let the machine do the thinking for you.

**Bonus: OSINT Anti-Overreliance Checklist**

Keep this near your screen. Use it when GenAI enters your workflow.

✅ **Did I verify osint-vs-ai the original source of any AI output?**

✅ **Did I consult non-AI sources before accepting the answer?**

✅ **Did I challenge the output with a counter-hypothesis or alternate model?**

✅ **Did I cross-reference data across at least two human-curated sources?**

✅ **Did I perform at least one task manually before accepting the AI's version?**

✅ **Did I identify any unstated assumptions in the AI's output?**

✅ **Did I treat GenAI as a thought partner—not a source of truth?**

✅ **Did I deliberately introduce friction into the process (slowing down, comparing, double-checking)?**

✅ **Did I stop and ask: What am I trusting without verifying?**

✅ **Did we share/cite with the reader of our OSINT product how we used AI**?
