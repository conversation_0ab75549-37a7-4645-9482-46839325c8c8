import { http } from "@/utils/http";

/**
 * 文件服务相关接口
 * baseUrl: http://192.168.0.212:6888/
 */

// 文件服务基础URL
const BASE_URL = "http://192.168.0.212:6888";

/**
 * 文件列表项类型定义
 */
export interface FileItem {
  fileName: string;
  url: string;
}

/**
 * 上传文件
 * @param file 要上传的文件
 * @param relativePath 文件存储的相对路径（可选），若路径已存在同名文件则会自动覆盖
 * @returns 上传结果，包含 fileName 和 url
 */
export function uploadFile(file: File, relativePath?: string) {
  const formData = new FormData();
  formData.append("file", file);

  return http.post<any, any>(`${BASE_URL}/api/File/upload`, {
    data: formData,
    params: { relativePath },
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
}

/**
 * 下载文件
 * @param filePath 文件存储路径(从文件列表接口获取的完整路径)
 * @returns 文件内容
 */
export function downloadFile(filePath: string) {
  return http.get<Blob, any>(`${BASE_URL}/api/File`, {
    params: { filePath },
    responseType: "blob"
  });
}

/**
 * 删除文件或目录
 * @param filePath 要删除的文件或目录的相对路径
 * @returns 删除结果
 */
export function deleteFile(filePath: string) {
  return http.request<any>("delete", `${BASE_URL}/api/File`, {
    params: { filePath }
  });
}

/**
 * 获取文件列表
 * @param relativePath 查询的相对路径（可选）
 * @returns 文件列表
 */
export function getFileList(relativePath?: string) {
  return http.get<FileItem[], any>(`${BASE_URL}/api/File/list`, {
    params: { relativePath }
  });
}
