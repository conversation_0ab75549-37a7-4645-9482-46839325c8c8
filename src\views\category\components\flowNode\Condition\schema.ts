import type { Api } from "@form-create/element-ui";
import { initComponentAttributes, isUuidValue } from "../utils";
import { useComponentCategory } from "@/store/modules/componentCategory";

// 操作符选项
const OPERATORS = [
  { label: "等于", value: "eq" },
  { label: "不等于", value: "ne" },
  { label: "大于", value: "gt" },
  { label: "小于", value: "lt" },
  { label: "大于等于", value: "gte" },
  { label: "小于等于", value: "lte" },
  { label: "包含", value: "contains" },
  { label: "不包含", value: "not_contains" },
  { label: "为空", value: "is_null" },
  { label: "不为空", value: "is_not_null" }
];

// 逻辑操作符选项（保留用于扩展）
const _LOGIC_OPERATORS = [
  { label: "且", value: "AND" },
  { label: "或", value: "OR" }
];

// 条件计数器
let conditionCounter = 0;

/**
 * 同步字段值到 store
 * @param inject form-create实例
 * @param fieldName 字段名
 * @param fieldValue 字段值
 */
const syncFieldToStore = (
  inject: Api,
  fieldName: string,
  fieldValue: any,
  paramType = "static"
) => {
  try {
    const componentStore = useComponentCategory();
    const currentNodeId = inject.$f.getData("currentNodeId");

    if (currentNodeId && componentStore.flowData) {
      const fieldConfig = {
        name: fieldName,
        value: fieldValue,
        valueType: 0,
        paramType
      };
      // Update store
      componentStore.updateNodeConfig(currentNodeId, fieldConfig);
    }
  } catch (error) {
    console.error("同步字段配置失败:", error);
  }
};

/**
 * 获取条件数据
 * @param formData 表单数据
 * @returns 格式化的条件数据
 */
export const getConditionData = (formData: Record<string, any>) => {
  const conditions: any[] = [];
  const conditionIndices = new Set<number>();

  // 提取所有条件索引
  Object.keys(formData).forEach(key => {
    const match = key.match(/condition_(\d+)_attribute/);
    if (match) {
      conditionIndices.add(parseInt(match[1]));
    }
  });

  // 构建条件数组
  Array.from(conditionIndices)
    .sort((a, b) => a - b)
    .forEach((index, i) => {
      const attribute = formData[`condition_${index}_attribute`];
      const operator = formData[`condition_${index}_operator`];
      const value = formData[`condition_${index}_value`];
      const logic = formData[`condition_${index}_logic`];

      if (attribute && operator) {
        const condition: any = {
          attribute,
          operator,
          value:
            operator === "is_null" || operator === "is_not_null" ? null : value
        };

        // 第一个条件不需要逻辑操作符
        if (i > 0 && logic) {
          condition.logic = logic;
        }

        conditions.push(condition);
      }
    });

  return conditions;
};

/**
 * 设置条件数据
 * @param inject form-create实例
 * @param conditions 条件数据
 */
export const setConditionData = (inject: Api, conditions: any[]) => {
  if (!conditions || !Array.isArray(conditions)) return;

  const { setValue } = inject.$f;

  conditions.forEach((condition, index) => {
    conditionCounter++;
    const conditionIndex = conditionCounter;

    // 创建条件表单项
    const conditionItems = createConditionItems(conditionIndex, index === 0);

    // 添加到表单
    conditionItems.forEach(item => {
      inject.$f.append(item);
    });

    // 设置值
    setValue(`condition_${conditionIndex}_attribute`, condition.attribute);
    setValue(`condition_${conditionIndex}_operator`, condition.operator);
    setValue(`condition_${conditionIndex}_value`, condition.value);

    if (index > 0 && condition.logic) {
      setValue(`condition_${conditionIndex}_logic`, condition.logic);
    }
  });

  inject.$f.refresh();
};

/**
 * 创建单个条件的表单项
 * @param index 条件索引
 * @param isFirst 是否是第一个条件
 * @returns 条件表单项数组
 */
const createConditionItems = (index: number, isFirst: boolean = false) => {
  const conditionItems: any[] = [];

  // 条件配置容器 - 垂直布局
  const conditionGroup = {
    type: "div",
    field: `condition_group_${index}`,
    col: { span: 24 },
    style: {
      border: "1px solid #e4e7ed",
      borderRadius: "4px",
      padding: "8px",
      margin: "4px 0",
      backgroundColor: "#fafbfc",
      width: "100%"
    },
    children: [
      // 属性选择
      {
        type: "select",
        title: "属性",
        field: `condition_${index}_attribute`,
        value: "",
        valueType: 3,
        col: { span: 24 },
        style: { marginBottom: "6px" },
        props: {
          filterable: true,
          clearable: true,
          placeholder: "选择属性",
          size: "small"
        },
        validate: [{ required: true, message: "" }],
        effect: {
          loadData: {
            attr: "attributes",
            to: "options"
          }
        },
        inject: true,
        on: {
          change(inject: Api, value: any) {
            // 判断是否为UUID格式的动态引用值
            const valueType = isUuidValue(value) ? "nodeRef" : "static";
            // 同步字段值到 store
            syncFieldToStore(
              inject,
              `condition_${index}_attribute`,
              value,
              valueType
            );
          }
        }
      },
      // 操作符选择
      {
        type: "select",
        field: `condition_${index}_operator`,
        title: "操作符",
        value: "",
        valueType: 3,
        props: {
          placeholder: "选择操作符",
          size: "small"
        },
        validate: [{ required: true, message: "" }],
        options: OPERATORS,
        col: { span: 24 },
        style: { marginBottom: "6px" },
        inject: true,
        on: {
          change(inject: Api, value: any) {
            // 同步字段值到 store
            syncFieldToStore(inject, `condition_${index}_operator`, value);

            const { updateRule } = inject.$f;
            const valueField = `condition_${index}_value`;

            // 根据操作符类型调整值输入框的显示/隐藏
            if (value === "is_null" || value === "is_not_null") {
              updateRule(valueField, {
                style: { display: "none" },
                validate: []
              });
              // Clear the value when hidden
              inject.setValue(valueField, null);
              syncFieldToStore(inject, valueField, null);
            } else {
              updateRule(valueField, {
                style: { display: "block", marginBottom: "6px" },
                validate: [{ required: true, message: "" }]
              });
            }
          }
        }
      },
      // 对比值输入
      {
        type: "select",
        field: `condition_${index}_value`,
        value: "",
        valueType: 0,
        title: "对比值",
        col: { span: 24 },
        style: { marginBottom: "6px" },
        props: {
          placeholder: "输入对比值",
          size: "small",
          filterable: true,
          clearable: true,
          allowCreate: true,
          defaultFirstOption: true
        },
        validate: [{ required: true, message: "" }],
        effect: {
          loadData: {
            attr: "attributes",
            to: "options"
          }
        },
        inject: true,
        on: {
          change(inject: Api, value: any) {
            // 判断是否为UUID格式的动态引用值
            const valueType = isUuidValue(value) ? "ref" : "static";
            // 同步字段值到 store
            syncFieldToStore(
              inject,
              `condition_${index}_value`,
              value,
              valueType
            );
          }
        }
      },
      {
        type: "button",
        field: `condition_${index}_delete`,
        props: {
          type: "danger",
          size: "small"
        },
        children: ["删除"],
        col: { span: 24 },
        style: { width: "100%" },
        inject: true,
        on: {
          click(inject: Api) {
            removeCondition(inject, index);
          }
        }
      }
    ]
  };

  conditionItems.push(conditionGroup);
  // 如果不是第一个条件，添加逻辑操作符选择
  if (!isFirst) {
    conditionItems.push({
      type: "select",
      field: `condition_${index}_logic`,
      title: "逻辑关系",
      value: "",
      valueType: 3,
      props: {
        placeholder: "选择逻辑关系",
        size: "small"
      },
      options: _LOGIC_OPERATORS,
      col: { span: 24 },
      style: { marginBottom: "0" },
      inject: true,
      on: {
        change(inject: Api, value: any) {
          // 同步字段值到 store
          syncFieldToStore(inject, `condition_${index}_logic`, value);
        }
      }
    });
  }
  return conditionItems;
};

/**
 * 添加条件
 * @param inject form-create实例
 */
const addCondition = (inject: Api) => {
  const { append, form } = inject.$f;
  conditionCounter++;

  // 检查是否是第一个条件
  const existingConditions = Object.keys(form).filter(
    key => key.startsWith("condition_") && key.includes("_attribute")
  );
  const isFirst = existingConditions.length === 0;

  // 创建条件项
  const conditionItems = createConditionItems(conditionCounter, isFirst);

  // 添加条件表单项
  conditionItems.forEach(item => {
    append(item);
  });

  // 刷新表单
  inject.$f.refresh();

  // 同步更新 store 中的节点配置
  try {
    const componentStore = useComponentCategory();
    const currentNodeId = inject.$f.getData("currentNodeId");

    if (currentNodeId && componentStore.flowData) {
      // 为新添加的条件字段设置默认配置
      const newConditionFields = [
        `condition_${conditionCounter}_attribute`,
        `condition_${conditionCounter}_operator`,
        `condition_${conditionCounter}_value`
      ];

      // 如果不是第一个条件，还要添加逻辑操作符字段
      if (!isFirst) {
        newConditionFields.push(`condition_${conditionCounter}_logic`);
      }

      // 为每个新字段创建配置对象
      newConditionFields.forEach(fieldName => {
        const fieldConfig = {
          name: fieldName,
          value: "", // 默认空值
          valueType: 0,
          paramType: "static"
        };

        // 更新 store 中的节点配置
        componentStore.updateNodeConfig(currentNodeId, fieldConfig);
      });
    }
  } catch (error) {
    console.error("同步添加条件配置失败:", error);
  }
};

/**
 * 删除条件
 * @param inject form-create实例
 * @param index 条件索引
 */
const removeCondition = (inject: Api, index: number) => {
  const { removeField, form, getData, refresh } = inject.$f;

  // 1. 收集需要从 configValue 中删除的字段
  const configKeysToRemove = Object.keys(form).filter(key =>
    key.startsWith(`condition_${index}_`)
  );

  // 2. 从表单中删除 UI 组件
  removeField(`condition_group_${index}`);
  const logicField = `condition_${index}_logic`;
  if (form[logicField]) {
    removeField(logicField);
  }

  // 3. 检查删除后是否需要调整新的第一个条件（移除其逻辑操作符）
  const remainingConditionAttributes = Object.keys(form)
    .filter(key => key.startsWith("condition_") && key.endsWith("_attribute"))
    .sort((a, b) => {
      const indexA = parseInt(a.match(/(\d+)/)[0], 10);
      const indexB = parseInt(b.match(/(\d+)/)[0], 10);
      return indexA - indexB;
    });

  if (remainingConditionAttributes.length > 0) {
    const firstConditionAttr = remainingConditionAttributes[0];
    const firstIndex = firstConditionAttr.match(/condition_(\d+)_attribute/)[1];
    const firstLogicField = `condition_${firstIndex}_logic`;
    if (form[firstLogicField]) {
      removeField(firstLogicField);
      // Also remove from config
      configKeysToRemove.push(firstLogicField);
    }
  }

  refresh();

  // 4. 同步更新 store 中的节点配置并触发节点刷新
  try {
    const componentStore = useComponentCategory();
    const currentNodeId = getData("currentNodeId");

    if (currentNodeId && componentStore.flowData) {
      const nodeIndex = componentStore.flowData.nodes.findIndex(
        (node: any) => node.id === currentNodeId
      );

      if (nodeIndex >= 0) {
        const node = componentStore.flowData.nodes[nodeIndex];
        const configValue = node.data.configValue || {};
        const newConfigValue = { ...configValue };

        // 删除被移除的字段配置
        configKeysToRemove.forEach(key => {
          if (newConfigValue.hasOwnProperty(key)) {
            Reflect.deleteProperty(newConfigValue, key);
          }
        });

        // 更新节点配置
        node.data.configValue = newConfigValue;

        // 触发 vue-flow 更新
        if (componentStore.flowInstance) {
          componentStore.flowInstance.updateNode(currentNodeId, {
            data: { ...node.data }
          });
        }
      }
    }
  } catch (error) {
    console.error("同步节点配置失败:", error);
  }
};

export default [
  {
    type: "button",
    field: "add_condition",
    props: {
      type: "primary",
      size: "small"
    },
    withoutReference: true,
    children: ["添加条件"],
    col: { span: 24 },
    style: {
      marginBottom: "12px",
      width: "100%"
    },
    inject: true,
    on: {
      click(inject: Api) {
        addCondition(inject);
      }
    },
    hook: {
      mounted(inject: Api) {
        const { form, append, refresh, setData, getData } = inject.$f;
        initComponentAttributes(setData, getData);
        const previousNodesOutput =
          inject.$f.options.formData.previousNodesOutput || [];

        const currentAttributes = getData("attributes") || [];

        const componentStore = useComponentCategory();
        const flowInstance = componentStore.flowInstance;
        if (flowInstance) {
          const { getIncomers, findNode } = flowInstance;
          const currentNodeId = inject.$f.options.formData.currentNodeId;
          setData("currentNodeId", currentNodeId);
          const currentNode = findNode(currentNodeId);

          const collectedNodeIds = new Set<string>();
          const paramList: any[] = [];

          const collectUpstreamNodes = (node: any) => {
            if (!node || collectedNodeIds.has(node.id)) return;

            collectedNodeIds.add(node.id);
            paramList.push({
              id: node.id,
              name:
                node.data.configValue?.name?.value ||
                node.data?.name ||
                node.id,
              outputParams: node.data?.outputParams || []
            });

            const incomers = getIncomers(node);
            incomers.forEach(incomer => collectUpstreamNodes(incomer));
          };

          if (currentNode) {
            const directIncomers = getIncomers(currentNode);
            directIncomers.forEach(node => collectUpstreamNodes(node));
          }

          const upstreamGroup = {
            label: "上游节点出参",
            options: previousNodesOutput
          };

          const newAttributes = [
            {
              label: "当前组件属性",
              options: currentAttributes
            },
            upstreamGroup
          ];

          setData("attributes", newAttributes);
        }

        // Parse configValue to find existing conditions
        const conditionIndices = new Set<number>();
        Object.keys(form).forEach(key => {
          const match = key.match(/condition_(\d+)_attribute/);
          if (match) {
            conditionIndices.add(parseInt(match[1], 10));
          }
        });

        if (conditionIndices.size > 0) {
          const sortedIndices = Array.from(conditionIndices).sort(
            (a, b) => a - b
          );

          // For each condition, create and append the form items
          sortedIndices.forEach((index, i) => {
            conditionCounter = Math.max(conditionCounter, index);
            const isFirst = i === 0;
            const conditionItems = createConditionItems(index, isFirst);
            conditionItems.forEach(item => {
              append(item);
            });
          });

          refresh();
        }
      }
    }
  }
];
