import type { Config } from "tailwindcss";
import tailwindcssAnimate from "tailwindcss-animate";

export default {
  darkMode: "class",
  corePlugins: {
    preflight: false
  },
  content: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        bg_color: "var(--el-bg-color)",
        primary: "var(--el-color-primary)",
        text_color_primary: "var(--el-text-color-primary)",
        text_color_regular: "var(--el-text-color-regular)"
      },
      animation: {
        // 🧬 量子级微物理动效系统
        // 基于真实物理学原理的高品质动效

        // 核心量子动效 - 模拟量子力学现象
        "quantum-breathing":
          "quantum-breathing 4s cubic-bezier(0.4, 0, 0.2, 1) infinite",
        "quantum-entanglement":
          "quantum-entanglement 0.8s cubic-bezier(0.68, -0.6, 0.32, 1.6) forwards",
        "quantum-superposition":
          "quantum-superposition 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards",

        // 磁场动效 - 模拟电磁学现象
        "magnetic-field":
          "magnetic-field 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards",
        "electromagnetic-pulse":
          "electromagnetic-pulse 1.2s cubic-bezier(0.19, 1, 0.22, 1) forwards",
        "magnetic-levitation":
          "magnetic-levitation 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",

        // 流体动效 - 模拟流体力学
        "liquid-surface-tension":
          "liquid-surface-tension 0.7s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards",
        "viscous-deformation":
          "viscous-deformation 0.9s cubic-bezier(0.23, 1, 0.32, 1) forwards",
        "fluid-dynamics":
          "fluid-dynamics 3s cubic-bezier(0.4, 0, 0.2, 1) infinite",

        // 光学动效 - 模拟光学现象
        "photon-emission":
          "photon-emission 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards",
        "light-refraction":
          "light-refraction 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards",
        "optical-interference":
          "optical-interference 2.5s cubic-bezier(0.4, 0, 0.6, 1) infinite",

        // 弹性动效 - 模拟材料力学
        "elastic-deformation":
          "elastic-deformation 0.8s cubic-bezier(0.68, -0.6, 0.32, 1.6) forwards",
        "spring-oscillation":
          "spring-oscillation 1.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards",
        "damped-harmonic":
          "damped-harmonic 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards",

        // 量子场效应动效
        "quantum-field-oscillation":
          "quantum-field-oscillation 3s ease-in-out infinite",
        "quantum-particle-dance":
          "quantum-particle-dance 2.5s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        "quantum-field-amplification":
          "quantum-field-amplification 0.8s cubic-bezier(0.68, -0.6, 0.32, 1.6) forwards",
        "quantum-particle-burst":
          "quantum-particle-burst 0.8s cubic-bezier(0.68, -0.6, 0.32, 1.6) forwards"
      },
      keyframes: {
        // 🧬 量子级微物理动效关键帧
        // 基于真实物理学原理的精密动画

        // 量子呼吸 - 模拟量子概率云的波动
        "quantum-breathing": {
          "0%": {
            transform: "scale(1) translateZ(0) rotateX(0deg)",
            filter:
              "drop-shadow(0 4px 12px rgba(59, 130, 246, 0.3)) brightness(1) blur(0px)",
            opacity: "1"
          },
          "25%": {
            transform: "scale(1.008) translateZ(2px) rotateX(0.5deg)",
            filter:
              "drop-shadow(0 6px 18px rgba(59, 130, 246, 0.4)) brightness(1.03) blur(0.2px)",
            opacity: "0.98"
          },
          "50%": {
            transform: "scale(1.012) translateZ(3px) rotateX(0deg)",
            filter:
              "drop-shadow(0 8px 24px rgba(59, 130, 246, 0.5)) brightness(1.05) blur(0.3px)",
            opacity: "0.95"
          },
          "75%": {
            transform: "scale(1.005) translateZ(1px) rotateX(-0.3deg)",
            filter:
              "drop-shadow(0 5px 15px rgba(59, 130, 246, 0.35)) brightness(1.02) blur(0.1px)",
            opacity: "0.97"
          },
          "100%": {
            transform: "scale(1) translateZ(0) rotateX(0deg)",
            filter:
              "drop-shadow(0 4px 12px rgba(59, 130, 246, 0.3)) brightness(1) blur(0px)",
            opacity: "1"
          }
        },
        // 量子纠缠 - 模拟量子纠缠的瞬时关联
        "quantum-entanglement": {
          "0%": {
            transform: "scale(1) rotate(0deg) skew(0deg, 0deg)",
            filter:
              "drop-shadow(0 4px 12px rgba(59, 130, 246, 0.3)) brightness(1) hue-rotate(0deg)",
            borderRadius: "50%"
          },
          "15%": {
            transform: "scale(0.95) rotate(2deg) skew(1deg, 0deg)",
            filter:
              "drop-shadow(0 2px 8px rgba(59, 130, 246, 0.2)) brightness(0.9) hue-rotate(5deg)",
            borderRadius: "45%"
          },
          "30%": {
            transform: "scale(1.15) rotate(-1deg) skew(-0.5deg, 1deg)",
            filter:
              "drop-shadow(0 16px 40px rgba(59, 130, 246, 0.8)) brightness(1.3) hue-rotate(15deg)",
            borderRadius: "60%"
          },
          "50%": {
            transform: "scale(1.25) rotate(3deg) skew(1.5deg, -1deg)",
            filter:
              "drop-shadow(0 24px 60px rgba(59, 130, 246, 0.9)) brightness(1.5) hue-rotate(25deg)",
            borderRadius: "40%"
          },
          "70%": {
            transform: "scale(1.18) rotate(-0.5deg) skew(-0.8deg, 0.5deg)",
            filter:
              "drop-shadow(0 20px 50px rgba(59, 130, 246, 0.85)) brightness(1.35) hue-rotate(20deg)",
            borderRadius: "55%"
          },
          "100%": {
            transform: "scale(1.12) rotate(0deg) skew(0deg, 0deg)",
            filter:
              "drop-shadow(0 18px 45px rgba(59, 130, 246, 0.75)) brightness(1.25) hue-rotate(12deg)",
            borderRadius: "50%"
          }
        },
        // 量子叠加态 - 模拟量子叠加的概率分布
        "quantum-superposition": {
          "0%": {
            transform: "scale(1) translateX(0px) translateY(0px)",
            filter:
              "drop-shadow(0 4px 12px rgba(59, 130, 246, 0.3)) brightness(1)",
            opacity: "1"
          },
          "20%": {
            transform: "scale(1.05) translateX(2px) translateY(-1px)",
            filter:
              "drop-shadow(0 8px 20px rgba(59, 130, 246, 0.5)) brightness(1.1)",
            opacity: "0.9"
          },
          "40%": {
            transform: "scale(0.98) translateX(-1px) translateY(2px)",
            filter:
              "drop-shadow(0 6px 16px rgba(59, 130, 246, 0.4)) brightness(0.95)",
            opacity: "0.8"
          },
          "60%": {
            transform: "scale(1.08) translateX(1px) translateY(-2px)",
            filter:
              "drop-shadow(0 12px 28px rgba(59, 130, 246, 0.6)) brightness(1.15)",
            opacity: "0.85"
          },
          "80%": {
            transform: "scale(1.02) translateX(-0.5px) translateY(1px)",
            filter:
              "drop-shadow(0 10px 24px rgba(59, 130, 246, 0.55)) brightness(1.08)",
            opacity: "0.9"
          },
          "100%": {
            transform: "scale(1.06) translateX(0px) translateY(0px)",
            filter:
              "drop-shadow(0 14px 32px rgba(59, 130, 246, 0.65)) brightness(1.12)",
            opacity: "0.95"
          }
        },
        // 磁场效应 - 模拟磁场的吸引和排斥
        "magnetic-field": {
          "0%": {
            transform: "scale(1) translateZ(0) rotateY(0deg)",
            filter:
              "drop-shadow(0 4px 12px rgba(59, 130, 246, 0.3)) brightness(1)",
            boxShadow: "0 0 0 0 rgba(59, 130, 246, 0)"
          },
          "25%": {
            transform: "scale(1.08) translateZ(5px) rotateY(2deg)",
            filter:
              "drop-shadow(0 12px 32px rgba(59, 130, 246, 0.6)) brightness(1.15)",
            boxShadow: "0 0 20px 5px rgba(59, 130, 246, 0.3)"
          },
          "50%": {
            transform: "scale(1.15) translateZ(8px) rotateY(0deg)",
            filter:
              "drop-shadow(0 20px 50px rgba(59, 130, 246, 0.8)) brightness(1.25)",
            boxShadow: "0 0 40px 10px rgba(59, 130, 246, 0.4)"
          },
          "75%": {
            transform: "scale(1.12) translateZ(6px) rotateY(-1deg)",
            filter:
              "drop-shadow(0 18px 45px rgba(59, 130, 246, 0.75)) brightness(1.2)",
            boxShadow: "0 0 35px 8px rgba(59, 130, 246, 0.35)"
          },
          "100%": {
            transform: "scale(1.1) translateZ(4px) rotateY(0deg)",
            filter:
              "drop-shadow(0 16px 40px rgba(59, 130, 246, 0.7)) brightness(1.18)",
            boxShadow: "0 0 30px 6px rgba(59, 130, 246, 0.3)"
          }
        },

        // 电磁脉冲 - 模拟电磁波的传播
        "electromagnetic-pulse": {
          "0%": {
            transform: "scale(1) rotateZ(0deg)",
            filter:
              "drop-shadow(0 4px 12px rgba(59, 130, 246, 0.3)) brightness(1) saturate(1)",
            boxShadow:
              "0 0 0 0 rgba(59, 130, 246, 0), 0 0 0 0 rgba(147, 197, 253, 0)"
          },
          "20%": {
            transform: "scale(1.05) rotateZ(5deg)",
            filter:
              "drop-shadow(0 8px 20px rgba(59, 130, 246, 0.5)) brightness(1.1) saturate(1.2)",
            boxShadow:
              "0 0 20px 5px rgba(59, 130, 246, 0.4), 0 0 40px 10px rgba(147, 197, 253, 0.2)"
          },
          "40%": {
            transform: "scale(1.12) rotateZ(-3deg)",
            filter:
              "drop-shadow(0 16px 40px rgba(59, 130, 246, 0.7)) brightness(1.2) saturate(1.4)",
            boxShadow:
              "0 0 40px 10px rgba(59, 130, 246, 0.5), 0 0 80px 20px rgba(147, 197, 253, 0.3)"
          },
          "60%": {
            transform: "scale(1.18) rotateZ(2deg)",
            filter:
              "drop-shadow(0 24px 60px rgba(59, 130, 246, 0.8)) brightness(1.3) saturate(1.6)",
            boxShadow:
              "0 0 60px 15px rgba(59, 130, 246, 0.6), 0 0 120px 30px rgba(147, 197, 253, 0.4)"
          },
          "80%": {
            transform: "scale(1.15) rotateZ(-1deg)",
            filter:
              "drop-shadow(0 20px 50px rgba(59, 130, 246, 0.75)) brightness(1.25) saturate(1.5)",
            boxShadow:
              "0 0 50px 12px rgba(59, 130, 246, 0.55), 0 0 100px 25px rgba(147, 197, 253, 0.35)"
          },
          "100%": {
            transform: "scale(1.12) rotateZ(0deg)",
            filter:
              "drop-shadow(0 18px 45px rgba(59, 130, 246, 0.7)) brightness(1.2) saturate(1.4)",
            boxShadow:
              "0 0 45px 10px rgba(59, 130, 246, 0.5), 0 0 90px 22px rgba(147, 197, 253, 0.3)"
          }
        },

        // 磁悬浮 - 模拟磁悬浮的稳定状态
        "magnetic-levitation": {
          "0%, 100%": {
            transform: "translateY(0px) rotateX(0deg) scale(1)",
            filter:
              "drop-shadow(0 4px 12px rgba(59, 130, 246, 0.3)) brightness(1)"
          },
          "25%": {
            transform: "translateY(-3px) rotateX(1deg) scale(1.01)",
            filter:
              "drop-shadow(0 8px 20px rgba(59, 130, 246, 0.4)) brightness(1.05)"
          },
          "50%": {
            transform: "translateY(-5px) rotateX(0deg) scale(1.02)",
            filter:
              "drop-shadow(0 12px 28px rgba(59, 130, 246, 0.5)) brightness(1.08)"
          },
          "75%": {
            transform: "translateY(-2px) rotateX(-0.5deg) scale(1.01)",
            filter:
              "drop-shadow(0 10px 24px rgba(59, 130, 246, 0.45)) brightness(1.06)"
          }
        },

        // 液体表面张力 - 模拟液滴的形变
        "liquid-surface-tension": {
          "0%, 100%": {
            transform: "scale(1) translateX(0px) translateY(0px) rotate(0deg)",
            filter:
              "drop-shadow(0 4px 12px rgba(56, 189, 248, 0.3)) brightness(1)"
          },
          "16%": {
            transform:
              "scale(1.01) translateX(0.5px) translateY(-0.3px) rotate(0.2deg)",
            filter:
              "drop-shadow(0 5px 14px rgba(56, 189, 248, 0.35)) brightness(1.02)"
          },
          "33%": {
            transform:
              "scale(1.008) translateX(-0.3px) translateY(0.5px) rotate(-0.1deg)",
            filter:
              "drop-shadow(0 4px 13px rgba(56, 189, 248, 0.32)) brightness(1.01)"
          },
          "50%": {
            transform:
              "scale(1.012) translateX(0.2px) translateY(-0.2px) rotate(0.15deg)",
            filter:
              "drop-shadow(0 5px 15px rgba(56, 189, 248, 0.36)) brightness(1.025)"
          },
          "66%": {
            transform:
              "scale(1.005) translateX(-0.4px) translateY(0.3px) rotate(-0.08deg)",
            filter:
              "drop-shadow(0 4px 11px rgba(56, 189, 248, 0.31)) brightness(1.008)"
          },
          "83%": {
            transform:
              "scale(1.009) translateX(0.3px) translateY(-0.4px) rotate(0.12deg)",
            filter:
              "drop-shadow(0 5px 13px rgba(56, 189, 248, 0.34)) brightness(1.015)"
          }
        },
        // 有机流动动画关键帧
        "organic-float": {
          "0%, 100%": {
            transform: "translateY(0px) translateX(0px) rotate(0deg) scale(1)",
            filter:
              "drop-shadow(0 4px 12px rgba(56, 189, 248, 0.3)) brightness(1)"
          },
          "20%": {
            transform:
              "translateY(-2px) translateX(1px) rotate(0.5deg) scale(1.008)",
            filter:
              "drop-shadow(0 5px 14px rgba(56, 189, 248, 0.35)) brightness(1.02)"
          },
          "40%": {
            transform:
              "translateY(-1px) translateX(-0.5px) rotate(-0.3deg) scale(1.005)",
            filter:
              "drop-shadow(0 4px 13px rgba(56, 189, 248, 0.32)) brightness(1.01)"
          },
          "60%": {
            transform:
              "translateY(-3px) translateX(0.8px) rotate(0.4deg) scale(1.012)",
            filter:
              "drop-shadow(0 6px 16px rgba(56, 189, 248, 0.38)) brightness(1.025)"
          },
          "80%": {
            transform:
              "translateY(-0.5px) translateX(-1px) rotate(-0.2deg) scale(1.003)",
            filter:
              "drop-shadow(0 4px 11px rgba(56, 189, 248, 0.31)) brightness(1.008)"
          }
        },
        "liquid-morph": {
          "0%, 100%": {
            borderRadius: "50%",
            transform: "scale(1) skew(0deg, 0deg)"
          },
          "25%": {
            borderRadius: "60% 40% 30% 70%",
            transform: "scale(1.02) skew(0.5deg, -0.3deg)"
          },
          "50%": {
            borderRadius: "30% 60% 70% 40%",
            transform: "scale(1.01) skew(-0.3deg, 0.4deg)"
          },
          "75%": {
            borderRadius: "40% 30% 60% 70%",
            transform: "scale(1.015) skew(0.2deg, -0.1deg)"
          }
        },
        "energy-pulse": {
          "0%, 100%": {
            transform: "scale(1)",
            filter:
              "drop-shadow(0 4px 12px rgba(56, 189, 248, 0.3)) brightness(1)",
            opacity: "1"
          },
          "50%": {
            transform: "scale(1.03)",
            filter:
              "drop-shadow(0 8px 24px rgba(56, 189, 248, 0.6)) brightness(1.15)",
            opacity: "0.9"
          }
        },
        "gentle-sway": {
          "0%, 100%": {
            transform: "rotate(0deg) translateX(0px)"
          },
          "25%": {
            transform: "rotate(0.8deg) translateX(1px)"
          },
          "50%": {
            transform: "rotate(0deg) translateX(0px)"
          },
          "75%": {
            transform: "rotate(-0.8deg) translateX(-1px)"
          }
        },
        "breath-of-life": {
          "0%, 100%": {
            transform: "scale(1) translateY(0px)",
            filter:
              "drop-shadow(0 4px 12px rgba(56, 189, 248, 0.3)) brightness(1) saturate(1)"
          },
          "33%": {
            transform: "scale(1.015) translateY(-0.8px)",
            filter:
              "drop-shadow(0 6px 16px rgba(56, 189, 248, 0.4)) brightness(1.05) saturate(1.1)"
          },
          "66%": {
            transform: "scale(1.008) translateY(0.3px)",
            filter:
              "drop-shadow(0 5px 14px rgba(56, 189, 248, 0.35)) brightness(1.02) saturate(1.05)"
          }
        },
        "living-glow": {
          "0%, 100%": {
            filter:
              "drop-shadow(0 4px 12px rgba(56, 189, 248, 0.3)) brightness(1) hue-rotate(0deg)"
          },
          "33%": {
            filter:
              "drop-shadow(0 6px 18px rgba(56, 189, 248, 0.45)) brightness(1.08) hue-rotate(5deg)"
          },
          "66%": {
            filter:
              "drop-shadow(0 5px 15px rgba(56, 189, 248, 0.38)) brightness(1.04) hue-rotate(3deg)"
          }
        },
        "natural-rhythm": {
          "0%, 100%": {
            transform: "scale(1) rotate(0deg) translateY(0px)",
            filter: "drop-shadow(0 4px 12px rgba(56, 189, 248, 0.3))"
          },
          "20%": {
            transform: "scale(1.005) rotate(0.2deg) translateY(-0.5px)",
            filter: "drop-shadow(0 5px 14px rgba(56, 189, 248, 0.35))"
          },
          "40%": {
            transform: "scale(1.008) rotate(-0.1deg) translateY(0.2px)",
            filter: "drop-shadow(0 4px 13px rgba(56, 189, 248, 0.32))"
          },
          "60%": {
            transform: "scale(1.003) rotate(0.15deg) translateY(-0.3px)",
            filter: "drop-shadow(0 5px 15px rgba(56, 189, 248, 0.36))"
          },
          "80%": {
            transform: "scale(1.006) rotate(-0.08deg) translateY(0.1px)",
            filter: "drop-shadow(0 4px 11px rgba(56, 189, 248, 0.31))"
          }
        },
        // 兼容性动画关键帧
        "gentle-bounce": {
          "0%, 100%": {
            transform: "translateY(0) scale(1)"
          },
          "50%": {
            transform: "translateY(-4px) scale(1.02)"
          }
        },
        "soft-pulse": {
          "0%, 100%": {
            opacity: "1",
            transform: "scale(1)"
          },
          "50%": {
            opacity: "0.8",
            transform: "scale(1.02)"
          }
        },
        "subtle-float": {
          "0%, 100%": {
            transform: "translateY(0px) rotate(0deg)"
          },
          "33%": {
            transform: "translateY(-2px) rotate(0.5deg)"
          },
          "66%": {
            transform: "translateY(1px) rotate(-0.5deg)"
          }
        },
        // 交互感应动画关键帧
        "magnetic-attract": {
          "0%": {
            transform: "scale(1) translateZ(0)"
          },
          "100%": {
            transform: "scale(1.05) translateZ(0)"
          }
        },
        "elastic-bounce": {
          "0%": {
            transform: "scale(1)"
          },
          "30%": {
            transform: "scale(1.15)"
          },
          "60%": {
            transform: "scale(0.95)"
          },
          "100%": {
            transform: "scale(1)"
          }
        },
        "smooth-emerge": {
          "0%": {
            transform: "scale(0.95) translateY(5px)",
            opacity: "0.8"
          },
          "100%": {
            transform: "scale(1) translateY(0px)",
            opacity: "1"
          }
        },
        // 量子场振荡
        "quantum-field-oscillation": {
          "0%, 100%": {
            transform: "scale(1) rotate(0deg)",
            opacity: "0.6"
          },
          "33%": {
            transform: "scale(1.05) rotate(1deg)",
            opacity: "0.8"
          },
          "66%": {
            transform: "scale(0.98) rotate(-0.5deg)",
            opacity: "0.7"
          }
        },

        // 量子粒子舞蹈
        "quantum-particle-dance": {
          "0%, 100%": {
            transform: "scale(1) rotate(0deg)",
            opacity: "0.4"
          },
          "20%": {
            transform: "scale(1.02) rotate(2deg)",
            opacity: "0.6"
          },
          "40%": {
            transform: "scale(0.99) rotate(-1deg)",
            opacity: "0.5"
          },
          "60%": {
            transform: "scale(1.01) rotate(1.5deg)",
            opacity: "0.7"
          },
          "80%": {
            transform: "scale(1.005) rotate(-0.5deg)",
            opacity: "0.55"
          }
        },

        // 量子场放大
        "quantum-field-amplification": {
          "0%": {
            transform: "scale(1) rotate(0deg)",
            opacity: "0.6"
          },
          "50%": {
            transform: "scale(1.3) rotate(5deg)",
            opacity: "0.9"
          },
          "100%": {
            transform: "scale(1.2) rotate(2deg)",
            opacity: "0.8"
          }
        },

        // 量子粒子爆发
        "quantum-particle-burst": {
          "0%": {
            transform: "scale(1) rotate(0deg)",
            opacity: "0.4"
          },
          "30%": {
            transform: "scale(1.5) rotate(10deg)",
            opacity: "0.8"
          },
          "100%": {
            transform: "scale(1.3) rotate(5deg)",
            opacity: "0.6"
          }
        }
      },
      transitionTimingFunction: {
        smooth: "cubic-bezier(0.4, 0, 0.2, 1)",
        gentle: "cubic-bezier(0.25, 0.46, 0.45, 0.94)",
        "bounce-soft": "cubic-bezier(0.68, -0.55, 0.265, 1.55)"
      },
      transitionDuration: {
        "250": "250ms",
        "350": "350ms",
        "400": "400ms"
      },
      scale: {
        "102": "1.02",
        "103": "1.03",
        "108": "1.08"
      }
    }
  },
  plugins: [tailwindcssAnimate]
} satisfies Config;
