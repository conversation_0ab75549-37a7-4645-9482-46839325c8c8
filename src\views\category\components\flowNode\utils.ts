import { useComponentCategory } from "@/store/modules/componentCategory";

/**
 * 初始化组件属性数据
 * @param setData 设置数据的函数
 * @param getData 获取数据的函数
 */
export const initComponentAttributes = (setData: any, getData: any) => {
  if (!getData("attributes")) {
    const { selectedComponent } = useComponentCategory();
    setData(
      "attributes",
      (selectedComponent?.attributes || []).map(attr => ({
        value: "component." + attr.name,
        label: attr.name,
        type: attr.type,
        description: attr.description
      }))
    );
  }
};

export const isUuidValue = (str: string) => {
  const regexExp = /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}/i;
  return regexExp.test(str);
};
