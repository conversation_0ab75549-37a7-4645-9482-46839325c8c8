#!/usr/bin/env node

/**
 * 动画验证脚本 - 彻底检查所有动画配置
 * 确保 Tailwind CSS 配置与代码使用完全匹配
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

console.log('🔍 开始验证动画配置...\n');

// 1. 读取 Tailwind 配置中定义的动画
function getTailwindAnimations() {
  const configPath = path.join(__dirname, '../tailwind.config.ts');
  const configContent = fs.readFileSync(configPath, 'utf-8');
  
  const animations = [];
  
  // 匹配动画定义
  const animationRegex = /"([^"]+)":\s*"[^"]+"/g;
  let match;
  while ((match = animationRegex.exec(configContent)) !== null) {
    animations.push(match[1]);
  }
  
  return animations;
}

// 2. 搜索代码中使用的动画类
function getUsedAnimations() {
  const usedAnimations = new Set();
  
  // 搜索的文件模式
  const patterns = [
    'src/**/*.vue',
    'src/**/*.css',
    'src/**/*.scss',
    'src/**/*.ts',
    'src/**/*.js'
  ];
  
  patterns.forEach(pattern => {
    const files = glob.sync(pattern, { cwd: path.join(__dirname, '..') });
    
    files.forEach(file => {
      const filePath = path.join(__dirname, '..', file);
      const content = fs.readFileSync(filePath, 'utf-8');
      
      // 匹配 animate- 开头的类名
      const animateRegex = /animate-([a-z-]+)/g;
      let match;
      while ((match = animateRegex.exec(content)) !== null) {
        usedAnimations.add(match[1]);
      }
      
      // 匹配 @apply animate- 的情况
      const applyRegex = /@apply\s+animate-([a-z-]+)/g;
      while ((match = applyRegex.exec(content)) !== null) {
        usedAnimations.add(match[1]);
      }
    });
  });
  
  return Array.from(usedAnimations);
}

// 3. 主验证函数
function validateAnimations() {
  const definedAnimations = getTailwindAnimations();
  const usedAnimations = getUsedAnimations();
  
  console.log('📋 Tailwind 配置中定义的动画:');
  definedAnimations.forEach(anim => {
    console.log(`  ✅ ${anim}`);
  });
  
  console.log('\n🎯 代码中使用的动画:');
  usedAnimations.forEach(anim => {
    const isDefined = definedAnimations.includes(anim);
    console.log(`  ${isDefined ? '✅' : '❌'} ${anim}`);
  });
  
  // 检查未定义的动画
  const undefinedAnimations = usedAnimations.filter(anim => 
    !definedAnimations.includes(anim)
  );
  
  // 检查未使用的动画
  const unusedAnimations = definedAnimations.filter(anim => 
    !usedAnimations.includes(anim)
  );
  
  console.log('\n📊 验证结果:');
  
  if (undefinedAnimations.length > 0) {
    console.log('\n❌ 未定义的动画 (需要修复):');
    undefinedAnimations.forEach(anim => {
      console.log(`  - animate-${anim}`);
    });
    
    console.log('\n🔧 修复建议:');
    console.log('在 tailwind.config.ts 的 animation 部分添加:');
    undefinedAnimations.forEach(anim => {
      console.log(`"${anim}": "${anim} 0.3s ease-in-out",`);
    });
  }
  
  if (unusedAnimations.length > 0) {
    console.log('\n⚠️  未使用的动画 (可以清理):');
    unusedAnimations.forEach(anim => {
      console.log(`  - ${anim}`);
    });
  }
  
  if (undefinedAnimations.length === 0 && unusedAnimations.length === 0) {
    console.log('✅ 所有动画配置正确！');
  }
  
  return undefinedAnimations.length === 0;
}

// 4. 检查关键帧定义
function validateKeyframes() {
  const configPath = path.join(__dirname, '../tailwind.config.ts');
  const configContent = fs.readFileSync(configPath, 'utf-8');
  
  // 提取动画名称
  const animationNames = [];
  const animationRegex = /"([^"]+)":\s*"([^"]+)\s+/g;
  let match;
  while ((match = animationRegex.exec(configContent)) !== null) {
    const animationName = match[2].split(' ')[0]; // 获取动画名称
    animationNames.push(animationName);
  }
  
  // 检查关键帧定义
  const keyframeNames = [];
  const keyframeRegex = /"([^"]+)":\s*{/g;
  const keyframeSection = configContent.match(/keyframes:\s*{([\s\S]*?)},\s*transitionTimingFunction/);
  if (keyframeSection) {
    while ((match = keyframeRegex.exec(keyframeSection[1])) !== null) {
      keyframeNames.push(match[1]);
    }
  }
  
  console.log('\n🎬 关键帧验证:');
  const missingKeyframes = animationNames.filter(anim => 
    !keyframeNames.includes(anim)
  );
  
  if (missingKeyframes.length > 0) {
    console.log('❌ 缺少关键帧定义:');
    missingKeyframes.forEach(anim => {
      console.log(`  - ${anim}`);
    });
  } else {
    console.log('✅ 所有动画都有对应的关键帧定义');
  }
  
  return missingKeyframes.length === 0;
}

// 5. 运行验证
try {
  const animationsValid = validateAnimations();
  const keyframesValid = validateKeyframes();
  
  console.log('\n🎯 总体结果:');
  if (animationsValid && keyframesValid) {
    console.log('✅ 所有动画配置完全正确！');
    process.exit(0);
  } else {
    console.log('❌ 发现配置问题，请根据上述建议修复');
    process.exit(1);
  }
} catch (error) {
  console.error('❌ 验证过程中出错:', error.message);
  process.exit(1);
}
