// 配置项类型
export enum FieldType {
  TEXT = "text",
  TEXTAREA = "textarea",
  NUMBER = "number",
  SELECT = "select",
  SWITCH = "switch",
  CHECKBOX = "checkbox",
  RADIO = "radio",
  DATE = "date",
  DATETIME = "datetime",
  TIME = "time",
  COLOR = "color",
  COMPONENT = "component", // 自定义组件
  VARIABLE_LIST = "variable_list", // 变量列表
  VARIABLE_REFERENCE = "variable_reference" // 变量引用
}

// 基础字段接口
export interface BaseField {
  type: FieldType; // 字段类型
  label: string; // 字段标签
  fieldName: string; // 字段键名
  required?: boolean; // 是否必填
  disabled?: boolean; // 是否禁用
  hidden?: boolean; // 是否隐藏
  placeholder?: string; // 占位符
  description?: string; // 描述
  defaultValue?: any; // 默认值
  validators?: Array<{
    // 验证器
    type: string; // 验证类型
    message: string; // 错误信息
    params?: any; // 验证参数
  }>;
  // 条件显示：根据其他字段值确定是否显示本字段
  visibleIf?: {
    field: string; // 依赖的字段名
    operator:
      | "=="
      | "!="
      | ">"
      | ">="
      | "<"
      | "<="
      | "includes"
      | "empty"
      | "not-empty";
    value: any; // 比较值
  };
}

// 文本字段
export interface TextField extends BaseField {
  type: FieldType.TEXT;
  maxLength?: number; // 最大长度
  minLength?: number; // 最小长度
}

// 文本域字段
export interface TextareaField extends BaseField {
  type: FieldType.TEXTAREA;
  rows?: number; // 行数
  maxLength?: number; // 最大长度
  minLength?: number; // 最小长度
}

// 数字字段
export interface NumberField extends BaseField {
  type: FieldType.NUMBER;
  min?: number; // 最小值
  max?: number; // 最大值
  step?: number; // 步长
  precision?: number; // 精度
}

// 选择字段基础接口
export interface SelectFieldBase extends BaseField {
  type: FieldType.SELECT;
  multiple?: boolean; // 是否多选
  filterable?: boolean; // 是否可搜索
  clearable?: boolean; // 是否可清空
}

// 带静态选项的选择字段
export interface StaticSelectField extends SelectFieldBase {
  options: Array<{
    // 选项
    label: string; // 选项标签
    value: any; // 选项值
    disabled?: boolean; // 是否禁用
  }>;
  dynamicOptions?: undefined;
}

// 带动态选项的选择字段
export interface DynamicSelectField extends SelectFieldBase {
  options?: Array<{
    // 可选静态选项
    label: string;
    value: any;
    disabled?: boolean;
  }>;
  dynamicOptions: {
    type: "api" | "function" | "store";
    source: string; // API 路径或函数名
    params?: Record<string, any>; // 参数
    labelKey?: string; // 选项标签键名
    valueKey?: string; // 选项值键名
  };
}

// 选择字段可以是静态选项或动态选项
// 用法示例:
// 1. 静态选项:
// {
//   type: FieldType.SELECT,
//   label: "状态",
//   key: "status",
//   options: [
//     { label: "启用", value: "enabled" },
//     { label: "禁用", value: "disabled" }
//   ]
// }
//
// 2. 动态选项:
// {
//   type: FieldType.SELECT,
//   label: "零件",
//   key: "config.component",
//   dynamicOptions: {
//     type: "function",
//     source: "getSubComponents",
//     labelKey: "label",
//     valueKey: "value"
//   }
// }
export type SelectField = StaticSelectField | DynamicSelectField;

// 开关字段
export interface SwitchField extends BaseField {
  type: FieldType.SWITCH;
  activeText?: string; // 开启时的文字
  inactiveText?: string; // 关闭时的文字
  activeValue?: any; // 开启时的值
  inactiveValue?: any; // 关闭时的值
}

// 复选框字段
export interface CheckboxField extends BaseField {
  type: FieldType.CHECKBOX;
  options: Array<{
    // 选项
    label: string; // 选项标签
    value: any; // 选项值
    disabled?: boolean; // 是否禁用
  }>;
}

// 单选框字段
export interface RadioField extends BaseField {
  type: FieldType.RADIO;
  options: Array<{
    // 选项
    label: string; // 选项标签
    value: any; // 选项值
    disabled?: boolean; // 是否禁用
  }>;
}

// 变量引用字段
export interface VariableReferenceField extends BaseField {
  type: FieldType.VARIABLE_REFERENCE;
  paramType: "static" | "dynamic"; // 参数类型
  allowFunctionParams?: boolean; // 是否允许函数参数
}

// 变量列表字段
export interface VariableListField extends BaseField {
  type: FieldType.VARIABLE_LIST;
  itemConfig: {
    // 变量项配置
    fields: SchemaField[]; // 变量项字段列表
  };
}

// 自定义组件字段
export interface ComponentField extends BaseField {
  type: FieldType.COMPONENT;
  component: string; // 组件名
  props?: Record<string, any>; // 组件属性
}

// 字段类型联合
export type SchemaField =
  | TextField
  | TextareaField
  | NumberField
  | SelectField
  | SwitchField
  | CheckboxField
  | RadioField
  | VariableReferenceField
  | VariableListField
  | ComponentField;

export interface NodeSchema {
  name: string; // 节点名字
  description?: string;
  fields: SchemaField[];
  icon?: string; // 节点图标
  color?: string; // 节点颜色
  inputs?: {
    // 节点输入连接
    name: string;
    label?: string;
    multiple?: boolean;
    type?: string;
  }[];
  outputs?: {
    // 节点输出连接
    name: string;
    label?: string;
    multiple?: boolean;
    type?: string;
  }[];
}

// 节点 Schema 集合接口
export interface NodeSchemas {
  [key: string]: NodeSchema;
}
