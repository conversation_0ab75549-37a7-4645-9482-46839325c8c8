/* 🧬 量子级微物理动效系统 */
/* 基于真实物理学原理的高品质动效 */

/* ========================================
   核心 Avatar 量子动效
   ======================================== */

.avatar-quantum {
  @apply w-44 h-auto mx-auto mb-6 cursor-pointer;
  @apply transition-all duration-500 ease-out;
  @apply transform-gpu origin-center;
  
  /* 3D 变换上下文 */
  transform-style: preserve-3d;
  perspective: 1000px;
  
  /* 基础量子状态 */
  position: relative;
  border-radius: 50%;
  overflow: visible;
  
  /* 量子呼吸动画 */
  animation: quantum-breathing 4s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  
  /* 基础滤镜效果 */
  filter: 
    drop-shadow(0 4px 12px rgba(59, 130, 246, 0.3)) 
    brightness(1) 
    blur(0px);
}

/* 量子场效应 - 第一层光环 */
.avatar-quantum::before {
  content: "";
  position: absolute;
  top: -15%;
  left: -15%;
  right: -15%;
  bottom: -15%;
  border-radius: 50%;
  z-index: -1;
  
  /* 量子场渐变 */
  background: radial-gradient(
    circle,
    rgba(59, 130, 246, 0.15) 0%,
    rgba(59, 130, 246, 0.08) 30%,
    rgba(147, 197, 253, 0.05) 60%,
    transparent 80%
  );
  
  /* 量子场动画 */
  animation: quantum-field-oscillation 3s ease-in-out infinite;
}

/* 量子粒子效应 - 第二层能量 */
.avatar-quantum::after {
  content: "";
  position: absolute;
  top: -8%;
  left: -8%;
  right: -8%;
  bottom: -8%;
  border-radius: 50%;
  z-index: -1;
  
  /* 粒子场渐变 */
  background: 
    radial-gradient(
      circle at 25% 25%,
      rgba(59, 130, 246, 0.2) 0%,
      transparent 40%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(147, 197, 253, 0.15) 0%,
      transparent 35%
    ),
    radial-gradient(
      circle at 50% 80%,
      rgba(99, 102, 241, 0.1) 0%,
      transparent 30%
    );
  
  /* 粒子动画 */
  animation: quantum-particle-dance 2.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* ========================================
   交互状态动效
   ======================================== */

/* 悬停状态 - 量子纠缠效应 */
.avatar-quantum:hover {
  animation: quantum-entanglement 0.8s cubic-bezier(0.68, -0.6, 0.32, 1.6) forwards;
}

.avatar-quantum:hover::before {
  animation: quantum-field-amplification 0.8s cubic-bezier(0.68, -0.6, 0.32, 1.6) forwards;
}

.avatar-quantum:hover::after {
  animation: quantum-particle-burst 0.8s cubic-bezier(0.68, -0.6, 0.32, 1.6) forwards;
}

/* 点击状态 - 量子叠加态 */
.avatar-quantum:active {
  animation: quantum-superposition 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 聚焦状态 - 光子发射 */
.avatar-quantum:focus {
  @apply outline-2 outline-blue-400/50 outline-offset-4;
  animation: photon-emission 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* ========================================
   量子动画关键帧定义
   ======================================== */

/* 量子呼吸 - 基础生命状态 */
@keyframes quantum-breathing {
  0% {
    transform: scale(1) translateZ(0) rotateX(0deg);
    filter: 
      drop-shadow(0 4px 12px rgba(59, 130, 246, 0.3)) 
      brightness(1) 
      blur(0px);
    opacity: 1;
  }
  25% {
    transform: scale(1.008) translateZ(2px) rotateX(0.5deg);
    filter: 
      drop-shadow(0 6px 18px rgba(59, 130, 246, 0.4)) 
      brightness(1.03) 
      blur(0.2px);
    opacity: 0.98;
  }
  50% {
    transform: scale(1.012) translateZ(3px) rotateX(0deg);
    filter: 
      drop-shadow(0 8px 24px rgba(59, 130, 246, 0.5)) 
      brightness(1.05) 
      blur(0.3px);
    opacity: 0.95;
  }
  75% {
    transform: scale(1.005) translateZ(1px) rotateX(-0.3deg);
    filter: 
      drop-shadow(0 5px 15px rgba(59, 130, 246, 0.35)) 
      brightness(1.02) 
      blur(0.1px);
    opacity: 0.97;
  }
  100% {
    transform: scale(1) translateZ(0) rotateX(0deg);
    filter: 
      drop-shadow(0 4px 12px rgba(59, 130, 246, 0.3)) 
      brightness(1) 
      blur(0px);
    opacity: 1;
  }
}

/* 量子场振荡 */
@keyframes quantum-field-oscillation {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.6;
  }
  33% {
    transform: scale(1.05) rotate(1deg);
    opacity: 0.8;
  }
  66% {
    transform: scale(0.98) rotate(-0.5deg);
    opacity: 0.7;
  }
}

/* 量子粒子舞蹈 */
@keyframes quantum-particle-dance {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.4;
  }
  20% {
    transform: scale(1.02) rotate(2deg);
    opacity: 0.6;
  }
  40% {
    transform: scale(0.99) rotate(-1deg);
    opacity: 0.5;
  }
  60% {
    transform: scale(1.01) rotate(1.5deg);
    opacity: 0.7;
  }
  80% {
    transform: scale(1.005) rotate(-0.5deg);
    opacity: 0.55;
  }
}

/* 量子纠缠 - 悬停效应 */
@keyframes quantum-entanglement {
  0% {
    transform: scale(1) rotate(0deg) skew(0deg, 0deg);
    filter: 
      drop-shadow(0 4px 12px rgba(59, 130, 246, 0.3)) 
      brightness(1) 
      hue-rotate(0deg);
    border-radius: 50%;
  }
  15% {
    transform: scale(0.95) rotate(2deg) skew(1deg, 0deg);
    filter: 
      drop-shadow(0 2px 8px rgba(59, 130, 246, 0.2)) 
      brightness(0.9) 
      hue-rotate(5deg);
    border-radius: 45%;
  }
  30% {
    transform: scale(1.15) rotate(-1deg) skew(-0.5deg, 1deg);
    filter: 
      drop-shadow(0 16px 40px rgba(59, 130, 246, 0.8)) 
      brightness(1.3) 
      hue-rotate(15deg);
    border-radius: 60%;
  }
  50% {
    transform: scale(1.25) rotate(3deg) skew(1.5deg, -1deg);
    filter: 
      drop-shadow(0 24px 60px rgba(59, 130, 246, 0.9)) 
      brightness(1.5) 
      hue-rotate(25deg);
    border-radius: 40%;
  }
  70% {
    transform: scale(1.18) rotate(-0.5deg) skew(-0.8deg, 0.5deg);
    filter: 
      drop-shadow(0 20px 50px rgba(59, 130, 246, 0.85)) 
      brightness(1.35) 
      hue-rotate(20deg);
    border-radius: 55%;
  }
  100% {
    transform: scale(1.12) rotate(0deg) skew(0deg, 0deg);
    filter: 
      drop-shadow(0 18px 45px rgba(59, 130, 246, 0.75)) 
      brightness(1.25) 
      hue-rotate(12deg);
    border-radius: 50%;
  }
}

/* 量子场放大 */
@keyframes quantum-field-amplification {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.3) rotate(5deg);
    opacity: 0.9;
  }
  100% {
    transform: scale(1.2) rotate(2deg);
    opacity: 0.8;
  }
}

/* 量子粒子爆发 */
@keyframes quantum-particle-burst {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 0.4;
  }
  30% {
    transform: scale(1.5) rotate(10deg);
    opacity: 0.8;
  }
  100% {
    transform: scale(1.3) rotate(5deg);
    opacity: 0.6;
  }
}

/* 量子叠加态 - 点击效应 */
@keyframes quantum-superposition {
  0% {
    transform: scale(1) translateX(0px) translateY(0px);
    filter: drop-shadow(0 4px 12px rgba(59, 130, 246, 0.3)) brightness(1);
    opacity: 1;
  }
  20% {
    transform: scale(1.05) translateX(2px) translateY(-1px);
    filter: drop-shadow(0 8px 20px rgba(59, 130, 246, 0.5)) brightness(1.1);
    opacity: 0.9;
  }
  40% {
    transform: scale(0.98) translateX(-1px) translateY(2px);
    filter: drop-shadow(0 6px 16px rgba(59, 130, 246, 0.4)) brightness(0.95);
    opacity: 0.8;
  }
  60% {
    transform: scale(1.08) translateX(1px) translateY(-2px);
    filter: drop-shadow(0 12px 28px rgba(59, 130, 246, 0.6)) brightness(1.15);
    opacity: 0.85;
  }
  80% {
    transform: scale(1.02) translateX(-0.5px) translateY(1px);
    filter: drop-shadow(0 10px 24px rgba(59, 130, 246, 0.55)) brightness(1.08);
    opacity: 0.9;
  }
  100% {
    transform: scale(1.06) translateX(0px) translateY(0px);
    filter: drop-shadow(0 14px 32px rgba(59, 130, 246, 0.65)) brightness(1.12);
    opacity: 0.95;
  }
}

/* 光子发射 - 聚焦效应 */
@keyframes photon-emission {
  0% {
    filter: 
      drop-shadow(0 4px 12px rgba(59, 130, 246, 0.3)) 
      brightness(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
  50% {
    filter: 
      drop-shadow(0 12px 32px rgba(59, 130, 246, 0.6)) 
      brightness(1.2);
    box-shadow: 0 0 30px 8px rgba(59, 130, 246, 0.4);
  }
  100% {
    filter: 
      drop-shadow(0 8px 24px rgba(59, 130, 246, 0.5)) 
      brightness(1.15);
    box-shadow: 0 0 20px 5px rgba(59, 130, 246, 0.3);
  }
}
