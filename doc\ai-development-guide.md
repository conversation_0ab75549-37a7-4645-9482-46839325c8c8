# Project: part-library (Vue 3 Admin Template)

**Core Tech:** Vue 3 (`<script setup>`, Composition API), Vite, TypeScript, Element Plus, Tailwind CSS, SCSS, Pinia, Vue Router, Axios.

**Key Directories & Purpose:**

- `src/api`: API request functions (organized by feature, uses `src/utils/http`).
- `src/store/modules`: Pinia state management modules.
- `src/router/modules`: Vue Router route definitions (lazy-loaded).
- `src/utils`: Utility functions (esp. `http`, `auth`, `message`).
- `src/views`: Page components.
- `src/components`: Reusable UI components.
- `src/hooks`: Custom composition functions.
- `src/styles`: Global styles & SCSS.
- `src/layouts`: Layout components.

**Key Dependencies:** `@pureadmin/*`, `@vueuse/core`, `dayjs`, `lodash-es`, `mitt`, `nprogress`, `echarts`, `@vue-flow/*`.

**AI Development Guidelines & Best Practices:**

1.  **Strict Tech Stack:** Adhere to the core tech stack.
2.  **Code Standards & Quality:**

    - **Linting & Formatting:** Code **must** strictly adhere to the rules defined in:
      - ESLint: `eslint.config.js` (using Flat Config format)
      - Prettier: `.prettierrc.js`
      - Stylelint: `stylelint.config.js`
      - **Key Prettier Rules:** Double quotes (`"`), bracket spacing, no trailing comma, avoid arrow parens.
    - **Commit Messages:** **Must** follow conventional commit format, enforced by `commitlint` (config: `commitlint.config.js`).
    - **Pre-commit Hooks:** `husky` and `lint-staged` (config: `.lintstagedrc`) automatically run linters and formatters before commits. Ensure commits pass these checks.
    - **IDE Integration:** **Strongly recommend** enabling ESLint, Prettier, and Stylelint plugins in your IDE for real-time feedback and auto-fixing.
    - **Unused Variables/Arguments:** Prefix unused variables and function arguments with an underscore (`_`) to satisfy the `no-unused-vars` ESLint rule (`varsIgnorePattern: "^_"`).
    - **Package Manager:** **Must** use `pnpm` for installing and managing dependencies.
    - **Code Comments:** Provide JSDoc comments for public/shared Composables, utility functions, and complex component Props/Emits. Add inline comments for non-obvious logic blocks. Avoid commenting on self-explanatory code.

3.  **Directory Structure:** Use the defined project structure.
4.  **API Calls (Axios) - Project Best Practices:**

    - **Must Use Encapsulated Instance:** All API calls **must** use the exported `http` instance from `src/utils/http/index.ts`. Do **not** use raw `axios`. This ensures consistent interceptors (auth, token refresh, progress bar, data handling, error handling).
    - **Prefer Helper Methods:** Use specific methods like `http.get<T>()`, `http.post<T, P>()`, `http.put<T, P>()`, etc., for clarity and better type support.
    - **Specify Response Type:** **Always** provide the expected response data type `T` as a generic argument (e.g., `http.get<User[]>('/api/users')`).
    - **Understand Default Data Extraction:** By default, successful (200 OK) responses return **only the content of `response.data.Attach` or `response.data.attach`**.
    - **Getting Raw Response Data:** If the full `response.data` object is needed, pass `{ returnRawData: true }` in the request configuration (last argument).
      ```typescript
      // Example: Get raw data
      const rawData = await http.get<RawUserData>("/api/profile", undefined, {
        returnRawData: true
      });
      ```
    - **Use Async/Await and Try/Catch:** Handle API calls with `async/await` and wrap them in `try/catch` blocks to manage asynchronous operations and errors gracefully.
    - **Specify Request Payload Type (P):** For methods like `post`, `put`, specify the request payload type `P` as the second generic argument if applicable (e.g., `http.post<ApiResponse, UserInput>('/api/users', { data: newUser })`).
    - **Custom Configuration:** Pass specific Axios configurations (like `timeout`, `headers`, or `returnRawData`) as the **last argument** to the helper methods when needed.
      ```typescript
      // Example: Custom timeout
      await http.get<Data>("/api/slow", undefined, { timeout: 20000 });
      ```

5.  **State Management (Pinia):**

    - Use Pinia modules in `src/store/modules` for global state.
    - Use `getters` for computed state.
    - Use `actions` for mutations and asynchronous operations (side effects).
    - Leverage Pinia's TypeScript support for type safety.
    - **Accessing Stores:** Stores can be accessed directly within component `setup` functions or `computed` properties by calling the store's hook (e.g., `const userStore = useUserStore();`).

6.  **Routing (Vue Router):**

    - Define lazy-loaded routes in `src/router/modules` using dynamic `import()`.
    - Use named routes for better maintainability.
    - Implement route guards for authentication/authorization (check `src/router/index.ts`).
    - Utilize route `meta` fields for additional information (e.g., permissions, title).

7.  **Styling (Tailwind CSS & SCSS):**

    - **Prioritize** Tailwind CSS utility classes for styling.
    - Use SCSS (in `src/styles` or component `<style lang="scss">`) for complex styles, reusable mixins, or when utility classes are insufficient.
    - Use Element Plus SCSS variables for theme customization (check Element Plus docs and project theme setup).
    - Consider using `@apply` in SCSS sparingly to leverage Tailwind utilities when appropriate.
    - Implement responsive design using Tailwind's modifiers (e.g., `md:`, `lg:`).
    - **Style Linting:** Stylelint rules are defined in `stylelint.config.js`. It integrates with Prettier and extends standard CSS/SCSS/Vue rules. Note:
      - It's configured to understand Vue (`:deep`) and Tailwind/SCSS syntax (`@apply`, `@mixin`).
      - Rules for specific class/SCSS variable naming patterns are currently disabled (`null`).
      - CSS property declaration order is enforced, but only as a `warning`.
    - **Organizing Classes:** For complex components, consider using `computed` properties within the `setup` function to group related Tailwind classes for better readability:
      ```typescript
      const buttonClasses = computed(() => [
        "px-4",
        "py-2",
        "rounded",
        isActive.value ? "bg-blue-500 text-white" : "bg-gray-200 text-gray-800"
      ]);
      ```

8.  **UI Components (Element Plus):**

    - **Prioritize** using official Element Plus components for UI consistency.
    - Use Element Plus icons for consistent iconography.
    - Implement form validation using Element Plus form capabilities.

9.  **TypeScript (Configuration & Best Practices):**

    - **Configuration Context:**
      - The primary TypeScript configuration is in `tsconfig.json`.
      - **Crucially, `compilerOptions.strict` is set to `false`.** This means many strict type checks (like `strictNullChecks`, `noImplicitAny`) are **disabled** by default in the TS compiler itself.
      - ESLint (`eslint.config.js`) uses `@typescript-eslint/eslint-plugin` to provide additional TS-specific linting.
    - **Best Practices & Linting Enforcement:**
      - **Avoid `any` (Strong Recommendation):** While `any` can be useful occasionally, strive for precise types whenever possible. **Note:** The ESLint rule `@typescript-eslint/no-explicit-any` is currently **disabled (`"off"`)** in `eslint.config.js`, so its avoidance is a recommended practice rather than a strictly enforced rule.
      - **Non-Null Assertion (`!`):** Use the non-null assertion operator (`!`) sparingly and only when you are absolutely certain a value is not null or undefined. ESLint rule `@typescript-eslint/no-non-null-assertion` is currently **disabled (`"off"`)**.
      - **Explicit Return Types:** Explicitly defining function return types is good practice for clarity, but not strictly enforced by ESLint (`@typescript-eslint/explicit-module-boundary-types` is **`"off"`**).
      - **Prefer `interface` over `type`:** Use `interface` for defining object shapes or implementing classes, and `type` for unions, intersections, or complex types.
      - **Type Imports:** Use inline type imports (enforced by `@typescript-eslint/consistent-type-imports`):


          ```typescript
          import { type User } from './types';
          ```
      - **Generics (`<T>`):** Use generics for reusable, type-safe functions and components.
      - **Type Guards:** Implement type guards (`isXxx` functions) for runtime type safety where needed.
      - **Component Props:** Define props precisely using `PropType<SpecificType>` and interfaces/types. Avoid `PropType<any>`.
    - **Path Aliases:** The alias `@/*` maps to `src/*` (defined in `tsconfig.json` and `vite.config.ts`).
    - **Global Types:** Common global types (like `Recordable`, `Nullable`) are likely defined in the `types/` directory and registered in `tsconfig.json` (`compilerOptions.types`) and `eslint.config.js` (`languageOptions.globals`).

10. **Vue 3:**

    - **Must** use Composition API. Choose either:
      - **SFC:** `<script setup>` syntax (Preferred for most components).
      - **TSX:** `defineComponent` + `setup` function + JSX (Suitable for complex render logic or JSX preference).
    - Avoid Options API.
    - Utilize Vue 3 features like `ref`, `reactive`, `computed`, `watch`, lifecycle hooks within `setup`.
    - Refer to the ESLint config (`eslint.config.js`) for Vue-specific linting rules (based on `vue/vue3-recommended` with overrides like `vue/multi-word-component-names: "off"`, `vue/require-explicit-emits: "off"`).

11. **Vite:**

    - Main configuration is in `vite.config.ts`.
    - Uses helper modules in the `build/` directory (`plugins`, `utils`, `optimize`).
    - Key environment variables (from `.env` files) include `VITE_PUBLIC_PATH`, `VITE_PORT`, `VITE_CDN`, `VITE_COMPRESSION`.
    - Defines path aliases (e.g., `@/*`).
    - Leverage dynamic imports (`import()`) for code splitting and lazy loading (especially for routes).
    - Includes a placeholder for development server API proxy configuration (`server.proxy` in `vite.config.ts`).
    - Utilize relevant Vite plugins (check `build/plugins.ts`, `vite.config.ts`, and `package.json`).

12. **Utilities:** Leverage existing utils (`@vueuse/core`, `@pureadmin/*`, `lodash-es`, `dayjs`, `mitt`, `vue-tippy`, etc.) before creating new ones.

13. **Component Writing Styles:**

    - The project accommodates two primary component writing styles:
      - **`.vue` Single File Components (SFC):** Using `<template>` and `<script setup>`. This is the standard and often preferred approach for typical Vue components.
      - **`.tsx` Files:** Using `defineComponent`, the `setup` function, and JSX for the template structure. This style is suitable when:
        - Dealing with complex rendering logic that might be more concise in JSX.
        - Developers have a strong preference for JSX syntax.
        - Leveraging TypeScript's full power within the template structure.
    - Consistency within a feature or module is encouraged, but mixing styles across the project is acceptable.

14. **Common Patterns & Practices:**

    - **Props Handling:** When a component needs to modify data derived from props for its internal state, use `cloneDeep` (from `@pureadmin/utils` or `lodash-es`) to create a deep copy of the prop value within the `setup` function. Avoid directly mutating props.
    - **Component Encapsulation (Slots):** Utilize Vue's slot mechanism (`<slot>`, named slots, scoped slots) to create flexible and reusable components. Pass data or state down to slots if needed (e.g., `slots.default({ internalState })`).
    - **Icons:** Two primary methods are used:
      - **Iconify:** Often used via a wrapper component like `<iconifyIconOffline>` (check specific implementation).
      - **Local SVG:** Import SVG files directly as Vue components using the `?component` suffix (e.g., `import MyIcon from '@/assets/my-icon.svg?component';`).
    - **Refs in JSX:** When using `.tsx`, refs to elements or components are typically accessed via `instance?.proxy?.$refs` within the `setup` function, often using string refs (e.g., `ref="myRef"`). This differs from template refs in SFCs and offers less type safety.
