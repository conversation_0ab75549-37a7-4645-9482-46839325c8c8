# 零件库页面开发分析

## 1. 零件库页面布局

根据原型图，零件库页面主要包含以下几个部分：

1. **顶部搜索和操作区**：

   - 搜索框：用于搜索零件
   - 刷新按钮：刷新零件列表
   - 新建按钮：创建新的零件

2. **左侧零件树/列表**：

   - 显示所有零件类型（如Robot, Robot A, LoadPort, Buffer, Chamber, EFEM等）
   - 每个零件类型旁有添加按钮，可以添加该类型的新零件

3. **右侧主内容区**：

   - 属性管理：显示和编辑零件的基本属性
   - 子零件管理：管理零件的子零件
   - 选择条件：设置零件的选择条件
   - 功能列表：管理零件的功能
   - 报警管理：管理零件的报警信息

4. **操作按钮区**：
   - 添加属性、添加子零件、添加功能等操作按钮

## 2. 零件的属性和规则

根据零件相关DTO，零件具有以下主要属性和规则：

### 2.1 基础信息 (ComponentBasicInfoDto)

- guid：唯一标识符
- 名/类型：零件名称或类型
- 版本号：零件版本
- 版本说明：版本的详细说明
- 描述：零件描述
- 标签列表：零件的标签
- 创建时间：零件创建的时间
- 作者：零件的创建者

### 2.2 属性列表 (ComponentAttributeDto)

- 名：属性名
- 显示名称：属性的显示名称
- 监听间隔：属性监听的时间间隔，<=0不启动监听
- 值类型：属性的数据类型（Int, string, bool, array, object）
- 描述：属性描述
- 单位：属性单位
- 默认值：属性的默认值
- 限制规则：
  - 上限：最大值
  - 下限：最小值
  - 长度：字符长度
  - 正则：正则表达式验证
  - 小数位数：小数点后位数

### 2.3 子零件列表 (SubcomponentInfoDto)

- 名：在主零件中子零件的名称
- 零件类型名：子零件的实际名称
- 零件类型版本：子零件的版本
- 参数传递规则：
  - 同名继承
  - 自定义规则
- 属性传递规则列表：
  - 当前零件属性
  - 子零件属性
  - 值传递方式（固定值、继承值、计算值）

### 2.4 报警列表 (ComponentAlarmDto)

- ID：报警ID
- 名：报警名称
- 描述：报警描述
- 等级：报警等级
- 可用处理方式：权限选择

### 2.5 功能列表 (ComponentFunctionDto)

- 名：功能名称
- 输入参数描述列表：来自流程列表中的Start
  - 入参：参数名、参数类型/值类型、参数注释
- 描述：功能描述
- 流程列表：
  - 单节点：节点ID、节点备注、节点基础功能配置、上一个节点列表、下一个节点列表

### 2.6 渲染条件列表

- 属性名称：条件关联的属性
- 条件：等于、大于、小于、不等于、包含、不包含
- 判断值：条件判断的值
- 渲染资源：满足条件时的渲染资源
- 子条件：和、或（复合条件）

## 3. 零件管理页面的功能

根据原型图和DTO结构，零件管理页面应具有以下功能：

1. **零件基本操作**：

   - 创建新零件
   - 查看零件列表
   - 搜索零件
   - 刷新零件列表

2. **属性管理**：

   - 添加属性
   - 编辑属性（名称、类型、限制规则等）
   - 删除属性
   - 设置属性监听

3. **子零件管理**：

   - 添加子零件
   - 设置子零件参数传递规则
   - 编辑子零件属性传递规则
   - 删除子零件

4. **功能管理**：

   - 添加功能
   - 设置功能参数
   - 编辑功能流程
   - 删除功能

5. **报警管理**：

   - 添加报警
   - 设置报警等级和处理方式
   - 编辑报警信息
   - 删除报警

6. **渲染条件管理**：
   - 设置渲染条件
   - 编辑条件规则
   - 设置渲染资源
   - 管理复合条件

## 4. 开发计划

1. 创建零件库页面的基本结构
2. 实现零件列表组件
3. 实现零件基本信息编辑组件
4. 实现属性管理组件
5. 实现子零件管理组件
6. 实现功能管理组件
7. 实现报警管理组件
8. 实现渲染条件管理组件
9. 整合所有组件并完善页面交互
10. 进行代码质量检查和优化

http://192.168.0.212:6888/swagger/index.html
