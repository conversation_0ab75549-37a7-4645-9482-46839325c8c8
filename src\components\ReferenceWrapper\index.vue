<template>
  <div class="reference-wrapper">
    <div class="reference-toggle">
      <el-switch
        v-model="isReference"
        active-text="引用值"
        inactive-text="静态值"
        @change="handleModeChange"
      />
    </div>
    <div class="reference-content">
      <!-- 引用模式 -->
      <template v-if="isReference">
        <el-select
          v-model="referenceValue"
          filterable
          :placeholder="'选择引用参数'"
          class="reference-select"
          @change="handleReferenceChange"
        >
          <el-option-group
            v-for="node in previousNodes"
            :key="node.id"
            :label="node.name"
          >
            <el-option
              v-for="param in node.outputParams"
              :key="`${node.id}-${param.name}`"
              :label="`${param.name} (${param.type || '未知类型'})`"
              :value="`${node.id}.${param.name}`"
            />
          </el-option-group>
        </el-select>
      </template>

      <!-- 静态值模式 - 显示原始表单项 -->
      <template v-else>
        <slot />
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from "vue";
import { useVueFlow } from "@vue-flow/core";

// 定义组件的 props
const props = defineProps({
  // 表单值
  modelValue: {
    type: [String, Number, Object, Array, Boolean],
    default: ""
  },
  // 当前节点ID
  nodeId: {
    type: String,
    default: ""
  },
  // 字段名
  field: {
    type: String,
    default: ""
  },
  // FormCreate 注入的属性
  formCreateInject: {
    type: Object,
    default: () => ({})
  }
});

// 定义组件的事件
const emit = defineEmits(["update:modelValue", "change"]);

// 本地状态
const isReference = ref(false);
const referenceValue = ref("");
const staticValue = ref(props.modelValue);

// 获取 Vue Flow 实例
const { getNodes } = useVueFlow();

// 获取所有之前的节点及其输出参数
const previousNodes = computed(() => {
  const nodes = getNodes.value || [];

  if (!props.nodeId) return [];

  // 找到当前节点之前的所有节点
  return nodes
    .filter(node => {
      // 过滤掉当前节点和没有输出参数的节点
      if (node.id === props.nodeId) return false;
      if (!node.data || !node.data.outputParams) return false;
      return true;
    })
    .map(node => ({
      id: node.id,
      name: node.data.name || node.id,
      outputParams: node.data.outputParams || []
    }));
});

// 初始化组件状态
onMounted(() => {
  initializeFromValue(props.modelValue);
});

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  newValue => {
    if (isReference.value) return; // 如果是引用模式，不更新静态值
    initializeFromValue(newValue);
  }
);

// 根据值初始化组件状态
const initializeFromValue = (value: any) => {
  const strValue = String(value || "");

  // 检查是否为引用格式 (例如 "ref:nodeId.paramName")
  if (strValue.startsWith("ref:")) {
    isReference.value = true;
    referenceValue.value = strValue.substring(4); // 移除 "ref:" 前缀
    staticValue.value = "";
  } else {
    isReference.value = false;
    staticValue.value = value;
    referenceValue.value = "";
  }
};

// 处理模式切换
const handleModeChange = (isRef: boolean) => {
  if (isRef) {
    // 切换到引用模式，如果已有引用值则使用，否则清空
    if (referenceValue.value) {
      const refValue = `ref:${referenceValue.value}`;
      emit("update:modelValue", refValue);
      emit("change", refValue);
    } else {
      emit("update:modelValue", "");
      emit("change", "");
    }
  } else {
    // 切换到静态模式，使用之前保存的静态值
    emit("update:modelValue", staticValue.value);
    emit("change", staticValue.value);
  }
};

// 处理引用值选择
const handleReferenceChange = (value: string) => {
  const refValue = `ref:${value}`;
  emit("update:modelValue", refValue);
  emit("change", refValue);
};
</script>

<style scoped>
.reference-wrapper {
  width: 100%;
}

.reference-toggle {
  margin-bottom: 8px;
  display: flex;
  justify-content: flex-end;
}

.reference-content {
  width: 100%;
}

.reference-select {
  width: 100%;
}
</style>
