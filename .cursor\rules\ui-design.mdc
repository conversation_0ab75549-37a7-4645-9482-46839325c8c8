---
description:
globs:
alwaysApply: false
---
- 使用Element Plus作为UI组件库基础，保持视觉一致性
- 颜色系统应遵循Element Plus主题，避免自定义颜色扰乱系统
- 使用Tailwind CSS进行布局和样式增强，提高开发效率
- 自定义组件应继承Element Plus的设计语言，保持体验一致
- 表单设计遵循一致的布局和对齐规则，提高可读性
- 使用响应式设计支持不同设备尺寸，布局应流畅适应
- 图标应优先使用Element Plus提供的图标系统，保持统一
- 对话框、抽屉和弹出提示应使用统一的交互模式
- 表格设计应考虑大数据展示和性能优化
- 数据可视化组件应选择合适的图表类型表达数据关系
- 加载状态应提供明确的视觉反馈，避免用户困惑
- 表单验证应提供及时清晰的错误提示
- 动效设计应适度，增强体验但不干扰操作
