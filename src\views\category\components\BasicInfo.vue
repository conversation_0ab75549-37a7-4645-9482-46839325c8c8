<template>
  <div class="basic-info">
    <div class="header flex justify-between items-center mb-4">
      <h2 class="text-lg font-medium">基础信息</h2>
      <div>
        <el-button v-if="!isEditing" type="primary" @click="handleEdit">
          编辑信息
        </el-button>
        <template v-else>
          <el-button @click="cancelEdit">取消</el-button>
          <el-button type="primary" @click="saveBasicInfo">保存</el-button>
        </template>
      </div>
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      :disabled="!isEditing"
    >
      <el-form-item label="ID">
        <el-input v-model="formData.id" disabled />
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name" />
      </el-form-item>
      <el-form-item label="版本号" prop="version">
        <el-input v-model="formData.version" />
      </el-form-item>
      <el-form-item label="版本说明">
        <el-input
          v-model="formData.versionDescription"
          type="textarea"
          :rows="2"
        />
      </el-form-item>
      <el-form-item label="描述">
        <el-input v-model="formData.description" type="textarea" :rows="3" />
      </el-form-item>
      <el-form-item label="标签">
        <template v-for="tag in formData.tags" :key="tag">
          <el-tag
            v-if="isEditing"
            closable
            :disable-transitions="false"
            class="mr-1"
            @close="handleRemoveTag(tag)"
          >
            {{ tag }}
          </el-tag>
          <el-tag v-else class="mr-1">
            {{ tag }}
          </el-tag>
        </template>
        <el-input
          v-if="inputTagVisible"
          ref="tagInputRef"
          v-model="inputTagValue"
          class="w-80 mt-1"
          size="small"
          @keyup.enter="handleInputConfirm"
          @blur="handleInputConfirm"
        />
        <el-button v-else-if="isEditing" size="small" @click="showTagInput">
          + 添加标签
        </el-button>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-input v-model="formData.createTime" disabled />
      </el-form-item>
      <el-form-item label="作者">
        <el-input v-model="formData.author" disabled />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, watch, computed } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import type { ComponentBasicInfoDto } from "@/types/componentType";
import { useComponentCategory } from "@/store/modules/componentCategory";

// Pinia store
const componentStore = useComponentCategory();

// 获取basicInfo
const storeBasicInfo = computed(
  () => componentStore.selectedComponent?.basicInfo || {}
);

// 表单引用
const formRef = ref<FormInstance>();

// 是否处于编辑状态
const isEditing = ref(false);

// 表单验证规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: "请输入名称/类型", trigger: "blur" },
    { min: 1, max: 50, message: "长度应在1到50个字符之间", trigger: "blur" }
  ],
  version: [{ required: true, message: "请输入版本号", trigger: "blur" }]
});

// 表单数据
const formData = reactive<ComponentBasicInfoDto>({
  name: "",
  version: "",
  description: "",
  versionDescription: "",
  tags: [],
  createTime: "",
  author: ""
});

// 标签输入相关
const inputTagVisible = ref(false);
const inputTagValue = ref("");
const tagInputRef = ref<HTMLInputElement>();

// 监听store中basicInfo变化，更新表单数据
watch(
  storeBasicInfo,
  newVal => {
    if (newVal) {
      Object.assign(formData, newVal);
    }
  },
  { immediate: true, deep: true }
);

// 处理编辑按钮点击
const handleEdit = () => {
  isEditing.value = true;
};

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false;
  // 重置表单数据
  Object.assign(formData, storeBasicInfo.value);
};

// 保存基本信息
const saveBasicInfo = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate(async valid => {
      if (valid) {
        debugger;
        // 使用Pinia store更新组件
        const updateResponse = await componentStore.updateCurrentComponent({
          basicInfo: formData
        });

        if (updateResponse.success) {
          ElMessage.success("保存成功");
          isEditing.value = false;
        } else {
          ElMessage.error(`保存失败: ${updateResponse.message}`);
        }
      }
    });
  } catch (error) {
    ElMessage.error("保存失败");
    console.error("保存基本信息失败:", error);
  }
};

// 显示标签输入框
const showTagInput = () => {
  inputTagVisible.value = true;
  nextTick(() => {
    tagInputRef.value?.focus();
  });
};

// 处理标签输入确认
const handleInputConfirm = () => {
  if (inputTagValue.value) {
    if (!formData.tags.includes(inputTagValue.value)) {
      formData.tags.push(inputTagValue.value);
    }
  }
  inputTagVisible.value = false;
  inputTagValue.value = "";
};

// 处理移除标签
const handleRemoveTag = (tag: string) => {
  formData.tags = formData.tags.filter(item => item !== tag);
};
</script>

<style scoped lang="scss">
.basic-info {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}
</style>
