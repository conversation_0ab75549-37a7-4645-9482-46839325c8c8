<template>
  <div class="render-condition-manager">
    <!-- 组件绑定区域 -->
    <div
      class="component-binding-area mb-6 p-4 bg-gray-50 rounded-md border border-gray-200"
    >
      <h2 class="text-lg font-medium mb-3">组件绑定</h2>
      <div class="flex items-center gap-3">
        <el-input
          v-model="componentName"
          placeholder="请输入组件名称"
          class="flex-1"
          clearable
        >
          <template #prefix>
            <el-icon><Shop /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="handleBindComponent">
          <el-icon class="mr-1"><Link /></el-icon>绑定组件
        </el-button>
      </div>
    </div>
    <div class="header flex justify-between items-center mb-4">
      <h2 class="text-lg font-medium">渲染条件管理</h2>
      <div class="flex items-center gap-2">
        <el-button type="primary" @click="handleAddCondition">
          <el-icon class="mr-1"><Plus /></el-icon>添加条件
        </el-button>
      </div>
    </div>

    <el-table :data="renderConditions" border style="width: 100%">
      <el-table-column prop="attributeName" label="属性名称" min-width="120" />
      <el-table-column prop="condition" label="条件" min-width="100">
        <template #default="{ row }">
          <el-tag>{{ getConditionText(row.condition) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="valueType" label="值类型" min-width="80">
        <template #default="{ row }">
          <el-tag type="info">{{ getValueTypeText(row.valueType) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="value" label="判断值" min-width="120" />
      <el-table-column
        prop="renderResource"
        label="渲染资源"
        min-width="150"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div
            v-if="row.renderResource && row.renderResource.startsWith('http')"
            class="flex items-center"
          >
            <el-image
              style="width: 40px; height: 40px"
              :src="row.renderResource"
              fit="cover"
              :preview-src-list="[row.renderResource]"
            />
            <span class="ml-2 truncate">{{ row.renderResource }}</span>
          </div>
          <span v-else>{{ row.renderResource }}</span>
        </template>
      </el-table-column>
      <el-table-column label="条件组" min-width="100">
        <template #default="{ row }">
          <el-tag
            v-if="row.subConditions && row.subConditions.length > 0"
            :type="row.subConditions[0].type === 'and' ? 'success' : 'warning'"
          >
            {{ row.subConditions[0].type === "and" ? "与" : "或" }} ({{
              row.subConditions.length
            }})
          </el-tag>
          <span v-else>单条件</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row, $index }">
          <el-button
            type="primary"
            link
            @click="handleEditCondition(row, $index)"
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            link
            @click="handleDeleteCondition(row, $index)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 渲染条件编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑渲染条件' : '添加渲染条件'"
      width="700px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="conditionForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="属性名称" prop="attributeName">
          <el-select
            v-model="conditionForm.attributeName"
            class="w-full"
            filterable
            placeholder="选择属性"
          >
            <el-option
              v-for="attr in attributes"
              :key="attr.name"
              :label="attr.displayName || attr.name"
              :value="attr.name"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="值类型" prop="valueType">
          <el-select v-model="conditionForm.valueType" class="w-full">
            <el-option label="字符串" value="string" />
            <el-option label="数字" value="number" />
            <el-option label="布尔值" value="boolean" />
            <el-option
              v-if="conditionForm.condition === 'regex'"
              label="正则表达式"
              value="regex"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="条件" prop="condition">
          <el-select
            v-model="conditionForm.condition"
            class="w-full"
            @change="handleConditionChange"
          >
            <el-option label="等于" value="equal" />
            <el-option label="大于" value="greater" />
            <el-option label="小于" value="less" />
            <el-option label="不等于" value="notEqual" />
            <el-option label="包含" value="contains" />
            <el-option label="不包含" value="notContains" />
            <el-option label="正则匹配" value="regex" />
          </el-select>
        </el-form-item>
        <el-form-item label="判断值" prop="value">
          <!-- 字符串输入 -->
          <el-input
            v-if="
              conditionForm.valueType === 'string' ||
              conditionForm.valueType === 'regex'
            "
            v-model="conditionForm.value"
            :placeholder="
              conditionForm.valueType === 'regex'
                ? '输入正则表达式，如: ^[a-z]+$'
                : '输入字符串值'
            "
          />
          <!-- 数字输入 -->
          <el-input-number
            v-else-if="conditionForm.valueType === 'number'"
            v-model="conditionForm.value"
            :controls="false"
            class="w-full"
          />
          <!-- 布尔值选择 -->
          <el-select
            v-else-if="conditionForm.valueType === 'boolean'"
            v-model="conditionForm.value"
            class="w-full"
          >
            <el-option label="真" :value="true" />
            <el-option label="假" :value="false" />
          </el-select>
        </el-form-item>

        <template
          v-if="
            conditionForm.subConditions &&
            conditionForm.subConditions.length > 0
          "
        >
          <div
            v-for="(subCond, index) in conditionForm.subConditions"
            :key="index"
            class="mb-4 p-3 rounded-md"
          >
            <div class="flex justify-end mb-2">
              <el-button
                type="danger"
                size="small"
                circle
                @click="removeSubCondition(index)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>

            <!-- 条件连接类型 -->
            <el-form-item :label="'连接类型'">
              <el-radio-group v-model="subCond.type" size="small">
                <el-radio label="and" value="and">与</el-radio>
                <el-radio label="or" value="or">或</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              :label="'属性名称'"
              :prop="`subConditions.conditions.${index}.attributeName`"
              :rules="{
                required: true,
                message: '请选择属性',
                trigger: 'change'
              }"
            >
              <el-select
                v-model="subCond.attributeName"
                class="w-full"
                filterable
                placeholder="选择属性"
              >
                <el-option
                  v-for="attr in attributes"
                  :key="attr.name"
                  :label="attr.displayName || attr.name"
                  :value="attr.name"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              :label="'条件'"
              :prop="`subConditions.conditions.${index}.condition`"
              :rules="{
                required: true,
                message: '请选择条件',
                trigger: 'change'
              }"
            >
              <el-select
                v-model="subCond.condition"
                class="w-full"
                @change="val => handleSubConditionChange(val, index)"
              >
                <el-option label="等于" value="equal" />
                <el-option label="大于" value="greater" />
                <el-option label="小于" value="less" />
                <el-option label="不等于" value="notEqual" />
                <el-option label="包含" value="contains" />
                <el-option label="不包含" value="notContains" />
                <el-option label="正则匹配" value="regex" />
              </el-select>
            </el-form-item>
            <el-form-item
              :label="'值类型'"
              :prop="`subConditions.conditions.${index}.valueType`"
              :rules="{
                required: true,
                message: '请选择值类型',
                trigger: 'change'
              }"
            >
              <el-select v-model="subCond.valueType" class="w-full">
                <el-option label="字符串" value="string" />
                <el-option label="数字" value="number" />
                <el-option label="布尔值" value="boolean" />
                <el-option
                  v-if="subCond.condition === 'regex'"
                  label="正则表达式"
                  value="regex"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              :label="'判断值'"
              :prop="`subConditions.conditions.${index}.value`"
              :rules="{
                required: true,
                message: '请输入判断值',
                trigger: 'blur'
              }"
            >
              <!-- 字符串输入 -->
              <el-input
                v-if="
                  subCond.valueType === 'string' ||
                  subCond.valueType === 'regex'
                "
                v-model="subCond.value"
                :placeholder="
                  subCond.valueType === 'regex'
                    ? '输入正则表达式，如: ^[a-z]+$'
                    : '输入字符串值'
                "
              />
              <!-- 数字输入 -->
              <el-input-number
                v-else-if="subCond.valueType === 'number'"
                v-model="subCond.value"
                :controls="false"
                class="w-full"
              />
              <!-- 布尔值选择 -->
              <el-select
                v-else-if="subCond.valueType === 'boolean'"
                v-model="subCond.value"
                class="w-full"
              >
                <el-option label="真" :value="true" />
                <el-option label="假" :value="false" />
              </el-select>
            </el-form-item>
          </div>
        </template>
        <div class="flex justify-center mt-4">
          <el-button type="primary" plain @click="addSubCondition">
            <el-icon class="mr-1"><Plus /></el-icon>添加条件
          </el-button>
        </div>

        <el-form-item label="渲染资源" prop="renderResource" class="mt-4">
          <div class="resource-upload-container">
            <div class="resource-input-group">
              <el-input
                v-model="conditionForm.renderResource"
                placeholder="输入资源URL或选择图片"
                class="resource-input"
                :prefix-icon="Link"
                clearable
              >
                <template #append>
                  <div class="flex items-center">
                    <el-upload
                      class="upload-btn"
                      :auto-upload="false"
                      :show-file-list="false"
                      :on-change="handleFileChange"
                      :before-upload="beforeUpload"
                    >
                      <el-button :icon="Upload">上传</el-button>
                    </el-upload>
                    <el-divider direction="vertical" />
                    <el-button
                      :icon="Select"
                      @click="showResourceSelector = true"
                    >
                      选择资源
                    </el-button>
                  </div>
                </template>
              </el-input>
            </div>

            <div v-if="conditionForm.renderResource" class="resource-preview">
              <div class="preview-card">
                <div class="preview-header">
                  <span class="preview-title">资源预览</span>
                  <el-button
                    class="preview-delete-btn"
                    type="danger"
                    size="small"
                    circle
                    @click="conditionForm.renderResource = ''"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
                <div
                  v-if="conditionForm.renderResource"
                  class="preview-content"
                >
                  <el-image
                    :src="conditionForm.renderResource"
                    fit="contain"
                    :preview-src-list="[conditionForm.renderResource]"
                    class="preview-image"
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                        <span>加载失败</span>
                      </div>
                    </template>
                  </el-image>
                </div>
              </div>
            </div>

            <div v-else class="resource-empty">
              <el-empty description="还未选择渲染资源" :image-size="100">
                <template #image>
                  <el-icon class="empty-icon"><PictureFilled /></el-icon>
                </template>
                <template #description>
                  <p>请上传或选择一个图片资源作为渲染资源</p>
                </template>
              </el-empty>
            </div>
          </div>
        </el-form-item>

        <!-- 资源选择器对话框 -->
        <el-dialog
          v-model="showResourceSelector"
          title="选择渲染资源"
          width="700px"
          destroy-on-close
        >
          <div class="resource-selector-container">
            <div class="resource-filter">
              <el-input
                v-model="resourceFilter"
                placeholder="搜索资源"
                clearable
                :prefix-icon="Search"
              />
              <el-button
                :loading="loadingResources"
                type="primary"
                @click="loadResourceList"
              >
                <el-icon class="mr-1"><Refresh /></el-icon>刷新
              </el-button>
            </div>

            <div v-if="loadingResources" class="resource-loading">
              <el-skeleton :rows="5" animated />
            </div>

            <div v-else-if="resourceList.length === 0" class="resource-empty">
              <el-empty description="没有找到可用的资源" />
            </div>

            <div v-else class="resource-grid">
              <div
                v-for="(item, index) in filteredResourceList"
                :key="index"
                class="resource-item"
                :class="{ 'resource-item-selected': isResourceSelected(item) }"
                @click="selectResource(item)"
              >
                <div class="resource-item-preview">
                  <el-image
                    v-if="isImageFile(item.fileName)"
                    :src="item.url"
                    fit="cover"
                  >
                    <template #error>
                      <div class="image-placeholder">
                        <el-icon><Picture /></el-icon>
                      </div>
                    </template>
                  </el-image>
                  <div v-else class="file-icon">
                    <el-icon><Document /></el-icon>
                  </div>
                </div>
                <div class="resource-item-info">
                  <div class="resource-item-name">{{ item.fileName }}</div>
                  <div class="resource-item-path">{{ item.url }}</div>
                </div>
              </div>
            </div>
          </div>

          <template #footer>
            <el-button @click="showResourceSelector = false">取消</el-button>
            <el-button type="primary" @click="confirmResourceSelection">
              确认选择
            </el-button>
          </template>
        </el-dialog>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveCondition">保存</el-button>
      </template>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog v-model="deleteDialogVisible" title="删除渲染条件" width="400px">
      <p>确定要删除该渲染条件吗？此操作不可恢复。</p>
      <template #footer>
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="confirmDelete">确定删除</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import {
  Plus,
  Delete,
  Link,
  Picture,
  Upload,
  Select,
  PictureFilled,
  Shop,
  Search,
  Refresh,
  Document
} from "@element-plus/icons-vue";
import { getFileList, uploadFile, type FileItem } from "@/api/file";
import type { RenderConditionDto, ConditionType } from "@/types/componentType";
import { useComponentCategory } from "@/store/modules/componentCategory";

// 从store获取数据
const componentStore = useComponentCategory();
const renderConditions = computed(
  () => componentStore.selectedComponent?.renderCondition || []
);
const attributes = computed(
  () => componentStore.selectedComponent?.attributes || []
);

// 组件绑定相关
const componentName = ref(
  componentStore.selectedComponent?.basicInfo?.bindComponentName || ""
);

// 对话框可见性
const dialogVisible = ref(false);
const deleteDialogVisible = ref(false);

// 是否是编辑模式
const isEdit = ref(false);

// 当前操作的条件索引
const currentConditionIndex = ref(-1);

// 表单引用
const formRef = ref<FormInstance>();

// 条件表单数据
const conditionForm = reactive<RenderConditionDto>({
  attributeName: "",
  condition: "equal",
  valueType: "string",
  value: "",
  renderResource: "",
  subConditions: []
});

// 表单验证规则
const formRules = reactive<FormRules>({
  attributeName: [{ required: true, message: "请选择属性", trigger: "change" }],
  condition: [{ required: true, message: "请选择条件", trigger: "change" }],
  value: [{ required: true, message: "请输入判断值", trigger: "blur" }],
  renderResource: [
    { required: true, message: "请输入渲染资源", trigger: "blur" }
  ]
});

// 获取条件文本
const getConditionText = (condition: ConditionType): string => {
  switch (condition) {
    case "equal":
      return "等于";
    case "greater":
      return "大于";
    case "less":
      return "小于";
    case "notEqual":
      return "不等于";
    case "contains":
      return "包含";
    case "notContains":
      return "不包含";
    case "regex":
      return "正则匹配";
    default:
      return condition;
  }
};

// 获取值类型文本
const getValueTypeText = (valueType: string): string => {
  switch (valueType) {
    case "string":
      return "字符串";
    case "number":
      return "数字";
    case "boolean":
      return "布尔值";
    case "regex":
      return "正则表达式";
    default:
      return valueType;
  }
};

// 处理条件类型变更
const handleConditionChange = (value: ConditionType) => {
  // 如果选择了正则匹配，自动设置值类型为正则表达式
  if (value === "regex") {
    conditionForm.valueType = "regex";
  } else if (conditionForm.valueType === "regex") {
    // 如果之前是正则表达式，但现在不是正则匹配，则改为字符串类型
    conditionForm.valueType = "string";
  }
};

// 处理子条件类型变更
const handleSubConditionChange = (value: ConditionType, index: number) => {
  // 如果选择了正则匹配，自动设置值类型为正则表达式
  if (value === "regex") {
    conditionForm.subConditions[index].valueType = "regex";
  } else if (conditionForm.subConditions[index].valueType === "regex") {
    // 如果之前是正则表达式，但现在不是正则匹配，则改为字符串类型
    conditionForm.subConditions[index].valueType = "string";
  }
};

// 处理上传前的验证
const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith("image/");
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    ElMessage.error("只能上传图片文件!");
    return false;
  }
  if (!isLt2M) {
    ElMessage.error("图片大小不能超过 2MB!");
    return false;
  }
  return true;
};

// 处理文件选择
const handleFileChange = async (file: any) => {
  if (!file || !file.raw) return;

  const isValid = beforeUpload(file.raw);
  if (!isValid) return;

  try {
    // 显示上传中状态
    ElMessage.info("正在上传文件...");

    // 调用 uploadFile API 上传文件
    const response = await uploadFile(file.raw, "render-resource");
    console.log("上传结果:", response);

    // 处理上传成功
    if (response && response.url) {
      conditionForm.renderResource = response.url;
      ElMessage.success("上传成功");
    } else {
      ElMessage.error("上传失败: 未获取到文件URL");
    }
  } catch (error) {
    console.error("文件上传失败:", error);
    ElMessage.error("文件上传失败，请重试");
  }
};

// 资源选择器相关状态和函数
const showResourceSelector = ref(false);
const resourceFilter = ref("");
const resourceList = ref<FileItem[]>([]);
const loadingResources = ref(false);
const selectedResource = ref<FileItem | null>(null);

const handleBindComponent = async () => {
  if (!componentName.value) {
    ElMessage.error("请输入组件名称");
    return;
  }

  try {
    // 使用store更新组件绑定
    const response = await componentStore.updateCurrentComponent({
      basicInfo: {
        ...componentStore.selectedComponent?.basicInfo,
        bindComponentName: componentName.value
      }
    });

    if (response.success) {
      ElMessage.success("绑定组件成功");
      dialogVisible.value = false;
    } else {
      ElMessage.error(`绑定组件失败: ${response.message}`);
    }
  } catch (error) {
    ElMessage.error("绑定组件失败");
    console.error("绑定组件失败:", error);
  }
};

// 加载资源列表
const loadResourceList = async () => {
  loadingResources.value = true;
  try {
    const result = await getFileList();
    resourceList.value = (result || []).filter(item =>
      isImageFile(item.fileName)
    );
  } catch (error) {
    console.error("获取资源列表失败:", error);
    ElMessage.error("获取资源列表失败");
    resourceList.value = [];
  } finally {
    loadingResources.value = false;
  }
};

// 过滤后的资源列表
const filteredResourceList = computed(() => {
  if (!resourceFilter.value) return resourceList.value;

  const keyword = resourceFilter.value.toLowerCase();
  return resourceList.value.filter(item =>
    item.fileName.toLowerCase().includes(keyword)
  );
});

// 判断是否为图片文件
const isImageFile = (filename: string) => {
  const imageExtensions = [
    ".jpg",
    ".jpeg",
    ".png",
    ".gif",
    ".bmp",
    ".webp",
    ".svg"
  ];
  return imageExtensions.some(ext => filename.toLowerCase().endsWith(ext));
};

// 判断资源是否被选中
const isResourceSelected = (item: FileItem) => {
  return selectedResource.value && selectedResource.value.url === item.url;
};

// 选择资源
const selectResource = (item: FileItem) => {
  selectedResource.value = item;
};

// 确认资源选择
const confirmResourceSelection = () => {
  if (selectedResource.value) {
    conditionForm.renderResource = selectedResource.value.url;
  }
  showResourceSelector.value = false;
};

// 初始化加载资源列表
onMounted(() => {
  loadResourceList();
});

// 已移除子条件的渲染资源上传功能

// 处理添加条件
const handleAddCondition = () => {
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
};

// 处理编辑条件
const handleEditCondition = (row: RenderConditionDto, index: number) => {
  isEdit.value = true;
  currentConditionIndex.value = index;

  // 填充表单数据
  conditionForm.attributeName = row.attributeName;
  conditionForm.condition = row.condition;
  conditionForm.valueType = row.valueType || "string"; // 兼容旧数据
  conditionForm.value = row.value;
  conditionForm.renderResource = row.renderResource;

  // 处理条件组
  if (row.subConditions && row.subConditions.length > 0) {
    conditionForm.subConditions = JSON.parse(JSON.stringify(row.subConditions));
  } else {
    conditionForm.subConditions = [];
  }

  dialogVisible.value = true;
};

// 处理删除条件
const handleDeleteCondition = (_row: RenderConditionDto, index: number) => {
  currentConditionIndex.value = index;
  deleteDialogVisible.value = true;
};

// 确认删除
const confirmDelete = async () => {
  if (currentConditionIndex.value === -1) return;

  try {
    const newConditions = [...renderConditions.value];
    newConditions.splice(currentConditionIndex.value, 1);

    // 使用store更新组件渲染条件
    const response = await componentStore.updateCurrentComponent({
      renderCondition: newConditions
    });

    if (response.success) {
      ElMessage.success("删除成功");
      deleteDialogVisible.value = false;
    } else {
      ElMessage.error(`删除失败: ${response.message}`);
    }
  } catch (error) {
    ElMessage.error("删除失败");
    console.error("删除渲染条件失败:", error);
  }
};

// 添加条件
const addSubCondition = () => {
  if (!conditionForm.subConditions) {
    conditionForm.subConditions = [];
  }
  conditionForm.subConditions.push({
    attributeName: "",
    condition: "equal",
    valueType: "string",
    value: "",
    type: "and" // 与下一个条件的逻辑连接符
  });
};

// 移除条件
const removeSubCondition = (index: number) => {
  if (conditionForm.subConditions) {
    conditionForm.subConditions.splice(index, 1);
  }
};

// 保存条件
const saveCondition = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async valid => {
    if (valid) {
      try {
        // 构造条件数据
        const conditionData: RenderConditionDto = {
          attributeName: conditionForm.attributeName,
          condition: conditionForm.condition,
          valueType: conditionForm.valueType,
          value: conditionForm.value,
          renderResource: conditionForm.renderResource
        };

        // 处理条件组
        if (
          conditionForm.subConditions &&
          conditionForm.subConditions.length > 0
        ) {
          conditionData.subConditions = conditionForm.subConditions;
        }

        const newConditions = [...renderConditions.value];

        if (isEdit.value) {
          // 更新条件
          newConditions[currentConditionIndex.value] = conditionData;
        } else {
          // 添加条件
          newConditions.push(conditionData);
        }

        // 使用store更新组件渲染条件
        const response = await componentStore.updateCurrentComponent({
          renderCondition: newConditions
        });

        if (response.success) {
          ElMessage.success(isEdit.value ? "更新成功" : "添加成功");
          dialogVisible.value = false;
        } else {
          ElMessage.error(
            `${isEdit.value ? "更新" : "添加"}失败: ${response.message}`
          );
        }
      } catch (error) {
        ElMessage.error(isEdit.value ? "更新失败" : "添加失败");
        console.error(isEdit.value ? "更新条件失败:" : "添加条件失败:", error);
      }
    }
  });
};

// 重置表单
const resetForm = () => {
  conditionForm.attributeName = "";
  conditionForm.condition = "equal";
  conditionForm.valueType = "string";
  conditionForm.value = "";
  conditionForm.renderResource = "";
  conditionForm.subConditions = [];
  componentName.value = "";

  // 重置表单验证
  if (formRef.value) {
    formRef.value.resetFields();
  }
};
</script>

<style scoped lang="scss">
.render-condition-manager {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

.resource-upload-container {
  width: 100%;
}

.resource-input-group {
  margin-bottom: 12px;
}

.resource-input {
  width: 100%;
}

.resource-preview {
  margin-top: 16px;
}

.preview-card {
  overflow: hidden;
  background-color: #fafafa;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.preview-title {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.preview-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 16px;
  background-color: #fff;
}

.preview-image {
  max-width: 50%;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
  transition: transform 0.3s;
}

.preview-image:hover {
  transform: scale(1.02);
}

.preview-footer {
  display: flex;
  justify-content: center;
  padding: 10px 16px;
  background-color: #f5f7fa;
  border-top: 1px solid #ebeef5;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
  font-size: 14px;
  color: #909399;
}

.image-error .el-icon {
  margin-bottom: 8px;
  font-size: 32px;
}

/* 资源选择器样式 */
.resource-selector-container {
  padding: 16px;
}

.resource-filter {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.resource-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 16px;
  max-height: 400px;
  margin-top: 16px;
  overflow-y: auto;
}

.resource-item {
  overflow: hidden;
  cursor: pointer;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  transition: all 0.3s;
}

.resource-item:hover {
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  transform: translateY(-2px);
}

.resource-item-selected {
  border: 2px solid #409eff;
  box-shadow: 0 2px 12px 0 rgb(64 158 255 / 20%);
}

.resource-item-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 120px;
  overflow: hidden;
  background-color: #f5f7fa;
}

.resource-item-preview .el-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-icon,
.image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #909399;
}

.file-icon .el-icon,
.image-placeholder .el-icon {
  font-size: 40px;
}

.resource-item-info {
  padding: 8px 12px;
}

.resource-item-name {
  margin-bottom: 4px;
  overflow: hidden;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.resource-item-path {
  overflow: hidden;
  font-size: 12px;
  color: #909399;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.resource-loading {
  padding: 20px 0;
}

.empty-icon {
  font-size: 60px;
  color: #c0c4cc;
}
</style>
