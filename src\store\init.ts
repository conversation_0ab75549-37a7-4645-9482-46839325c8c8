/**
 * Store 初始化文件
 * 用于在应用启动时主动初始化所有 store，确保它们出现在 Pinia 开发者工具面板中
 */

import { useUserStoreHook } from "./modules/user";
import { useAppStoreHook } from "./modules/app";
import { usePermissionStoreHook } from "./modules/permission";
import { useMultiTagsStoreHook } from "./modules/multiTags";
import { useEpThemeStoreHook } from "./modules/epTheme";
import { useSettingStoreHook } from "./modules/settings";
import { useComponentCategory } from "./modules/componentCategory";
import { useWorkflowStore } from "./modules/workflow";

/**
 * 初始化所有 store
 * 这将确保所有 store 都被创建并注册到 Pinia 实例中
 */
export async function initializeStores() {
  // 初始化用户 store
  const userStore = useUserStoreHook();

  // 初始化应用 store
  useAppStoreHook();

  // 初始化权限 store
  usePermissionStoreHook();

  // 初始化多标签 store
  useMultiTagsStoreHook();

  // 初始化主题 store
  useEpThemeStoreHook();

  // 初始化设置 store
  useSettingStoreHook();

  // 初始化组件分类 store
  useComponentCategory();

  // 初始化工作流 store
  useWorkflowStore();

  console.log("All stores initialized successfully");

  // 尝试初始化用户信息（如果用户已登录）
  try {
    const isLoggedIn = await userStore.initUserInfo();
    if (isLoggedIn) {
      console.log("User info restored from token");
    } else {
      console.log("No valid user session found");
    }
  } catch (error) {
    console.error("Error during user info initialization:", error);
  }
}
