---
description:
globs:
alwaysApply: false
---
- 使用Pinia作为状态管理解决方案，替代Vuex
- Store定义应使用Options API或Setup方式，保持一致性
- 每个模块应有自己的Store，如userStore、permissionStore等
- Store应明确区分state、actions和getters
- 使用TypeScript定义Store的状态类型，提高类型安全
- 异步操作应封装在actions中，并考虑并发情况
- 使用getters计算派生状态，避免在组件中重复计算
- 持久化状态使用如`localforage`结合Pinia插件实现
- 避免在Store中直接操作DOM，这应该是组件的责任
- 复杂状态应考虑规范化，提高查询效率
- 使用`storeToRefs`获取响应式Store属性，避免解构丢失响应性
- 共享的常量应放在单独的文件中，而不是Store中
