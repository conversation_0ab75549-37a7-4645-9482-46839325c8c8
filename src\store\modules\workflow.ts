import { defineStore } from "pinia";
// import type { ComponentFlowNodeDto } from "@/views/category/types";

interface WorkflowState {
  selectedNodeId: string | null;
  workflow: any;
}

export const useWorkflowStore = defineStore("workflow", {
  state: (): WorkflowState => ({
    selectedNodeId: null,
    workflow: []
  }),

  getters: {
    getSelectedNodeId: state => state.selectedNodeId,
    getWorkflow: state => state.workflow,

    // 获取选中的节点
    getSelectedNode: state => {
      if (!state.selectedNodeId) return null;
      return (
        state.workflow.nodes.find(node => node.id === state.selectedNodeId) ||
        null
      );
    }
  },

  actions: {
    // 重置所有状态
    resetWorkflow() {
      this.$reset();
    }
  }
});

// TODO: 打包发布到1panel
