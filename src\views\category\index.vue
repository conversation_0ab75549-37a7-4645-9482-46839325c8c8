<script setup lang="ts">
import { ref } from "vue";
import ComponentList from "./components/ComponentList.vue";
import BasicInfo from "./components/BasicInfo.vue";
import AttributeManager from "./components/AttributeManager.vue";
import SubcomponentManager from "./components/SubcomponentManager.vue";
import FunctionManager from "./components/FunctionManager.vue";
import AlarmManager from "./components/AlarmManager.vue";
import { useComponentCategory } from "@/store/modules/componentCategory";

// ! TODO
// 1. 新建零件时，function部分会有上一个零件创建的function残留
// 2. 删除零件function中的某一个节点，只删除了界面展示相关的元素没有删除详细的节点信息
// 3. 添加属性时，属性类型选择为数字默认值还是字符串
// 4. 需要增加一个调用全局属性的节点

defineOptions({
  name: "PartCategory"
});

const componentStore = useComponentCategory();

// 当前选中的标签页
const activeTab = ref("basic");
</script>

<template>
  <div class="part-category">
    <el-container class="h-full">
      <!-- 左侧零件列表 -->
      <ComponentList />

      <!-- 右侧内容区 -->
      <el-main class="p-0">
        <div v-if="componentStore.selectedComponentId" class="p-4">
          <el-tabs v-model="activeTab" type="border-card">
            <el-tab-pane label="基本信息" name="basic">
              <BasicInfo />
            </el-tab-pane>
            <el-tab-pane label="属性管理" name="attributes">
              <AttributeManager />
            </el-tab-pane>
            <el-tab-pane label="子零件管理" name="subcomponents">
              <SubcomponentManager />
            </el-tab-pane>
            <el-tab-pane label="功能管理" name="functions">
              <FunctionManager />
            </el-tab-pane>
            <el-tab-pane label="报警管理" name="alarms">
              <AlarmManager />
            </el-tab-pane>

            <!-- 渲染条件 -->
            <!-- <el-tab-pane label="渲染条件" name="renderConditions">
              <RenderConditionManager />
            </el-tab-pane> -->
          </el-tabs>
        </div>

        <div v-else class="flex justify-center items-center h-full">
          <el-empty description="请选择一个零件进行管理" />
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<style scoped lang="scss">
.part-category {
  height: calc(100vh - 106px);
  background-color: #f5f7fa;
}
</style>
