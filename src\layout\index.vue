<script setup lang="ts">
import "animate.css";
// 引入 src/components/ReIcon/src/offlineIcon.ts 文件中所有使用addIcon添加过的本地图标
import "@/components/ReIcon/src/offlineIcon";
import { ref } from "vue";
import { useNav } from "./hooks/useNav";
import { IconifyIconOffline } from "@/components/ReIcon";
import LogOut from "@iconify-icons/ri/logout-circle-r-line";

const appWrapperRef = ref();
const userDropdownRef = ref();

// 使用现有的导航钩子获取所有需要的功能
const {
  title,
  username,
  userAvatar,
  avatarsStyle,
  logout,
  getLogo,
  tooltipEffect
} = useNav();

// 处理键盘事件
const handleKeyboardClick = () => {
  if (userDropdownRef.value) {
    userDropdownRef.value.handleOpen();
  }
};
</script>

<template>
  <div ref="appWrapperRef" class="app-layout">
    <!-- 应用头部 -->
    <header class="app-header" role="banner">
      <div class="header-container">
        <!-- 左侧：Logo 和标题 -->
        <div class="header-left">
          <div class="logo-section">
            <img
              :src="getLogo()"
              alt="系统Logo"
              class="app-logo"
              loading="lazy"
            />
            <h1 class="app-title">{{ title }}</h1>
          </div>
        </div>

        <!-- 右侧：用户信息 -->
        <div class="header-right">
          <el-dropdown
            ref="userDropdownRef"
            trigger="click"
            :effect="tooltipEffect"
            placement="bottom-end"
            class="user-dropdown"
          >
            <div
              class="user-info"
              role="button"
              tabindex="0"
              :aria-label="`用户菜单 - ${username}`"
              @keydown.enter="handleKeyboardClick"
              @keydown.space.prevent="handleKeyboardClick"
            >
              <el-avatar
                :src="userAvatar"
                :size="32"
                class="user-avatar"
                :style="avatarsStyle"
              >
                <IconifyIconOffline icon="ri:user-3-fill" class="w-4 h-4" />
              </el-avatar>
              <span v-if="username" class="username">{{ username }}</span>
              <IconifyIconOffline
                icon="ri:arrow-down-s-line"
                class="dropdown-arrow"
              />
            </div>

            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item class="logout-item" @click="logout">
                  <IconifyIconOffline :icon="LogOut" class="w-4 h-4 mr-2" />
                  <span>退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="app-main" role="main">
      <router-view>
        <template #default="{ Component }">
          <component :is="Component" />
        </template>
      </router-view>
    </main>
  </div>
</template>

<style lang="scss" scoped>
.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--el-bg-color-page);
}

.app-header {
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all 0.3s ease;

  .header-container {
    margin: 0 auto;
    padding: 0 24px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    @media (max-width: 768px) {
      padding: 0 16px;
      height: 56px;
    }
  }
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;

  .logo-section {
    display: flex;
    align-items: center;
    gap: 12px;

    .app-logo {
      width: 32px;
      height: 32px;
      object-fit: contain;
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.05);
      }

      @media (max-width: 768px) {
        width: 28px;
        height: 28px;
      }
    }

    .app-title {
      font-size: 20px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin: 0;
      letter-spacing: 0.5px;
      transition: color 0.3s ease;

      @media (max-width: 768px) {
        font-size: 18px;
      }

      @media (max-width: 480px) {
        display: none;
      }
    }
  }
}

.header-right {
  display: flex;
  align-items: center;

  .user-dropdown {
    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
      border: 1px solid transparent;

      &:hover {
        background-color: var(--el-fill-color-light);
        border-color: var(--el-border-color);
      }

      &:focus {
        outline: none;
        box-shadow: 0 0 0 2px var(--el-color-primary-light-7);
        border-color: var(--el-color-primary);
      }

      .user-avatar {
        flex-shrink: 0;
        border: 2px solid var(--el-border-color-lighter);
        transition: border-color 0.2s ease;

        &:hover {
          border-color: var(--el-color-primary-light-5);
        }
      }

      .username {
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        @media (max-width: 640px) {
          display: none;
        }
      }

      .dropdown-arrow {
        color: var(--el-text-color-secondary);
        transition:
          transform 0.2s ease,
          color 0.2s ease;
        flex-shrink: 0;
      }

      &:hover .dropdown-arrow {
        color: var(--el-text-color-primary);
        transform: rotate(180deg);
      }
    }
  }
}

.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// 下拉菜单样式
:deep(.el-dropdown-menu) {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--el-border-color-light);
  padding: 4px;

  .logout-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    color: var(--el-color-danger);
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--el-color-danger-light-9);
      color: var(--el-color-danger);
    }

    &:focus {
      background-color: var(--el-color-danger-light-9);
      color: var(--el-color-danger);
    }
  }
}

// 深色主题适配
html[data-theme="dark"] {
  .app-header {
    background: var(--el-bg-color);
    border-bottom-color: var(--el-border-color);
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .app-header {
    border-bottom-width: 2px;
  }

  .user-info {
    border-width: 2px !important;
  }
}

// 减少动画模式支持
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}
</style>
