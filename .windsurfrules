# 项目指引

这是一个基于Vue 3、TypeScript、Element Plus和Tailwind CSS的部件库管理系统。

## 核心文件

- [package.json](mdc:package.json) - 项目依赖和脚本
- [vite.config.ts](mdc:vite.config.ts) - Vite构建配置
- [src/main.ts](mdc:src/main.ts) - 应用入口文件
- [src/App.vue](mdc:src/App.vue) - 根组件
- [src/router/index.ts](mdc:src/router/index.ts) - 路由配置
- [tsconfig.json](mdc:tsconfig.json) - TypeScript配置

## 项目结构

- `src/api` - API请求文件
- `src/components` - 可复用组件
- `src/assets` - 静态资源文件
- `src/router` - 路由配置
- `src/store` - Pinia状态管理
- `src/utils` - 工具函数
- `src/views` - 页面组件
- `src/types` - TypeScript类型定义
- `src/styles` - 全局样式文件

## 开发指南

1. 使用`pnpm install`安装依赖
2. 使用`pnpm dev`启动开发服务器
3. 使用`pnpm build`构建生产版本
4. 使用`pnpm preview`预览构建版本

- 项目使用Vue 3组合式API和TypeScript进行开发
- 所有组件应放在`src/components`目录下，并按功能进行分组
- API请求应集中在`src/api`目录下，并按业务领域进行分类
- 全局状态管理使用Pinia，store文件应放在`src/store/modules`目录下
- 公共工具函数应放在`src/utils`目录下，并按功能分类
- 类型定义应集中在`src/types`目录下，确保类型共享和一致性
- 路由配置应按功能模块拆分，放在`src/router/modules`目录下
- 组件应使用前缀命名，例如`Re`前缀表示可复用组件


## 技术栈

- Vue 3 - 前端框架
- TypeScript - 类型系统
- Vite - 构建工具
- Pinia - 状态管理
- Vue Router - 路由管理
- Element Plus - UI组件库
- Tailwind CSS - 工具类CSS框架
- Axios - HTTP客户端

## 文件命名规范

- Vue单文件组件使用PascalCase命名法，如`UserProfile.vue`
- 可复用组件应添加前缀，如`Re`前缀表示通用组件
- TypeScript类型定义文件使用`.d.ts`后缀，如`user.d.ts`
- API请求模块应使用业务领域命名，如`user.ts`、`project.ts`
- 工具函数文件使用功能命名，如`formatter.ts`、`validator.ts`
- Store模块使用功能命名，如`userStore.ts`、`permissionStore.ts`
- 路由模块使用业务功能命名，如`dashboard.ts`、`settings.ts`
- 样式文件使用kebab-case命名法，如`element-override.scss`
- 测试文件使用原文件名加`.spec`或`.test`后缀
- 常量文件使用全大写下划线分隔命名，如`API_CONSTANTS.ts`
- Hook函数文件使用`use`前缀，如`usePermission.ts`
- 工具函数应使用动词或者动词+名词的形式，如`formatDate.ts`

## 组件开发规范

- 使用Vue 3的组合式API进行组件开发
- 组件Props应使用TypeScript类型定义，并提供默认值
- 自定义组件应提供完整的类型定义和文档注释
- 组件内部状态使用`ref`和`reactive`管理，确保响应式
- 对于表单组件，提供`v-model`双向绑定支持
- 复杂逻辑应抽取到组合函数(composables)中，放置在`src/composables`目录
- 组件应遵循单一职责原则，避免过于复杂的组件
- 使用命名插槽(named slots)提供灵活的内容分发机制
- 对外暴露的方法使用`defineExpose`明确声明
- 组件样式应使用`scoped`或模块化CSS防止样式污染

## 状态管理规范

- 使用Pinia作为状态管理解决方案，替代Vuex
- Store定义应使用Options API或Setup方式，保持一致性
- 每个模块应有自己的Store，如userStore、permissionStore等
- Store应明确区分state、actions和getters
- 使用TypeScript定义Store的状态类型，提高类型安全
- 异步操作应封装在actions中，并考虑并发情况
- 使用getters计算派生状态，避免在组件中重复计算
- 持久化状态使用如`localforage`结合Pinia插件实现
- 避免在Store中直接操作DOM，这应该是组件的责任
- 复杂状态应考虑规范化，提高查询效率
- 使用`storeToRefs`获取响应式Store属性，避免解构丢失响应性
- 共享的常量应放在单独的文件中，而不是Store中


## API开发规范

- API 请求通过@/utils/http 下的 http方法实现
- API请求函数应使用TypeScript类型定义请求参数和响应数据
- 请求函数应返回Promise，并处理错误情况
- 使用Axios实例创建基础配置，如基础URL、超时设置等
- 实现请求拦截器处理认证令牌和公共参数
- 实现响应拦截器统一处理错误码和异常
- API模块应按业务领域分类，如用户API、项目API等
- 使用泛型为响应数据提供类型安全
- 对于复杂查询参数，使用接口定义参数结构
- 提供请求取消功能，避免重复请求
- 对于开发环境，集成Mock数据服务

## 路由管理

- 使用Vue Router 4进行路由管理，支持历史模式和哈希模式
- 路由配置应模块化，按功能分组放置在`src/router/modules`目录
- 使用路由元数据(meta)定义路由属性，如权限、标题、图标等
- 实现路由守卫(Guards)进行权限控制和页面跳转拦截
- 使用动态导入(Dynamic Import)进行代码分割，提高首屏加载速度
- 多级路由应使用嵌套路由(Nested Routes)表达层次结构
- 复杂页面切换应使用过渡动画增强用户体验
- 使用路由别名(alias)提供路径映射，优化用户导航
- 实现404页面处理无效路由
- 对参数必须的路由实现参数验证
- 路由跳转尽量使用命名路由，而不是硬编码URL
- 使用路由参数(params)和查询参数(query)传递数据


## 性能优化

- 使用Vue 3的异步组件和Suspense进行懒加载
- 路由组件使用动态导入实现代码分割
- 大型列表使用虚拟滚动技术，如Element Plus的虚拟表格
- 大型表单应拆分成多个小表单或分步骤展示
- 图片资源应进行懒加载和适当的压缩
- 使用Web Worker处理复杂计算，避免阻塞主线程
- 优化打包配置，实现代码分割和Tree-shaking
- 关键CSS应内联，非关键CSS应异步加载
- 使用缓存策略缓存API响应和静态资源
- 实现组件级别的功能开关，便于性能调试
- 长列表渲染时使用唯一key，优化Diff算法
- 避免在模板中使用复杂表达式，应抽取为计算属性
- 使用`shallowRef`和`shallowReactive`处理大型非响应式数据
- 利用`v-once`和`v-memo`减少不必要的重新渲染

## UI规范

- 使用Element Plus作为UI组件库基础，保持视觉一致性
- 颜色系统应遵循Element Plus主题，避免自定义颜色扰乱系统
- 使用Tailwind CSS进行布局和样式增强，提高开发效率
- 自定义组件应继承Element Plus的设计语言，保持体验一致
- 表单设计遵循一致的布局和对齐规则，提高可读性
- 使用响应式设计支持不同设备尺寸，布局应流畅适应
- 图标应优先使用Element Plus提供的图标系统，保持统一
- 对话框、抽屉和弹出提示应使用统一的交互模式
- 表格设计应考虑大数据展示和性能优化
- 数据可视化组件应选择合适的图表类型表达数据关系
- 加载状态应提供明确的视觉反馈，避免用户困惑
- 表单验证应提供及时清晰的错误提示
- 动效设计应适度，增强体验但不干扰操作
