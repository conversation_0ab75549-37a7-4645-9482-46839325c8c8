---
description:
globs:
alwaysApply: false
---
- 使用Vue 3的异步组件和Suspense进行懒加载
- 路由组件使用动态导入实现代码分割
- 大型列表使用虚拟滚动技术，如Element Plus的虚拟表格
- 大型表单应拆分成多个小表单或分步骤展示
- 图片资源应进行懒加载和适当的压缩
- 使用Web Worker处理复杂计算，避免阻塞主线程
- 优化打包配置，实现代码分割和Tree-shaking
- 关键CSS应内联，非关键CSS应异步加载
- 使用缓存策略缓存API响应和静态资源
- 实现组件级别的功能开关，便于性能调试
- 长列表渲染时使用唯一key，优化Diff算法
- 避免在模板中使用复杂表达式，应抽取为计算属性
- 使用`shallowRef`和`shallowReactive`处理大型非响应式数据
- 利用`v-once`和`v-memo`减少不必要的重新渲染
