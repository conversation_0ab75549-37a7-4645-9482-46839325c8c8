# 零件相关Dto

# 1.零件相关Dto

## 1.1.基础信息 ComponentBasicInfoDto

### 1.1.1.guid

### 1.1.2.名/类型

### 1.1.3.版本号

### 1.1.4.版本说明

### 1.1.5.描述

### 1.1.6.标签列表

### 1.1.7.创建时间

### 1.1.8.作者

## 1.2.属性列表

### 1.2.1.属性 ComponentAttributeDto

#### 1.2.1.1.名

#### 1.2.1.2.显示名称

#### 1.2.1.3.监听间隔 <=0不启动监听，其余情况启动监听

#### 1.2.1.4.值类型 通过数据协议转换层装换

- Int

- string

- bool

- array

- object

#### 1.2.1.5.描述

#### 1.2.1.6.单位

#### 1.2.1.7.默认值

#### 1.2.1.8.限制规则

- 上限

- 下限

- 长度

- 正则

- 小数位数

## 1.3.子零件列表

### 1.3.1.子零件 SubcomponentInfoDto

#### 1.3.1.1.名 在主零件中子零件的名称，非子零件名称

#### 1.3.1.2.零件类型名 其实是子零件名

#### 1.3.1.3.零件类型版本

#### 1.3.1.4.参数传递规则

- 同名继承

- 自定义规则

#### 1.3.1.5.属性传递规则列表

- 当前零件属性

- 子零件属性

- 值传递方式

  - 固定值
  - 继承值
  - 计算值

## 1.4.报警列表

### 1.4.1.报警 ComponentAlarmDto

#### 1.4.1.1.ID

#### 1.4.1.2.名

#### 1.4.1.3.描述

#### 1.4.1.4.等级

#### 1.4.1.5.可用处理方式 权限选择

## 1.5.功能列表

### 1.5.1.功能 ComponentFunctionDto

#### 1.5.1.1.名

#### 1.5.1.2.输入参数描述列表 来自流程列表中的Start

- 入参

  - 参数名
  - 参数类型/值类型
  - 参数注释

#### 1.5.1.3.描述

#### 1.5.1.4.流程列表

- 单节点

  - 节点ID
  - 节点备注
  - 节点基础功能配置 _见零件功能编辑设计基础功能_
  - 上一个节点列表 _输入，通常为1个_
  - 下一个节点列表 _输出_

## 1.6.参数传递规则

### 1.6.1./

## 1.7.渲染条件列表

### 1.7.1.属性名称

### 1.7.2.条件

#### 1.7.2.1.等于

#### 1.7.2.2.大于

#### 1.7.2.3.小于

#### 1.7.2.4.不等于

#### 1.7.2.5.包含

#### 1.7.2.6.不包含

### 1.7.3.判断值

### 1.7.4.渲染资源

### 1.7.5.子条件

#### 1.7.5.1.和

#### 1.7.5.2.或
