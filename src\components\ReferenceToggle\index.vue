<template>
  <div class="reference-toggle-container">
    <div class="form-item-container">
      <template v-if="isReference && referenceValue">
        <div class="reference-display">
          <div v-if="label" class="reference-label">
            {{ label }}
          </div>
          <div class="reference-value-tag">
            <!-- <el-icon class="reference-icon"><Link /></el-icon> -->
            <span class="reference-text">
              {{ formatReferenceDisplay(referenceValue) }}
            </span>
          </div>
        </div>
      </template>
      <template v-else>
        <slot />
      </template>
    </div>
    <div class="reference-toggle">
      <el-popover
        placement="right"
        :width="300"
        trigger="click"
        popper-class="reference-popover"
      >
        <template #reference>
          <el-button
            type="primary"
            size="small"
            :icon="isReference ? Link : Connection"
            circle
            class="reference-btn"
            :class="{ 'is-referenced': isReference }"
          />
        </template>
        <div class="reference-content">
          <div class="reference-header">
            <span class="reference-title">参数引用</span>
            <el-switch
              v-model="isReference"
              active-text="引用值"
              inactive-text="静态值"
              @change="handleModeChange"
            />
          </div>
          <div v-if="isReference" class="reference-selector">
            <el-select
              v-model="referenceValue"
              filterable
              placeholder="选择引用节点"
              class="reference-select"
              @change="handleReferenceChange"
            >
              <el-option-group
                v-for="node in previousNodes"
                :key="node.id"
                :label="node.name"
              >
                <el-option
                  v-for="param in node.outputParams"
                  :key="`${node.id}-${param.name}`"
                  :label="`${param.name} (${getParamTypeLabel(param.valueType)})`"
                  :value="`${node.id}.${param.name}`"
                />
              </el-option-group>
            </el-select>
          </div>
        </div>
      </el-popover>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { useVueFlow } from "@vue-flow/core";
import { Link, Connection } from "@element-plus/icons-vue";
import { getParamTypeLabel } from "@/utils/util";

// 定义组件的 props
const props = defineProps({
  modelValue: {
    type: [String, Object, Number, Boolean] as PropType<any>,
    default: null
  },
  valueType: {
    type: Number,
    default: 0
  },
  label: {
    type: String,
    default: ""
  },
  field: {
    type: String,
    default: ""
  },
  nodeId: {
    type: String,
    default: ""
  }
});

// 定义组件的事件
const emit = defineEmits(["update:modelValue"]);

// 本地状态
const isReference = ref(false);
const referenceValue = ref("");
const staticValue = ref<any>(null);

// 获取 Vue Flow 实例
const { getNodes, getIncomers } = useVueFlow();

// 定义接口类型
interface NodeOutputParam {
  name: string;
  valueType: number;
}

interface ParamNode {
  id: string;
  name: string;
  outputParams: NodeOutputParam[];
}

// 获取当前节点之前的节点及其输出参数
const previousNodes = computed(() => {
  const nodes = getNodes.value || [];
  const currentNode = nodes.find(node => node.id === props.nodeId);

  if (!props.nodeId || !currentNode) return [];

  const collectedNodeIds = new Set<string>();
  const paramList: ParamNode[] = [];

  // 递归收集上游节点
  const collectUpstreamNodes = (node: any) => {
    if (!node || collectedNodeIds.has(node.id)) return;

    collectedNodeIds.add(node.id);
    paramList.push({
      id: node.id,
      name: node.data.configValue?.name?.value || node.data?.name || node.id,
      outputParams: node.data?.outputParams || []
    });

    const incomers = getIncomers(node);
    incomers.forEach(incomer => collectUpstreamNodes(incomer));
  };

  const directIncomers = getIncomers(currentNode);
  directIncomers.forEach(node => collectUpstreamNodes(node));

  // 直接返回已收集的参数列表，不需要再次过滤和映射
  return paramList.filter(
    node => node.outputParams && node.outputParams.length > 0
  );
});

// 初始化组件状态
onMounted(() => {
  initializeFromValue(props.modelValue);
});

// 根据值初始化组件状态
const initializeFromValue = (value: any) => {
  if (value && typeof value === "object" && value.hasOwnProperty("paramType")) {
    if (value.paramType === "ref") {
      isReference.value = true;
      referenceValue.value = value.value || "";
      staticValue.value = undefined;
    } else if (value.paramType === "static") {
      isReference.value = false;
      staticValue.value = value.value;
      referenceValue.value = "";
    }
  } else {
    // 空值处理
    isReference.value = false;
    staticValue.value = value || null;
    referenceValue.value = "";
  }
};

// 构建输出值
const buildOutputValue = () => {
  // 引用类型
  if (isReference.value && referenceValue.value) {
    return {
      name: props.field,
      value: referenceValue.value,
      valueType: props.valueType,
      paramType: "ref"
    };
  }

  // 静态类型
  return {
    name: props.field,
    value: staticValue.value,
    valueType: props.valueType,
    paramType: "static"
  };
};

// 处理模式切换
const handleModeChange = (isRef: boolean) => {
  // 当模式切换时，清除相应的值
  if (isRef) {
    referenceValue.value = "";
    staticValue.value = undefined;
  } else {
    referenceValue.value = "";
    // 可以保留静态值或重置它
    staticValue.value = null;
  }

  // 更新到store中
  const outputValue = buildOutputValue();

  // 通知父组件
  emit("update:modelValue", outputValue);
};

// 格式化引用值显示
const formatReferenceDisplay = (value: string) => {
  if (!value || !value.includes(".")) return value;

  const [nodeId, paramName] = value.split(".");
  const node = previousNodes.value.find(n => n.id === nodeId);

  if (node) {
    return `${node.name}.${paramName}`;
  }

  return value;
};

// 处理引用值选择
const handleReferenceChange = (value: string) => {
  if (!value || !value.includes(".")) return;

  const outputValue = {
    name: props.field,
    value,
    valueType: props.valueType,
    paramType: "ref"
  };

  // 通知父组件
  emit("update:modelValue", outputValue);
};
</script>

<style scoped>
.reference-toggle-container {
  display: flex;
  align-items: center;
}

.form-item-container {
  flex: 1;
  margin-right: 8px;
}

.reference-toggle {
  flex-shrink: 0;
}

.reference-select {
  width: 100%;
}

.reference-display {
  padding: 4px 0;
  display: flex;
  align-items: center;
  margin-bottom: 18px;
}

.reference-label {
  width: 70px;
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
  font-weight: 500;
  margin-right: 12px;
  text-align: right;
}

.reference-value-tag {
  display: inline-flex;
  align-items: center;
  padding: 5px;
  border-radius: 4px;
  background-color: rgba(64, 158, 255, 0.08);
  border: 1px solid rgba(64, 158, 255, 0.2);
  color: #409eff;
  font-size: 14px;
  line-height: 1.4;
  transition: all 0.3s;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.reference-value-tag:hover {
  background-color: rgba(64, 158, 255, 0.12);
  border-color: rgba(64, 158, 255, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.reference-text {
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  direction: rtl;
}

.reference-btn {
  transition: all 0.3s ease;
  margin-bottom: 18px;
}

.reference-btn.is-referenced {
  background-color: var(--el-color-success);
  border-color: var(--el-color-success);
}

.reference-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.reference-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.reference-selector {
  margin-top: 12px;
}

.reference-select {
  width: 100%;
}

:deep(.reference-popover) {
  padding: 16px;
}
</style>
