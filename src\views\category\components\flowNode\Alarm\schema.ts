import { FieldType, type NodeSchema } from "../nodeSchema";
const alarmSchema: NodeSchema = {
  name: "alarm",
  description: "报警",
  fields: [
    {
      type: FieldType.SELECT,
      label: "选择报警",
      fieldName: "alarm-index",
      required: true,
      placeholder: "请选择报警",
      dynamicOptions: {
        type: "store",
        source: "componentCategory.getAlarms",
        labelKey: "label",
        valueKey: "value"
      }
    }
  ],
  inputs: [{ name: "in", label: "输入" }],
  outputs: [
    { name: "out", label: "输出" },
    { name: "error", label: "错误" }
  ]
};

export default alarmSchema;
