<template>
  <div class="alarm-manager">
    <div class="header flex justify-between items-center mb-4">
      <h2 class="text-lg font-medium">报警管理</h2>
      <el-button type="primary" @click="handleAddAlarm">
        <el-icon class="mr-1"><Plus /></el-icon>添加报警
      </el-button>
    </div>

    <el-table :data="alarms" border style="width: 100%">
      <el-table-column prop="index" label="ID" min-width="100" />
      <el-table-column prop="name" label="名称" min-width="120" />
      <el-table-column
        prop="description"
        label="描述"
        min-width="150"
        show-overflow-tooltip
      />
      <el-table-column prop="level" label="等级" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getAlarmLevelType(row.level)" effect="dark">
            {{ getAlarmLevelText(row.level) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="可用处理方式" min-width="200">
        <template #default="{ row }">
          <el-tag
            v-for="method in row.processingMethod"
            :key="method"
            class="mr-1 mb-1"
          >
            {{ getProcessingMethodText(method) }}
          </el-tag>
          <span
            v-if="!row.processingMethod || row.processingMethod.length === 0"
            >无</span
          >
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEditAlarm(row)">
            编辑
          </el-button>
          <el-button type="danger" link @click="handleDeleteAlarm(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 报警编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑报警' : '添加报警'"
      width="650px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="alarmForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="序号" prop="index">
          <el-input-number v-model="alarmForm.index" />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="alarmForm.name" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="alarmForm.description" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="等级" prop="level">
          <el-select v-model="alarmForm.level" class="w-full">
            <el-option
              v-for="option in alarmLevelOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="处理方式" prop="processingMethod">
          <el-select
            v-model="alarmForm.processingMethod"
            multiple
            class="w-full"
            placeholder="请选择可用处理方式"
          >
            <el-option
              v-for="option in processingMethodOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveAlarm">保存</el-button>
      </template>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog v-model="deleteDialogVisible" title="删除报警" width="400px">
      <p>确定要删除报警 "{{ currentAlarm?.name }}" 吗？此操作不可恢复。</p>
      <template #footer>
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="confirmDelete">确定删除</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { AlarmLevel } from "@/const/enums";
import type { ComponentAlarmDto } from "@/types/componentType";
import { useComponentCategory } from "@/store/modules/componentCategory";

// 从store获取数据
const componentStore = useComponentCategory();
const alarms = computed(() => componentStore.selectedComponent?.alarms || []);

// 报警等级选项
const alarmLevelOptions = [
  { label: "信息", value: "Info" },
  { label: "警告", value: "Warning" },
  { label: "验证", value: "Alarm" },
  { label: "未知", value: "Unknown" }
];

// 处理方式选项
const processingMethodOptions = [
  { label: "清除", value: "Clean" },
  { label: "重试", value: "Retry" },
  { label: "中止", value: "Abort" },
  { label: "忽略", value: "Ignore" }
];

// 对话框可见性
const dialogVisible = ref(false);
const deleteDialogVisible = ref(false);

// 是否是编辑模式
const isEdit = ref(false);

// 当前操作的报警
const currentAlarm = ref<ComponentAlarmDto | null>(null);

// 表单引用
const formRef = ref<FormInstance>();

// 报警表单数据
const alarmForm = reactive<{
  index: number;
  name: string;
  description: string;
  level: string;
  processingMethod: string[];
}>({
  index: undefined,
  name: "",
  description: "",
  level: "Info",
  processingMethod: []
});

// 表单验证规则
const formRules = reactive<FormRules>({
  name: [{ required: true, message: "请输入报警名称", trigger: "blur" }],
  level: [{ required: true, message: "请选择报警等级", trigger: "change" }]
});

// 获取报警等级对应的标签类型
const getAlarmLevelType = (
  level: AlarmLevel
): "success" | "warning" | "info" | "danger" | "primary" => {
  switch (level) {
    case "Info":
      return "info";
    case "Warning":
      return "warning";
    case "Alarm":
      return "danger";
    case "Unknown":
      return "info";
    default:
      return "info";
  }
};

// 获取报警等级对应的文本
const getAlarmLevelText = (level: AlarmLevel): string => {
  const found = alarmLevelOptions.find(option => option.value === level);
  return found ? found.label : level;
};

// 获取处理方式对应的文本
const getProcessingMethodText = (method: string): string => {
  const found = processingMethodOptions.find(option => option.value === method);
  return found ? found.label : method;
};

// 处理添加报警
const handleAddAlarm = () => {
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
};

// 处理编辑报警
const handleEditAlarm = (row: ComponentAlarmDto) => {
  isEdit.value = true;
  currentAlarm.value = row;

  // 填充表单数据
  alarmForm.index = row.index;
  alarmForm.name = row.name;
  alarmForm.description = row.description;
  alarmForm.level = row.level;
  alarmForm.processingMethod = [...(row.processingMethod || [])];

  dialogVisible.value = true;
};

// 处理删除报警
const handleDeleteAlarm = (row: ComponentAlarmDto) => {
  currentAlarm.value = row;
  deleteDialogVisible.value = true;
};

// 确认删除
const confirmDelete = async () => {
  if (!currentAlarm.value) return;

  try {
    // 移除要删除的报警
    const updatedAlarms = alarms.value.filter(
      alarm => alarm.index !== currentAlarm.value?.index
    );

    // 使用Pinia store更新组件
    const response = await componentStore.updateCurrentComponent({
      alarms: updatedAlarms
    });

    if (response.success) {
      ElMessage.success("删除成功");
      deleteDialogVisible.value = false;
    } else {
      ElMessage.error(`删除失败: ${response.message}`);
    }
  } catch (error) {
    ElMessage.error("删除失败");
    console.error("删除报警失败:", error);
  }
};

// 保存报警
const saveAlarm = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async valid => {
    if (valid) {
      try {
        // 构造报警数据
        const alarmData: ComponentAlarmDto = {
          index: alarmForm.index, // 非编辑模式生成新ID
          name: alarmForm.name,
          description: alarmForm.description,
          level: alarmForm.level,
          processingMethod: alarmForm.processingMethod
        };

        let response;
        if (isEdit.value) {
          // 更新报警 - 使用Pinia store
          // 需要获取当前组件报警列表并更新特定报警
          const updatedAlarms = [...alarms.value];
          const alarmIndex = updatedAlarms.findIndex(
            alarm => alarm.index === alarmData.index
          );

          if (alarmIndex >= 0) {
            updatedAlarms[alarmIndex] = alarmData;
          } else {
            updatedAlarms.push(alarmData);
          }

          // 调用store更新组件
          response = await componentStore.updateCurrentComponent({
            alarms: updatedAlarms
          });
        } else {
          // 添加报警 - 使用Pinia store
          // 为新报警设置索引
          if (alarmData.index === undefined) {
            // 如果没有设置索引，就使用当前最大索引+1
            const maxIndex =
              alarms.value.length > 0
                ? Math.max(...alarms.value.map(alarm => alarm.index))
                : -1;
            alarmData.index = maxIndex + 1;
          }

          const updatedAlarms = [...alarms.value, alarmData];

          // 调用store更新组件
          response = await componentStore.updateCurrentComponent({
            alarms: updatedAlarms
          });
        }

        if (response.success) {
          ElMessage.success(isEdit.value ? "更新成功" : "添加成功");
          dialogVisible.value = false;
        } else {
          ElMessage.error(
            `${isEdit.value ? "更新" : "添加"}失败: ${response.message}`
          );
        }
      } catch (error) {
        ElMessage.error(isEdit.value ? "更新失败" : "添加失败");
        console.error(isEdit.value ? "更新报警失败:" : "添加报警失败:", error);
      }
    }
  });
};

// 重置表单
const resetForm = () => {
  alarmForm.index = undefined;
  alarmForm.name = "";
  alarmForm.description = "";
  alarmForm.level = "Info";
  alarmForm.processingMethod = [];

  // 重置表单验证
  if (formRef.value) {
    formRef.value.resetFields();
  }
};
</script>

<style scoped lang="scss">
.alarm-manager {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}
</style>
