---
description:
globs:
alwaysApply: false
---
# 项目指引

这是一个基于Vue 3、TypeScript、Element Plus和Tailwind CSS的部件库管理系统。

## 核心文件

- [package.json](mdc:package.json) - 项目依赖和脚本
- [vite.config.ts](mdc:vite.config.ts) - Vite构建配置
- [src/main.ts](mdc:src/main.ts) - 应用入口文件
- [src/App.vue](mdc:src/App.vue) - 根组件
- [src/router/index.ts](mdc:src/router/index.ts) - 路由配置
- [tsconfig.json](mdc:tsconfig.json) - TypeScript配置

## 项目结构

- `src/api` - API请求文件
- `src/components` - 可复用组件
- `src/assets` - 静态资源文件
- `src/router` - 路由配置
- `src/store` - Pinia状态管理
- `src/utils` - 工具函数
- `src/views` - 页面组件
- `src/types` - TypeScript类型定义
- `src/styles` - 全局样式文件

## 开发指南

1. 使用`pnpm install`安装依赖
2. 使用`pnpm dev`启动开发服务器
3. 使用`pnpm build`构建生产版本
4. 使用`pnpm preview`预览构建版本

## 技术栈

- Vue 3 - 前端框架
- TypeScript - 类型系统
- Vite - 构建工具
- Pinia - 状态管理
- Vue Router - 路由管理
- Element Plus - UI组件库
- Tailwind CSS - 工具类CSS框架
- Axios - HTTP客户端
