<template>
  <div class="panel-section output-config-section">
    <div class="section-header">
      <div class="section-label">
        <el-icon><Connection /></el-icon>
        <span>输出参数</span>
      </div>
    </div>
    <!-- 参数展示区域 -->
    <div v-if="outputParams.length" class="params-container">
      <div class="params-list">
        <div
          v-for="(param, index) in outputParams"
          :key="param.name || `param-${index}`"
          class="param-item"
        >
          <div class="param-header">
            <div class="param-name">
              <el-tag
                size="small"
                effect="plain"
                class="param-type-tag"
                :type="getParamTypeColor(param.valueType)"
              >
                {{ getParamTypeLabel(param.valueType) }}
              </el-tag>
              <span class="param-name-text">{{ param.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 空状态 -->
    <div v-else class="empty-params">
      <el-empty description="无输出参数" :image-size="60">
        <template #image>
          <div class="empty-icon">
            <el-icon :size="30" color="#909399"><InfoFilled /></el-icon>
          </div>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { InfoFilled, Connection } from "@element-plus/icons-vue";
import { OutputParam } from "@/types/componentType";
import { getParamTypeLabel } from "@/utils/util";

const props = defineProps<{
  removeNodes: (nodeIds: string[]) => void;
  selectedNode: any;
}>();

// 获取参数类型颜色
const getParamTypeColor = (type: number) => {
  const colorMap = [
    "primary", // 文本
    "success", // 数字
    "warning", // 布尔
    "info", // 对象
    "danger" // 数组
  ] as const;
  return colorMap[type] || "info";
};

const outputParams = computed<OutputParam[]>(
  () => props.selectedNode?.data?.outputParams || []
);
</script>

<style scoped>
.panel-section {
  margin-bottom: 24px;
  border-radius: 8px;
  animation: fade-in 0.3s ease-in-out;
}

.output-config-section {
  background-color: #fff;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.03);
}

/* 参数容器样式 */
.params-container {
  margin-top: 16px;
}

.params-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.param-item {
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
  padding: 14px 16px;
  transition: all 0.3s ease;
  border-left: 3px solid var(--el-color-primary-light-5);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.param-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.param-name {
  display: flex;
  align-items: center;
  gap: 10px;
}

.param-type-tag {
  font-size: 12px;
  padding: 0 8px;
  height: 22px;
  line-height: 20px;
  border-radius: 4px;
}

.param-name-text {
  font-weight: 500;
  font-size: 15px;
  color: var(--el-text-color-primary);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-primary);
  font-weight: 600;
  font-size: 16px;
  position: relative;
}

.section-label .el-icon {
  color: var(--el-color-primary);
}

.add-param-btn {
  transition: all 0.2s ease;
  border-radius: 6px;
}

.add-param-btn:hover {
  transform: translateY(-2px);
}

/* 参数卡片样式 */
.params-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

.param-card {
  background: var(--el-bg-color, #fff);
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.param-card:hover {
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.param-card.is-editing {
  box-shadow:
    0 0 0 2px var(--el-color-primary-light-5),
    0 4px 16px 0 rgba(0, 0, 0, 0.08);
}

.param-card.is-start-param {
  border-left: 3px solid var(--el-color-success);
}

.param-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 16px;
  cursor: pointer;
  position: relative;
  background-color: var(--el-fill-color-light);
  transition: background-color 0.2s ease;
}

.param-card-header:hover {
  background-color: var(--el-fill-color);
}

.param-card.is-collapsed .param-card-header::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent,
    var(--el-color-primary-light-8),
    transparent
  );
  opacity: 0.5;
}

.param-main-info {
  display: flex;
  align-items: center;
  flex: 1;
  margin-right: 12px;
}

.param-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.param-name .el-icon {
  color: var(--el-color-info);
  font-size: 14px;
}

.param-actions {
  display: flex;
  gap: 8px;
}

.param-card-content {
  padding: 18px 16px;
  border-top: 1px solid var(--el-border-color-lighter);
  transition: all 0.3s ease;
}

.param-detail {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.param-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.param-field label {
  font-size: 13px;
  color: var(--el-text-color-secondary);
  font-weight: 500;
}

.param-value {
  padding: 10px 12px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 6px;
  min-height: 36px;
  display: flex;
  align-items: center;
}

.source-field {
  color: var(--el-color-primary);
  font-weight: 500;
}

.source-label {
  font-size: 13px;
  color: var(--el-color-success);
  font-weight: 500;
}

.param-select {
  width: 100%;
  --el-select-input-focus-border-color: var(--el-color-primary);
}

.param-error {
  padding: 8px 16px;
  color: var(--el-color-danger);
  font-size: 12px;
  background-color: var(--el-color-danger-light-9);
  border-top: 1px solid var(--el-color-danger-light-7);
}

/* 空状态 */
.empty-params {
  display: flex;
  justify-content: center;
  padding: 30px 0;
}

.empty-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--el-fill-color-light);
  margin: 0 auto;
}

/* 动画 */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.param-item-enter-active,
.param-item-leave-active {
  transition: all 0.3s ease;
}

.param-item-enter-from,
.param-item-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 响应式微调 */
@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .add-param-btn {
    align-self: flex-end;
  }

  .param-card-header {
    flex-wrap: wrap;
    gap: 10px;
  }

  .param-actions {
    margin-left: auto;
  }
}
</style>
