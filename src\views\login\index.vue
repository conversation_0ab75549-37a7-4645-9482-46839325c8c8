<script setup lang="ts">
import Motion from "./utils/motion";
import { useRouter } from "vue-router";
import { message } from "@/utils/message";
import { loginRules } from "./utils/rule";
import { useNav } from "@/layout/hooks/useNav";
import type { FormInstance } from "element-plus";
import { useUserStoreHook } from "@/store/modules/user";
import { initRouter, getTopMenu } from "@/router/utils";
import { bg, avatar, illustration } from "./utils/static";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ref, reactive, toRaw, onMounted, onBeforeUnmount } from "vue";

import Lock from "@iconify-icons/ri/lock-fill";
import User from "@iconify-icons/ri/user-3-fill";

defineOptions({
  name: "Login"
});
const router = useRouter();
const loading = ref(false);
const ruleFormRef = ref<FormInstance>();

const { title } = useNav();

const ruleForm = reactive({
  username: "dcp01",
  password: "IcPt1357"
});

// 粒子动画样式生成
const getParticleStyle = (index: number) => {
  const size = Math.random() * 4 + 2;
  const left = Math.random() * 100;
  const animationDelay = Math.random() * 20;
  const animationDuration = Math.random() * 10 + 15;

  return {
    width: `${size}px`,
    height: `${size}px`,
    left: `${left}%`,
    animationDelay: `${animationDelay}s`,
    animationDuration: `${animationDuration}s`
  };
};

const onLogin = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      loading.value = true;
      useUserStoreHook()
        .loginByUsername({
          UserCode: ruleForm.username,
          Password: ruleForm.password
        })
        .then(async res => {
          if (res) {
            // 获取后端路由
            return initRouter().then(() => {
              router.push(getTopMenu(true).path).then(() => {
                message("登录成功", { type: "success" });
              });
            });
          } else {
            message("登录失败", { type: "error" });
          }
        })
        .finally(() => (loading.value = false));
    }
  });
};

/** 使用公共函数，避免`removeEventListener`失效 */
function onkeypress({ code }: KeyboardEvent) {
  if (["Enter", "NumpadEnter"].includes(code)) {
    onLogin(ruleFormRef.value);
  }
}

onMounted(() => {
  window.document.addEventListener("keypress", onkeypress);
});

onBeforeUnmount(() => {
  window.document.removeEventListener("keypress", onkeypress);
});
</script>

<template>
  <!-- 现代工业风格背景层 -->
  <div class="wave">
    <!-- 动态粒子装饰层 -->
    <div class="particle-layer">
      <div
        v-for="i in 15"
        :key="i"
        class="particle"
        :style="getParticleStyle(i)"
      />
    </div>
  </div>

  <div class="select-none">
    <!-- 主登录区域 -->
    <div class="login-container">
      <div class="login-box">
        <div class="login-form">
          <!-- 顶部装饰线 -->
          <div class="form-header-decoration">
            <div class="decoration-line left" />
            <div class="decoration-center">
              <span class="tech-dot" />
            </div>
            <div class="decoration-line right" />
          </div>

          <!-- Logo和系统标识 -->
          <div class="system-branding">
            <avatar
              class="avatar-quantum"
              tabindex="0"
              role="img"
              :aria-label="`${title} 系统标识 - 量子级微物理动效体验`"
            />
            <Motion>
              <h2 class="system-title">{{ title }}</h2>
            </Motion>
          </div>

          <!-- 登录表单 -->
          <el-form
            ref="ruleFormRef"
            :model="ruleForm"
            :rules="loginRules"
            size="large"
            class="login-form-content"
          >
            <Motion :delay="100">
              <div class="form-group">
                <label class="form-label">用户账号</label>
                <el-form-item
                  :rules="[
                    {
                      required: true,
                      message: '请输入账号',
                      trigger: 'blur'
                    }
                  ]"
                  prop="username"
                >
                  <el-input
                    v-model="ruleForm.username"
                    clearable
                    placeholder="请输入您的账号"
                    :prefix-icon="useRenderIcon(User)"
                    class="modern-input input-enhanced"
                  />
                </el-form-item>
              </div>
            </Motion>

            <Motion :delay="150">
              <div class="form-group">
                <label class="form-label">登录密码</label>
                <el-form-item prop="password">
                  <el-input
                    v-model="ruleForm.password"
                    clearable
                    show-password
                    placeholder="请输入您的密码"
                    :prefix-icon="useRenderIcon(Lock)"
                    class="modern-input input-enhanced"
                  />
                </el-form-item>
              </div>
            </Motion>

            <!-- 登录按钮 -->
            <Motion :delay="250">
              <div class="button-container">
                <el-button
                  class="login-button button-enhanced"
                  size="default"
                  type="primary"
                  :loading="loading"
                  @click="onLogin(ruleFormRef)"
                >
                  <span v-if="!loading" class="button-content">
                    <span class="button-icon">🔐</span>
                    <span class="button-text">安全登录</span>
                    <span class="button-arrow">→</span>
                  </span>
                  <span v-else class="loading-content">
                    <span class="loading-text">验证中...</span>
                  </span>
                </el-button>
              </div>
            </Motion>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import url("@/style/login.css");
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}
</style>
