// 处理类型映射
const PROCESS_TYPE_LABELS: Record<string, string> = {
  math: "数学运算",
  string: "字符串操作",
  typecast: "类型转换",
  logic: "逻辑运算",
  condition: "条件处理",
  date: "日期处理"
};

// 操作映射
const OPERATION_LABELS: Record<string, string> = {
  // 数学运算
  add: "加法",
  subtract: "减法",
  multiply: "乘法",
  divide: "除法",
  modulo: "取余",
  power: "幂运算",
  abs: "绝对值",
  round: "四舍五入",
  ceil: "向上取整",
  floor: "向下取整",
  sqrt: "平方根",

  // 字符串操作
  concat: "拼接",
  substring: "截取",
  replace: "替换",
  uppercase: "转大写",
  lowercase: "转小写",
  trim: "去空格",
  length: "获取长度",
  split: "分割",
  indexOf: "查找位置",

  // 类型转换
  toNumber: "转数字",
  toString: "转字符串",
  toBoolean: "转布尔值",
  toJSON: "转JSON",
  parseJSON: "解析JSON",

  // 逻辑运算
  and: "与",
  or: "或",
  not: "非",
  equal: "等于",
  notEqual: "不等于",
  greater: "大于",
  less: "小于",
  greaterOrEqual: "大于等于",
  lessOrEqual: "小于等于",

  // 条件处理
  ternary: "三元运算",
  nullish: "空值合并",
  default: "默认值",

  // 日期处理
  format: "格式化",
  getYear: "获取年份",
  getMonth: "获取月份",
  getDate: "获取日期",
  getTimestamp: "获取时间戳",
  addDate: "日期加法",
  subtractDate: "日期减法"
};

// 输入源映射
const INPUT_SOURCE_LABELS: Record<string, string> = {
  attribute: "属性值",
  static: "静态值",
  nodeOutput: "节点输出"
};

/**
 * 格式化输入源显示
 * @param configValue 节点配置值
 * @returns 输入源描述
 */
const formatInputSource = (configValue: Record<string, any>): string => {
  const inputSource = configValue.inputSource?.value || "";
  const sourceLabel = INPUT_SOURCE_LABELS[inputSource] || inputSource;

  switch (inputSource) {
    case "attribute":
      const attributeName = configValue.attributeName?.value || "";
      return attributeName ? `${sourceLabel}: ${attributeName}` : sourceLabel;

    case "static":
      const staticValue = configValue.staticValue?.value || "";
      return staticValue ? `${sourceLabel}: ${staticValue}` : sourceLabel;

    case "nodeOutput":
      const nodeOutputRef = configValue.nodeOutputRef?.value || "";
      return nodeOutputRef ? `${sourceLabel}: ${nodeOutputRef}` : sourceLabel;

    default:
      return sourceLabel;
  }
};

/**
 * 格式化操作参数显示
 * @param configValue 节点配置值
 * @param processType 处理类型
 * @param operation 操作类型
 * @returns 参数描述
 */
const formatOperationParams = (
  configValue: Record<string, any>,
  processType: string
): string => {
  const params: string[] = [];

  // 数学运算参数
  if (processType === "math") {
    const operand = configValue.operand?.value;
    if (operand !== undefined && operand !== "") {
      params.push(`操作数: ${operand}`);
    }

    const precision = configValue.precision?.value;
    if (precision !== undefined && precision !== "") {
      params.push(`精度: ${precision}`);
    }
  }

  // 字符串操作参数
  if (processType === "string") {
    const concatValue = configValue.concatValue?.value;
    if (concatValue !== undefined && concatValue !== "") {
      params.push(`拼接: "${concatValue}"`);
    }

    const startIndex = configValue.startIndex?.value;
    const length = configValue.length?.value;
    if (startIndex !== undefined) {
      if (length !== undefined && length !== "") {
        params.push(
          `位置: ${startIndex}-${parseInt(startIndex) + parseInt(length)}`
        );
      } else {
        params.push(`起始: ${startIndex}`);
      }
    }

    const searchValue = configValue.searchValue?.value;
    const replaceValue = configValue.replaceValue?.value;
    if (searchValue !== undefined && replaceValue !== undefined) {
      params.push(`"${searchValue}" → "${replaceValue}"`);
    } else if (searchValue !== undefined) {
      params.push(`查找: "${searchValue}"`);
    }

    const separator = configValue.separator?.value;
    if (separator !== undefined && separator !== "") {
      params.push(`分隔符: "${separator}"`);
    }
  }

  // 逻辑运算参数
  if (processType === "logic") {
    const compareValue = configValue.compareValue?.value;
    if (compareValue !== undefined && compareValue !== "") {
      params.push(`比较值: ${compareValue}`);
    }
  }

  // 条件处理参数
  if (processType === "condition") {
    const condition = configValue.condition?.value;
    const trueValue = configValue.trueValue?.value;
    const falseValue = configValue.falseValue?.value;

    if (condition && trueValue !== undefined && falseValue !== undefined) {
      params.push(`${condition} ? ${trueValue} : ${falseValue}`);
    }

    const defaultValue = configValue.defaultValue?.value;
    if (defaultValue !== undefined && defaultValue !== "") {
      params.push(`默认值: ${defaultValue}`);
    }
  }

  // 日期处理参数
  if (processType === "date") {
    const format = configValue.format?.value;
    if (format !== undefined && format !== "") {
      params.push(`格式: ${format}`);
    }

    const amount = configValue.amount?.value;
    const unit = configValue.unit?.value;
    if (amount !== undefined && unit !== undefined) {
      params.push(`${amount}${unit}`);
    }
  }

  return params.length > 0 ? `(${params.join(", ")})` : "";
};

/**
 * ProcessValue节点配置渲染函数
 * @param configValue 节点配置值
 * @returns 格式化的配置描述字符串
 */
export default (configValue: Record<string, any>): string => {
  if (!configValue || typeof configValue !== "object") {
    return "暂无配置";
  }

  // 获取基本配置
  const processType = configValue.processType?.value || "";
  const operation = configValue.operation?.value || "";

  if (!processType || !operation) {
    return "配置不完整";
  }

  // 格式化显示
  const processTypeLabel = PROCESS_TYPE_LABELS[processType] || processType;
  const operationLabel = OPERATION_LABELS[operation] || operation;
  const inputSourceDesc = formatInputSource(configValue);
  const paramsDesc = formatOperationParams(configValue, processType);

  // 构建显示文本
  const parts: string[] = [];

  // 输入源
  if (inputSourceDesc) {
    parts.push(`输入: ${inputSourceDesc}`);
  }

  // 操作描述
  const operationDesc = `${processTypeLabel} - ${operationLabel}${paramsDesc}`;
  parts.push(`操作: ${operationDesc}`);

  // 如果内容较长，进行分行显示
  if (parts.join(" | ").length > 50) {
    return parts.join("\n");
  } else {
    return parts.join(" | ");
  }
};
