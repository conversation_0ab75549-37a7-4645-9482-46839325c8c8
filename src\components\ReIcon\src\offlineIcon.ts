// 这里存放本地图标，在 src/layout/index.vue 文件中加载，避免在首启动加载
import { addIcon } from "@iconify/vue/dist/offline";

// 本地菜单图标，后端在路由的 icon 中返回对应的图标字符串并且前端在此处使用 addIcon 添加即可渲染菜单图标
// @iconify-icons/ep
import Lollipop from "@iconify-icons/ep/lollipop";
import HomeFilled from "@iconify-icons/ep/home-filled";
addIcon("ep:lollipop", Lollipop);
addIcon("ep:home-filled", HomeFilled);
// @iconify-icons/ri
import Search from "@iconify-icons/ri/search-line";
import InformationLine from "@iconify-icons/ri/information-line";
import User3Fill from "@iconify-icons/ri/user-3-fill";
import ArrowDownSLine from "@iconify-icons/ri/arrow-down-s-line";
import LogoutCircleRLine from "@iconify-icons/ri/logout-circle-r-line";
import HeartFill from "@iconify-icons/ri/heart-fill";
import LeafFill from "@iconify-icons/ri/leaf-fill";
import SunFill from "@iconify-icons/ri/sun-fill";
addIcon("ri:search-line", Search);
addIcon("ri:information-line", InformationLine);
addIcon("ri:user-3-fill", User3Fill);
addIcon("ri:arrow-down-s-line", ArrowDownSLine);
addIcon("ri:logout-circle-r-line", LogoutCircleRLine);
addIcon("ri:heart-fill", HeartFill);
addIcon("ri:leaf-fill", LeafFill);
addIcon("ri:sun-fill", SunFill);
