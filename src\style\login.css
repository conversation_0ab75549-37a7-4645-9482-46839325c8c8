/* 导入量子动效样式 */
@import url("./quantum-animations.css");

/* ========================================
   现代工业化ERP登录界面设计
   设计理念：融合现代工业美学与用户体验
   ======================================== */

/* 基础重置与根设置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  overflow: hidden;
}

/* ========================================
   主体背景层 - 工业科技风格
   ======================================== */

.wave {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;

  /* 主背景 - 深邃的工业蓝灰渐变 */
  background:
    /* 主要科技渐变 */ radial-gradient(
      ellipse at 25% 20%,
      #1e3a8a 0%,
      #1e40af 35%,
      #1d4ed8 100%
    ),
    radial-gradient(ellipse at 75% 80%, #0f172a 0%, #1e293b 50%, #334155 100%),
    linear-gradient(
      135deg,
      #0c4a6e 0%,
      #0369a1 25%,
      #0284c7 50%,
      #0ea5e9 75%,
      #38bdf8 100%
    );

  /* 纹理层叠加 */
  background-blend-mode: multiply, screen, normal;
}

.wave::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.4;

  /* 工业网格纹理 */
  background-image:
    /* 主要网格 - 模拟工业图纸 */
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    /* 精细网格 */
      linear-gradient(rgba(56, 189, 248, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(56, 189, 248, 0.05) 1px, transparent 1px),
    /* 工业标记点 */
      radial-gradient(
        circle at 40px 40px,
        rgba(14, 165, 233, 0.15) 2px,
        transparent 2px
      ),
    radial-gradient(
      circle at 80px 80px,
      rgba(59, 130, 246, 0.1) 1px,
      transparent 1px
    );

  background-size:
    60px 60px,
    60px 60px,
    20px 20px,
    20px 20px,
    80px 80px,
    120px 120px;

  animation: gridFlow 20s linear infinite;
}

.wave::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.6;

  /* 动态光效层 */
  background:
    /* 主要光晕 */
    radial-gradient(
      ellipse 800px 400px at 15% 25%,
      rgba(56, 189, 248, 0.15) 0%,
      transparent 60%
    ),
    /* 次要光效 */
      radial-gradient(
        circle 600px at 85% 75%,
        rgba(14, 165, 233, 0.1) 0%,
        transparent 50%
      ),
    /* 科技光束 */
      radial-gradient(
        ellipse 400px 800px at 90% 10%,
        rgba(3, 105, 161, 0.08) 0%,
        transparent 70%
      ),
    /* 动态扫描线 */
      linear-gradient(
        45deg,
        transparent 40%,
        rgba(56, 189, 248, 0.05) 50%,
        transparent 60%
      ),
    linear-gradient(
      -45deg,
      transparent 35%,
      rgba(14, 165, 233, 0.03) 50%,
      transparent 65%
    );

  animation: lightFlow 15s ease-in-out infinite alternate;
}

/* 动态粒子系统 */
.particle-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.particle {
  position: absolute;
  background: radial-gradient(
    circle,
    rgba(56, 189, 248, 0.8) 0%,
    rgba(14, 165, 233, 0.4) 50%,
    transparent 100%
  );
  border-radius: 50%;
  animation: particleFloat 15s ease-in-out infinite;
}

/* 动画定义 */
@keyframes gridFlow {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(60px, 60px);
  }
}

@keyframes lightFlow {
  0% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 0.5;
  }
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) translateX(0) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(20px) scale(1);
    opacity: 0;
  }
}

/* ========================================
   顶部工具栏设计
   ======================================== */

.top-toolbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 2rem;
  z-index: 1000;
  backdrop-filter: blur(10px);
  background: linear-gradient(
    135deg,
    rgba(15, 23, 42, 0.1) 0%,
    rgba(30, 41, 59, 0.05) 100%
  );
  border-bottom: 1px solid rgba(56, 189, 248, 0.1);
}

.brand-mark {
  display: flex;
  align-items: center;
}

.brand-icon {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-ring {
  position: absolute;
  width: 32px;
  height: 32px;
  border: 2px solid rgba(56, 189, 248, 0.6);
  border-radius: 50%;
  animation: rotate 8s linear infinite;
}

.icon-core {
  position: absolute;
  width: 12px;
  height: 12px;
  background: radial-gradient(circle, #38bdf8 0%, #0ea5e9 100%);
  border-radius: 50%;
  box-shadow: 0 0 12px rgba(56, 189, 248, 0.6);
}

.theme-switcher {
  display: flex;
  align-items: center;
}

.custom-theme-switch {
  --el-switch-on-color: #38bdf8;
  --el-switch-off-color: #475569;
}

/* ========================================
   登录容器布局
   ======================================== */

.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  position: relative;
}

.decorative-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
}

.ring {
  position: absolute;
  border-radius: 50%;
  border: 1px solid rgba(56, 189, 248, 0.1);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ring-1 {
  width: 600px;
  height: 600px;
  background: conic-gradient(
    from 0deg,
    transparent 0%,
    rgba(56, 189, 248, 0.08) 25%,
    transparent 50%,
    rgba(14, 165, 233, 0.12) 75%,
    transparent 100%
  );
  animation: rotate 30s linear infinite;
}

.ring-2 {
  width: 400px;
  height: 400px;
  background: radial-gradient(
    circle at center,
    rgba(14, 165, 233, 0.06) 0%,
    transparent 60%
  );
  animation: rotate 25s linear infinite reverse;
}

.ring-3 {
  width: 200px;
  height: 200px;
  background: radial-gradient(
    circle at center,
    rgba(56, 189, 248, 0.1) 0%,
    transparent 70%
  );
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.login-box {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 450px;
  z-index: 10;
}

/* ========================================
   登录表单核心设计
   ======================================== */

.login-form {
  width: 100%;
  max-width: 420px;
  padding: 3rem 2.5rem;
  position: relative;
  border-radius: 24px;

  /* 工业金属质感背景 */
  background:
    /* 主背景 */
    linear-gradient(
      145deg,
      rgba(15, 23, 42, 0.95) 0%,
      rgba(30, 41, 59, 0.98) 100%
    ),
    /* 金属纹理层 */
      linear-gradient(
        45deg,
        rgba(51, 65, 85, 0.3) 0%,
        rgba(71, 85, 105, 0.2) 100%
      );

  /* 现代工业边框与阴影 */
  border: 1px solid rgba(56, 189, 248, 0.2);
  box-shadow:
    /* 主要深度阴影 */
    0 32px 64px rgba(0, 0, 0, 0.4),
    0 16px 32px rgba(0, 0, 0, 0.3),
    /* 内部光晕 */ inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2),
    /* 科技边框光效 */ 0 0 0 1px rgba(56, 189, 248, 0.1),
    0 4px 12px rgba(56, 189, 248, 0.2);

  backdrop-filter: blur(16px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.login-form::before {
  content: "";
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: 24px;
  padding: 1px;

  /* 动态边框渐变 */
  background: linear-gradient(
    135deg,
    rgba(56, 189, 248, 0.4) 0%,
    rgba(14, 165, 233, 0.3) 25%,
    rgba(3, 105, 161, 0.2) 50%,
    rgba(56, 189, 248, 0.3) 75%,
    rgba(14, 165, 233, 0.4) 100%
  );

  mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  mask-composite: exclude;
  z-index: -1;
  animation: borderGlow 3s ease-in-out infinite alternate;
}

@keyframes borderGlow {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.6;
  }
}

.login-form:hover {
  transform: translateY(-4px);
  box-shadow:
    0 40px 80px rgba(0, 0, 0, 0.5),
    0 20px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    0 0 0 1px rgba(56, 189, 248, 0.2),
    0 8px 24px rgba(56, 189, 248, 0.3);
}

/* ========================================
   表单头部装饰设计
   ======================================== */

.form-header-decoration {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
  padding: 0 1rem;
}

.decoration-line {
  height: 1px;
  flex: 1;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(56, 189, 248, 0.6) 50%,
    transparent 100%
  );
}

.decoration-center {
  margin: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tech-dot {
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, #38bdf8 0%, #0ea5e9 100%);
  border-radius: 50%;
  box-shadow:
    0 0 12px rgba(56, 189, 248, 0.8),
    0 0 24px rgba(56, 189, 248, 0.4);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

/* ========================================
   系统品牌标识设计
   ======================================== */

.system-branding {
  text-align: center;
  margin-bottom: 2.5rem;
}

/*
   注意：新的登录页面现在使用 .avatar-tailwind 类，
   该类基于 Tailwind CSS 自定义动画实现。
   此 .avatar 类保留用于向后兼容。
*/
.avatar {
  width: 180px;
  height: auto;
  margin: 0 auto 1.5rem;
  filter: drop-shadow(0 4px 12px rgba(56, 189, 248, 0.3));

  /* 微动画基础设置 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
  cursor: pointer;

  /* 添加微妙的初始状态 */
  transform: scale(1) rotate(0deg);

  /* 静态时的微妙脉冲动画 */
  animation: avatarBreath 4s ease-in-out infinite;
}

.avatar:hover {
  /* 微妙的缩放和旋转效果 */
  transform: scale(1.05) rotate(2deg);

  /* 增强阴影效果 */
  filter: drop-shadow(0 8px 24px rgba(56, 189, 248, 0.4))
    drop-shadow(0 4px 12px rgba(14, 165, 233, 0.3)) brightness(1.1);

  /* 停止呼吸动画，使用发光动画 */
  animation: avatarGlow 0.3s ease-out forwards;
}

/* 焦点状态（可访问性增强） */
.avatar:focus {
  outline: 2px solid rgba(56, 189, 248, 0.5);
  outline-offset: 4px;
  transform: scale(1.03) rotate(1deg);
  filter: drop-shadow(0 6px 18px rgba(56, 189, 248, 0.35)) brightness(1.08);
}

/* 发光动画关键帧 */
@keyframes avatarGlow {
  0% {
    filter: drop-shadow(0 4px 12px rgba(56, 189, 248, 0.3)) brightness(1);
  }
  50% {
    filter: drop-shadow(0 12px 32px rgba(56, 189, 248, 0.5))
      drop-shadow(0 6px 16px rgba(14, 165, 233, 0.4)) brightness(1.15);
  }
  100% {
    filter: drop-shadow(0 8px 24px rgba(56, 189, 248, 0.4))
      drop-shadow(0 4px 12px rgba(14, 165, 233, 0.3)) brightness(1.1);
  }
}

/* 鼠标离开时的平滑过渡，然后恢复呼吸动画 */
.avatar:not(:hover) {
  animation:
    avatarReturn 0.3s ease-out forwards,
    avatarBreath 4s ease-in-out 0.3s infinite;
}

@keyframes avatarReturn {
  0% {
    transform: scale(1.05) rotate(2deg);
    filter: drop-shadow(0 8px 24px rgba(56, 189, 248, 0.4))
      drop-shadow(0 4px 12px rgba(14, 165, 233, 0.3)) brightness(1.1);
  }
  100% {
    transform: scale(1) rotate(0deg);
    filter: drop-shadow(0 4px 12px rgba(56, 189, 248, 0.3));
  }
}

/* 静态呼吸动画 - 非常微妙的脉冲效果 */
@keyframes avatarBreath {
  0%,
  100% {
    transform: scale(1) rotate(0deg);
    filter: drop-shadow(0 4px 12px rgba(56, 189, 248, 0.3));
  }
  50% {
    transform: scale(1.01) rotate(0deg);
    filter: drop-shadow(0 5px 14px rgba(56, 189, 248, 0.35));
  }
}

.system-title {
  margin: 1rem 0 0.5rem 0;
  font-size: 1.75rem;
  font-weight: 700;
  letter-spacing: 0.025em;
  text-transform: uppercase;

  /* 现代渐变文字效果 */
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  /* 文字阴影增强立体感 */
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));

  font-family:
    "Segoe UI",
    system-ui,
    -apple-system,
    sans-serif;
  position: relative;
}

.system-title::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #38bdf8 50%,
    transparent 100%
  );
  border-radius: 1px;
}

.system-subtitle {
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 400;
  letter-spacing: 0.05em;
  margin: 0;
  opacity: 0.8;
}

/* ========================================
   表单内容设计
   ======================================== */

.login-form-content {
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  color: #e2e8f0;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  letter-spacing: 0.025em;
}

.login-form :deep(.el-form-item) {
  margin-bottom: 0;
}

.login-form :deep(.el-input__wrapper) {
  height: 56px;
  padding: 0 16px;
  border-radius: 12px;
  border: 2px solid rgba(71, 85, 105, 0.4);

  /* 工业金属质感背景 */
  background: linear-gradient(
    145deg,
    rgba(30, 41, 59, 0.9) 0%,
    rgba(51, 65, 85, 0.95) 100%
  );

  /* 现代阴影系统 */
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.2),
    inset 0 -1px 0 rgba(255, 255, 255, 0.05),
    0 1px 3px rgba(0, 0, 0, 0.1);

  backdrop-filter: blur(8px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.login-form :deep(.el-input__wrapper:hover) {
  border-color: rgba(56, 189, 248, 0.5);
  background: linear-gradient(
    145deg,
    rgba(51, 65, 85, 0.95) 0%,
    rgba(71, 85, 105, 1) 100%
  );

  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.15),
    0 4px 12px rgba(56, 189, 248, 0.15),
    0 0 0 4px rgba(56, 189, 248, 0.05);
}

.login-form :deep(.el-input__wrapper.is-focus) {
  border-color: #38bdf8;
  background: linear-gradient(
    145deg,
    rgba(51, 65, 85, 1) 0%,
    rgba(71, 85, 105, 1) 100%
  );

  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.1),
    0 8px 24px rgba(56, 189, 248, 0.25),
    0 0 0 4px rgba(56, 189, 248, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.login-form :deep(.el-input__inner) {
  color: #f1f5f9;
  font-size: 16px;
  font-weight: 500;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.login-form :deep(.el-input__inner::placeholder) {
  color: #64748b;
  font-weight: 400;
}

.login-form :deep(.el-input__prefix-inner),
.login-form :deep(.el-input__suffix-inner) {
  color: #94a3b8;
  transition: color 0.3s ease;
}

.login-form :deep(.el-input__wrapper:hover .el-input__prefix-inner),
.login-form :deep(.el-input__wrapper:hover .el-input__suffix-inner) {
  color: #38bdf8;
}

/* ========================================
   附加功能区域设计
   ======================================== */

.additional-options {
  margin: 1.5rem 0;
  padding: 0 0.5rem;
}

.security-features {
  display: flex;
  justify-content: center;
  align-items: center;
}

.security-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(
    135deg,
    rgba(16, 185, 129, 0.1) 0%,
    rgba(5, 150, 105, 0.05) 100%
  );
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 20px;
  backdrop-filter: blur(4px);
}

.security-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
  animation: securePulse 2s ease-in-out infinite;
}

.security-dot.active {
  animation: securePulse 1.5s ease-in-out infinite;
}

@keyframes securePulse {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 16px rgba(16, 185, 129, 0.8);
  }
}

.security-text {
  color: #10b981;
  font-size: 0.75rem;
  font-weight: 500;
  letter-spacing: 0.025em;
}

/* ========================================
   登录按钮现代化设计
   ======================================== */

.button-container {
  margin-top: 2rem;
}

.login-button {
  width: 100% !important;
  height: 56px !important;
  border: none !important;
  border-radius: 12px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  letter-spacing: 0.025em !important;
  text-transform: uppercase !important;

  /* 现代工业渐变背景 */
  background: linear-gradient(
    135deg,
    #0ea5e9 0%,
    #0284c7 50%,
    #0369a1 100%
  ) !important;

  /* 立体质感阴影 */
  box-shadow:
    0 8px 16px rgba(14, 165, 233, 0.4),
    0 4px 8px rgba(14, 165, 233, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1) !important;

  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.login-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%
  );
  transition: left 0.6s ease;
}

.login-button:hover {
  transform: translateY(-2px) !important;
  background: linear-gradient(
    135deg,
    #38bdf8 0%,
    #0ea5e9 50%,
    #0284c7 100%
  ) !important;

  box-shadow:
    0 12px 24px rgba(14, 165, 233, 0.5),
    0 8px 16px rgba(14, 165, 233, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.login-button:hover::before {
  left: 100%;
}

.login-button:active {
  transform: translateY(0) !important;
  box-shadow:
    0 4px 8px rgba(14, 165, 233, 0.3),
    inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.button-icon {
  font-size: 1.1rem;
}

.button-text {
  flex: 1;
  text-align: center;
}

.button-arrow {
  font-size: 1.2rem;
  font-weight: bold;
  transition: transform 0.3s ease;
}

.login-button:hover .button-arrow {
  transform: translateX(4px);
}

.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-text {
  color: #e2e8f0;
  font-weight: 500;
}

.login-button.is-loading {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%) !important;
  cursor: not-allowed !important;
}

/* ========================================
   表单底部设计
   ======================================== */

.form-footer {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(71, 85, 105, 0.3);
}

.security-badges {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  background: linear-gradient(
    135deg,
    rgba(71, 85, 105, 0.3) 0%,
    rgba(51, 65, 85, 0.2) 100%
  );
  border: 1px solid rgba(71, 85, 105, 0.4);
  border-radius: 16px;
  backdrop-filter: blur(4px);
}

.badge-icon {
  font-size: 0.75rem;
}

.badge-text {
  color: #94a3b8;
  font-size: 0.6875rem;
  font-weight: 500;
  letter-spacing: 0.025em;
}

.version-info {
  text-align: center;
}

.version-text {
  color: #64748b;
  font-size: 0.75rem;
  font-weight: 400;
  letter-spacing: 0.05em;
  opacity: 0.8;
}

/* ========================================
   响应式设计
   ======================================== */

@media (max-width: 768px) {
  .top-toolbar {
    padding: 0 1rem;
    height: 60px;
  }

  .login-container {
    padding: 1rem;
  }

  .login-form {
    padding: 2rem 1.5rem;
    margin: 1rem;
  }

  .system-title {
    font-size: 1.5rem;
    margin: 0.5rem 0 2rem 0;
  }

  .login-form :deep(.el-input__wrapper),
  .login-button {
    height: 48px !important;
  }

  .avatar {
    width: 120px;
    height: auto;
    margin-bottom: 1rem;
  }

  /* 移动端减少动画强度 */
  .avatar:hover {
    transform: scale(1.03) rotate(1deg);
    filter: drop-shadow(0 6px 18px rgba(56, 189, 248, 0.35))
      drop-shadow(0 3px 9px rgba(14, 165, 233, 0.25)) brightness(1.08);
  }

  .security-badges {
    gap: 0.5rem;
  }

  .badge {
    padding: 0.2rem 0.5rem;
  }

  .badge-text {
    font-size: 0.625rem;
  }
}

@media (max-width: 480px) {
  .top-toolbar {
    padding: 0 0.75rem;
  }

  .login-form {
    padding: 1.5rem 1rem;
    border-radius: 16px;
  }

  .system-title {
    font-size: 1.25rem;
  }

  .system-subtitle {
    font-size: 0.8125rem;
  }

  /* 小屏幕进一步减少动画效果 */
  .avatar {
    width: 100px;
  }

  .avatar:hover {
    transform: scale(1.02) rotate(0.5deg);
    filter: drop-shadow(0 4px 12px rgba(56, 189, 248, 0.3)) brightness(1.05);
  }

  .wave::before,
  .wave::after {
    opacity: 0.3;
  }

  .decorative-rings {
    display: none;
  }

  .particle-layer {
    opacity: 0.5;
  }

  .security-badges {
    justify-content: space-between;
  }

  .badge {
    flex: 1;
    justify-content: center;
    min-width: 70px;
  }
}

/* ========================================
   深色主题适配
   ======================================== */

html[data-theme="default"] .wave {
  background: radial-gradient(
      ellipse at 25% 0%,
      rgba(15, 23, 42, 0.95) 0%,
      rgba(2, 6, 23, 0.98) 50%
    ),
    radial-gradient(
      ellipse at 75% 100%,
      rgba(30, 41, 59, 0.9) 0%,
      rgba(15, 23, 42, 0.95) 50%
    ),
    linear-gradient(
      135deg,
      #020617 0%,
      #0f172a 25%,
      #1e293b 50%,
      #334155 75%,
      #475569 100%
    );
}

html[data-theme="default"] .wave::before {
  background-image: linear-gradient(
      rgba(59, 130, 246, 0.2) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(59, 130, 246, 0.2) 1px, transparent 1px),
    linear-gradient(rgba(148, 163, 184, 0.15) 1px, transparent 1px),
    linear-gradient(90deg, rgba(148, 163, 184, 0.15) 1px, transparent 1px),
    radial-gradient(
      circle at 50px 50px,
      rgba(59, 130, 246, 0.25) 2px,
      transparent 2px
    ),
    radial-gradient(
      circle at 100px 100px,
      rgba(16, 185, 129, 0.15) 1px,
      transparent 1px
    );
}

html[data-theme="default"] .wave::after {
  background: radial-gradient(
      ellipse 800px 400px at 20% 30%,
      rgba(59, 130, 246, 0.12) 0%,
      rgba(59, 130, 246, 0.04) 40%,
      transparent 70%
    ),
    radial-gradient(
      circle 600px at 80% 70%,
      rgba(147, 51, 234, 0.08) 0%,
      transparent 50%
    ),
    radial-gradient(
      ellipse 300px 800px at 90% 20%,
      rgba(16, 185, 129, 0.06) 0%,
      transparent 60%
    ),
    linear-gradient(
      45deg,
      transparent 20%,
      rgba(59, 130, 246, 0.04) 50%,
      transparent 80%
    ),
    linear-gradient(
      -45deg,
      transparent 30%,
      rgba(147, 51, 234, 0.03) 50%,
      transparent 70%
    );
}

html[data-theme="default"] .login-form {
  background: linear-gradient(
      145deg,
      rgba(15, 23, 42, 0.95) 0%,
      rgba(30, 41, 59, 0.98) 100%
    ),
    linear-gradient(
      45deg,
      rgba(51, 65, 85, 0.3) 0%,
      rgba(71, 85, 105, 0.2) 100%
    );

  border-color: rgba(59, 130, 246, 0.1);

  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.4),
    0 16px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.05),
    0 0 0 1px rgba(59, 130, 246, 0.1),
    0 4px 12px rgba(59, 130, 246, 0.2);
}

html[data-theme="default"] .system-title {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

html[data-theme="default"] .login-form :deep(.el-input__wrapper) {
  background: linear-gradient(
    145deg,
    rgba(30, 41, 59, 0.8) 0%,
    rgba(51, 65, 85, 0.9) 100%
  );
  border-color: rgba(71, 85, 105, 0.6);

  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.2),
    inset 0 -1px 0 rgba(255, 255, 255, 0.05),
    0 1px 3px rgba(0, 0, 0, 0.1);
}

html[data-theme="default"] .login-form :deep(.el-input__wrapper:hover) {
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(51, 65, 85, 1);

  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.15),
    0 4px 12px rgba(59, 130, 246, 0.15),
    0 0 0 4px rgba(59, 130, 246, 0.05);
}

html[data-theme="default"] .login-form :deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  background: rgba(51, 65, 85, 1);

  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.1),
    0 8px 24px rgba(59, 130, 246, 0.25),
    0 0 0 4px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

html[data-theme="default"] .login-form :deep(.el-input__inner) {
  color: #f1f5f9;
}

html[data-theme="default"] .login-form :deep(.el-input__inner::placeholder) {
  color: #64748b;
}

/* 深色主题下的 avatar 动画效果 */
html[data-theme="default"] .avatar {
  filter: drop-shadow(0 4px 12px rgba(59, 130, 246, 0.3));
}

html[data-theme="default"] .avatar:hover {
  filter: drop-shadow(0 8px 24px rgba(59, 130, 246, 0.4))
    drop-shadow(0 4px 12px rgba(37, 99, 235, 0.3)) brightness(1.1);
}

html[data-theme="default"] @keyframes avatarGlow {
  0% {
    filter: drop-shadow(0 4px 12px rgba(59, 130, 246, 0.3)) brightness(1);
  }
  50% {
    filter: drop-shadow(0 12px 32px rgba(59, 130, 246, 0.5))
      drop-shadow(0 6px 16px rgba(37, 99, 235, 0.4)) brightness(1.15);
  }
  100% {
    filter: drop-shadow(0 8px 24px rgba(59, 130, 246, 0.4))
      drop-shadow(0 4px 12px rgba(37, 99, 235, 0.3)) brightness(1.1);
  }
}

html[data-theme="default"] @keyframes avatarBreath {
  0%,
  100% {
    filter: drop-shadow(0 4px 12px rgba(59, 130, 246, 0.3));
  }
  50% {
    filter: drop-shadow(0 5px 14px rgba(59, 130, 246, 0.35));
  }
}

html[data-theme="default"] .login-button {
  background: linear-gradient(
    135deg,
    #1e40af 0%,
    #1d4ed8 50%,
    #2563eb 100%
  ) !important;

  box-shadow:
    0 8px 16px rgba(59, 130, 246, 0.4),
    0 4px 8px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

html[data-theme="default"] .login-button:hover {
  background: linear-gradient(
    135deg,
    #2563eb 0%,
    #3b82f6 50%,
    #60a5fa 100%
  ) !important;

  box-shadow:
    0 12px 24px rgba(59, 130, 246, 0.5),
    0 8px 16px rgba(59, 130, 246, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

/* ========================================
   量子级微物理动效系统
   ======================================== */

/* ========================================
   Tailwind CSS 动画实现（已弃用）
   ======================================== */

/* Avatar 组件 - 全新生命力动画系统 */
.avatar-vitality {
  @apply w-44 h-auto mx-auto mb-6 cursor-pointer;
  @apply transition-all duration-400 ease-smooth;
  @apply transform-gpu origin-center;
  @apply animate-quantum-breathing;

  /* 基础生命力效果 */
  filter: drop-shadow(0 4px 12px rgba(56, 189, 248, 0.3)) brightness(1);
  border-radius: 50%;
  position: relative;
  overflow: visible;
}

/* 添加生命力光环效果 */
.avatar-vitality::before {
  content: "";
  position: absolute;
  top: -10%;
  left: -10%;
  right: -10%;
  bottom: -10%;
  background: radial-gradient(
    circle,
    rgba(56, 189, 248, 0.1) 0%,
    rgba(56, 189, 248, 0.05) 40%,
    transparent 70%
  );
  border-radius: 50%;
  z-index: -1;
  animation: quantum-field-oscillation 3s ease-in-out infinite;
}

/* 添加能量粒子效果 */
.avatar-vitality::after {
  content: "";
  position: absolute;
  top: -5%;
  left: -5%;
  right: -5%;
  bottom: -5%;
  background: radial-gradient(
      circle at 30% 30%,
      rgba(56, 189, 248, 0.15) 0%,
      transparent 30%
    ),
    radial-gradient(
      circle at 70% 70%,
      rgba(14, 165, 233, 0.1) 0%,
      transparent 25%
    );
  border-radius: 50%;
  z-index: -1;
  animation: quantum-particle-dance 2.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 悬停时的磁性吸引效果 */
.avatar-vitality:hover {
  @apply animate-quantum-entanglement;

  /* 停止量子呼吸动画，启动量子纠缠动画 */
  animation: quantum-entanglement 0.8s cubic-bezier(0.68, -0.6, 0.32, 1.6)
    forwards;
}

/* 悬停时的量子场增强 */
.avatar-vitality:hover::before {
  animation: quantum-field-amplification 0.8s
    cubic-bezier(0.68, -0.6, 0.32, 1.6) forwards;
}

/* 悬停时的粒子爆发 */
.avatar-vitality:hover::after {
  animation: quantum-particle-burst 0.8s cubic-bezier(0.68, -0.6, 0.32, 1.6)
    forwards;
}

/* 点击时的量子叠加效果 */
.avatar-vitality:active {
  @apply animate-quantum-superposition;
  animation: quantum-superposition 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)
    forwards;
}

/* 焦点状态 - 生命力聚焦效果 */
.avatar-vitality:focus {
  @apply outline-2 outline-sky-400/50 outline-offset-4;

  /* 聚焦时的生命力集中效果 */
  animation: avatar-whisper 6s ease-in-out infinite;
  filter: drop-shadow(0 8px 20px rgba(56, 189, 248, 0.5)) brightness(1.12)
    saturate(1.2);
}

/* 焦点状态的光环效果 */
.avatar-vitality:focus::before {
  animation: breath-of-life 4s ease-in-out infinite;
}

/* 焦点状态的能量效果 */
.avatar-vitality:focus::after {
  animation: natural-rhythm 5s ease-in-out infinite;
}

.avatar-tailwind:not(:hover) {
  animation:
    avatar-return 0.3s ease-out forwards,
    avatar-breath 4s ease-in-out 0.3s infinite;
}

/* 生命力动画响应式调整 */
@media (max-width: 768px) {
  .avatar-vitality {
    @apply w-32;

    /* 移动端减少动画强度 */
    animation: quantum-breathing 4s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  }

  .avatar-vitality::before {
    animation: quantum-field-oscillation 3s ease-in-out infinite;
  }

  .avatar-vitality::after {
    animation: quantum-particle-dance 2.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .avatar-vitality:hover {
    /* 移动端量子效果减弱 */
    animation: quantum-superposition 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)
      forwards;
  }

  .avatar-vitality:hover::before {
    animation: quantum-field-oscillation 3s ease-in-out infinite;
  }
}

@media (max-width: 480px) {
  .avatar-vitality {
    @apply w-28;

    /* 小屏幕最小化动画 */
    animation: quantum-breathing 4s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  }

  .avatar-vitality::before {
    animation: none; /* 移除光环动画 */
    opacity: 0.5;
  }

  .avatar-vitality::after {
    animation: quantum-particle-dance 2.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .avatar-vitality:hover {
    /* 小屏幕简化悬停效果 */
    animation: quantum-superposition 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)
      forwards;
  }
}

/* 深色主题生命力适配 */
html[data-theme="default"] .avatar-vitality {
  filter: drop-shadow(0 4px 12px rgba(59, 130, 246, 0.3)) brightness(1);
}

html[data-theme="default"] .avatar-vitality::before {
  background: radial-gradient(
    circle,
    rgba(59, 130, 246, 0.12) 0%,
    rgba(59, 130, 246, 0.06) 40%,
    transparent 70%
  );
}

html[data-theme="default"] .avatar-vitality::after {
  background: radial-gradient(
      circle at 30% 30%,
      rgba(59, 130, 246, 0.18) 0%,
      transparent 30%
    ),
    radial-gradient(
      circle at 70% 70%,
      rgba(37, 99, 235, 0.12) 0%,
      transparent 25%
    );
}

html[data-theme="default"] .avatar-vitality:focus {
  @apply outline-blue-400/50;

  filter: drop-shadow(0 8px 20px rgba(59, 130, 246, 0.5)) brightness(1.12)
    saturate(1.2);
}

/* 通用微动画工具类 */
.animate-gentle-hover {
  @apply transition-all duration-250 ease-gentle;
}

.animate-gentle-hover:hover {
  @apply animate-quantum-superposition;
}

.animate-soft-glow {
  @apply animate-quantum-breathing;
}

.animate-floating {
  @apply animate-quantum-field-oscillation;
}

.animate-micro-interaction {
  @apply transition-transform duration-200 ease-bounce-soft;
}

.animate-micro-interaction:hover {
  @apply scale-105;
}

.animate-micro-interaction:active {
  @apply scale-95;
}

/* 按钮增强动画 */
.button-enhanced {
  @apply transition-all duration-300 ease-smooth;
  @apply transform-gpu;
}

.button-enhanced:hover {
  @apply animate-quantum-superposition;
  @apply shadow-lg shadow-blue-500/25;
}

.button-enhanced:active {
  @apply scale-95;
}

/* 表单输入框增强动画 */
.input-enhanced {
  @apply transition-all duration-350 ease-gentle;
}

.input-enhanced:focus {
  @apply animate-quantum-breathing;
  @apply shadow-lg shadow-blue-500/20;
}

/* ========================================
   性能优化
   ======================================== */

/* 启用硬件加速 */
.wave,
.wave::before,
.wave::after,
.login-form,
.decorative-rings,
.particle {
  will-change: transform, opacity;
  transform: translateZ(0);
}

/* 减少重绘 */
.login-form :deep(.el-input__wrapper),
.login-button {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  perspective: 1000px;
}
