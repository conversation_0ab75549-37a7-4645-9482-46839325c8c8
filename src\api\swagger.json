{"openapi": "3.0.1", "info": {"title": "RH.Product.DCP API", "description": "RH.Product.DCP Web API", "version": "v1"}, "paths": {"/api/abp/api-definition": {"get": {"tags": ["AbpApiDefinition"], "parameters": [{"name": "IncludeTypes", "in": "query", "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ApplicationApiDescriptionModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ApplicationApiDescriptionModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ApplicationApiDescriptionModel"}}}}}}}, "/api/abp/application-configuration": {"get": {"tags": ["AbpApplicationConfiguration"], "parameters": [{"name": "IncludeLocalizationResources", "in": "query", "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto"}}}}}}}, "/api/abp/application-localization": {"get": {"tags": ["AbpApplicationLocalization"], "parameters": [{"name": "CultureName", "in": "query", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "OnlyDynamics", "in": "query", "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto"}}}}}}}, "/Alarm/GetAlarmByTimeRange": {"post": {"tags": ["Alarm"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Alarm.AlarmQueryDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Alarm.AlarmQueryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Alarm.AlarmQueryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Alarm.AlarmQueryDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Alarm.AlarmEventData, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Alarm.AlarmEventData, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Alarm.AlarmEventData, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}}}}}}, "/Alarm/SaveAlarm": {"post": {"tags": ["Alarm"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Alarm.AlarmEventData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Alarm.AlarmEventData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Alarm.AlarmEventData"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Alarm.AlarmEventData"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.OpResult"}}}}}}}, "/AlarmTools/GetAlarmInfoByID": {"get": {"tags": ["AlarmTools"], "summary": "通过报警ID获取报警信息", "parameters": [{"name": "alarmID", "in": "query", "description": "", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Component.ComponentAlarmDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Component.ComponentAlarmDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Component.ComponentAlarmDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/AlarmTools/ExportAlarmManual": {"get": {"tags": ["AlarmTools"], "summary": "导出报警手册", "parameters": [{"name": "isIncludeAll", "in": "query", "description": "是否包含所有的报警信息，默认为当前项目所用到零件", "style": "form", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Alarm.ProjectAlarmManualDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Alarm.ProjectAlarmManualDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Alarm.ProjectAlarmManualDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}}}}}}, "/Component/AddComponent": {"post": {"tags": ["Component"], "summary": "添加新组件", "requestBody": {"description": "完整的组件信息数据传输对象", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.web.FullComponentInfoDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.web.FullComponentInfoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.web.FullComponentInfoDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.web.FullComponentInfoDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/Component/DeleteComponentByID": {"delete": {"tags": ["Component"], "summary": "根据ID删除组件", "requestBody": {"description": "包含组件ID的查询对象", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/Component/GetAlarmsByID": {"post": {"tags": ["Component"], "summary": "根据ID获取组件告警信息", "requestBody": {"description": "包含组件ID的查询对象", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentAlarmDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentAlarmDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentAlarmDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}}}}}}, "/Component/GetAlarmsByName": {"post": {"tags": ["Component"], "summary": "根据名称获取组件告警信息", "requestBody": {"description": "包含组件名称的查询对象", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithNameDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithNameDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithNameDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithNameDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentAlarmDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentAlarmDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentAlarmDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}}}}}}, "/Component/GetAllComponent": {"get": {"tags": ["Component"], "summary": "获取全部组件信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.web.FullComponentInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.web.FullComponentInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.web.FullComponentInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}}}}}}, "/Component/GetAllComponentOverviewInfo": {"get": {"tags": ["Component"], "summary": "获取所有组件概要信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentBasicInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentBasicInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentBasicInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}}}}}}, "/Component/GetAttributesByID": {"post": {"tags": ["Component"], "summary": "根据ID获取组件属性", "requestBody": {"description": "包含组件ID的查询对象", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentAttributeDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentAttributeDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentAttributeDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}}}}}}, "/Component/GetAttributesByName": {"post": {"tags": ["Component"], "summary": "根据Name获取组件属性", "requestBody": {"description": "包含组件Name的查询对象", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithNameDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithNameDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithNameDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithNameDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentAttributeDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentAttributeDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentAttributeDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}}}}}}, "/Component/GetComponentByID": {"post": {"tags": ["Component"], "summary": "根据ID获取单个组件详细信息", "requestBody": {"description": "包含组件ID的查询对象", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Component.web.FullComponentInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Component.web.FullComponentInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Component.web.FullComponentInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/Component/GetFunctionsByID": {"post": {"tags": ["Component"], "summary": "根据ID获取组件功能信息", "requestBody": {"description": "包含组件ID的查询对象", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentFunctionDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentFunctionDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentFunctionDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}}}}}}, "/Component/GetFunctionsByName": {"post": {"tags": ["Component"], "summary": "根据名称获取组件功能信息", "requestBody": {"description": "包含组件名称的查询对象", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithNameDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithNameDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithNameDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithNameDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentFunctionDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentFunctionDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentFunctionDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}}}}}}, "/Component/GetSubcomponentsByID": {"post": {"tags": ["Component"], "summary": "根据ID获取子组件列表", "requestBody": {"description": "包含组件ID的查询对象", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.SubcomponentInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.SubcomponentInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.SubcomponentInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}}}}}}, "/Component/GetSubcomponentsByName": {"post": {"tags": ["Component"], "summary": "根据名称获取子组件列表", "requestBody": {"description": "包含组件名称的查询对象", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithNameDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithNameDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithNameDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentQueryWithNameDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.SubcomponentInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.SubcomponentInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.SubcomponentInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}}}}}}, "/Component/UpdateComponent": {"post": {"tags": ["Component"], "summary": "更新组件信息", "requestBody": {"description": "完整的组件信息数据传输对象", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.web.FullComponentInfoDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.web.FullComponentInfoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.web.FullComponentInfoDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.web.FullComponentInfoDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/api/File/upload": {"post": {"tags": ["File"], "summary": "上传文件", "parameters": [{"name": "relativePath", "in": "query", "description": "文件存储的相对路径（可选），若路径已存在同名文件则会自动覆盖", "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.OpResult"}}}}}}}, "/api/File": {"get": {"tags": ["File"], "summary": "下载文件", "parameters": [{"name": "filePath", "in": "query", "description": "文件存储路径(从文件列表接口获取的完整路径)", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["File"], "summary": "删除文件或目录", "parameters": [{"name": "filePath", "in": "query", "description": "要删除的文件或目录的相对路径", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.OpResult"}}}}}}}, "/api/File/list": {"get": {"tags": ["File"], "summary": "获取文件列表", "parameters": [{"name": "relativePath", "in": "query", "description": "查询的相对路径（可选）", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.OpResult"}}}}}}}, "/ProjectManager/GetAllProject": {"get": {"tags": ["ProjectManager"], "summary": "获取所有项目", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Project.ProjectBasicInfoWithIdDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Project.ProjectBasicInfoWithIdDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Project.ProjectBasicInfoWithIdDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}}}}}}, "/ProjectManager/GetProject/{projectId}": {"get": {"tags": ["ProjectManager"], "summary": "根据项目ID获取项目信息", "parameters": [{"name": "projectId", "in": "path", "description": "项目ID", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Project.ProjectInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Project.ProjectInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Project.ProjectInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/ProjectManager/AddProject": {"post": {"tags": ["ProjectManager"], "summary": "添加项目", "requestBody": {"description": "项目信息", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectInfoDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectInfoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectInfoDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectInfoDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/ProjectManager/DeleteProject/{projectId}": {"delete": {"tags": ["ProjectManager"], "summary": "删除项目", "parameters": [{"name": "projectId", "in": "path", "description": "项目ID", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/Runtime/ChangeProject": {"post": {"tags": ["Runtime"], "summary": "切换项目", "parameters": [{"name": "projectId", "in": "query", "description": "项目ID", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/Runtime/ExitProject": {"post": {"tags": ["Runtime"], "summary": "退出项目", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/Runtime/SaveProject": {"post": {"tags": ["Runtime"], "summary": "保存项目", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/Runtime/GetActivedProjectInfo": {"get": {"tags": ["Runtime"], "summary": "获取当前选中的项目信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult`1[[RH.Product.DCP.DomainModel.Project.ProjectInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult`1[[RH.Product.DCP.DomainModel.Project.ProjectInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult`1[[RH.Product.DCP.DomainModel.Project.ProjectInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/Runtime/UISwitch": {"post": {"tags": ["Runtime"], "summary": "切换UI界面", "requestBody": {"description": "界面元素", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Platform.ActiveUIElementsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Platform.ActiveUIElementsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Platform.ActiveUIElementsDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Platform.ActiveUIElementsDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/Runtime/Start": {"post": {"tags": ["Runtime"], "summary": "一键启动", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/Runtime/Stop": {"post": {"tags": ["Runtime"], "summary": "一键停止", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/Runtime/SaveProjectBasicInfo": {"post": {"tags": ["Runtime"], "summary": "保存项目基本信息", "requestBody": {"description": "基础信息", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectBasicInfoDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectBasicInfoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectBasicInfoDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectBasicInfoDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/Runtime/SaveProjectGlobalAttribute": {"post": {"tags": ["Runtime"], "summary": "保存全局属性", "requestBody": {"description": "全局属性", "content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectGlobalAttributeDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectGlobalAttributeDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectGlobalAttributeDto"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectGlobalAttributeDto"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/Runtime/GetProjectOverviewInfo": {"post": {"tags": ["Runtime"], "summary": "获取项目概要信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult`1[[RH.Product.DCP.DomainModel.Project.ProjectOverviewDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult`1[[RH.Product.DCP.DomainModel.Project.ProjectOverviewDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult`1[[RH.Product.DCP.DomainModel.Project.ProjectOverviewDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/RuntimeAttribute/ReadAttribute": {"post": {"tags": ["RuntimeAttribute"], "summary": "读取属性", "parameters": [{"name": "attributeName", "in": "query", "description": "", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}}}}}}, "/RuntimeAttribute/WriteAttribute": {"post": {"tags": ["RuntimeAttribute"], "summary": "写入属性", "parameters": [{"name": "attributeName", "in": "query", "description": "", "style": "form", "schema": {"type": "string"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {}}, "application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/RuntimeComponent/AddComponent": {"post": {"tags": ["RuntimeComponent"], "summary": "添加零件", "requestBody": {"description": "项目零件信息", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectComponentInfoDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectComponentInfoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectComponentInfoDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectComponentInfoDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/RuntimeComponent/DeleteComponent/{componentName}": {"delete": {"tags": ["RuntimeComponent"], "summary": "删除零件", "parameters": [{"name": "componentName", "in": "path", "description": "零件名", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/RuntimeComponent/GetComponents": {"get": {"tags": ["RuntimeComponent"], "summary": "获取零件概览列表", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Project.ProjectComponentOverviewDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Project.ProjectComponentOverviewDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Project.ProjectComponentOverviewDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}}}}}}, "/RuntimeComponent/GetComponent/{componentName}": {"get": {"tags": ["RuntimeComponent"], "summary": "获取零件信息", "parameters": [{"name": "componentName", "in": "path", "description": "零件名", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Project.ProjectComponentInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Project.ProjectComponentInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Project.ProjectComponentInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/RuntimeComponent/UpdateComponent/{componentName}": {"put": {"tags": ["RuntimeComponent"], "summary": "更新零件信息", "parameters": [{"name": "componentName", "in": "path", "description": "零件名", "required": true, "style": "simple", "schema": {"type": "string"}}], "requestBody": {"description": "更新的零件信息", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectComponentInfoDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectComponentInfoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectComponentInfoDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectComponentInfoDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/RuntimeComponent/InitAll": {"post": {"tags": ["RuntimeComponent"], "summary": "初始化所有零件", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/RuntimeComponent/IdleAll": {"post": {"tags": ["RuntimeComponent"], "summary": "使空闲所有零件", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/RuntimeComponent/AbortAll": {"post": {"tags": ["RuntimeComponent"], "summary": "终止所有零件", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/RuntimeComponent/InitComponent/{componentName}": {"post": {"tags": ["RuntimeComponent"], "summary": "初始化零件", "parameters": [{"name": "componentName", "in": "path", "description": "零件名", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/RuntimeComponent/IdleComponent/{componentName}": {"post": {"tags": ["RuntimeComponent"], "summary": "使空闲零件", "parameters": [{"name": "componentName", "in": "path", "description": "零件名", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/RuntimeComponent/AbortComponent/{componentName}": {"post": {"tags": ["RuntimeComponent"], "summary": "终止零件", "parameters": [{"name": "componentName", "in": "path", "description": "零件名", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/RuntimeComponent/GetComponentDefaultTreeInfoWithAdd": {"post": {"tags": ["RuntimeComponent"], "summary": "项目新建零件时获取新建组件的默认配置信息", "requestBody": {"description": "新建组件基础信息", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.SubcomponentInfoDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.SubcomponentInfoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.SubcomponentInfoDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.SubcomponentInfoDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Project.ProjectComponentInfoTreeDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Project.ProjectComponentInfoTreeDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Project.ProjectComponentInfoTreeDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/RuntimeDriver/AddDriver": {"post": {"tags": ["RuntimeDriver"], "summary": "添加驱动", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Driver.DriverInfoDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Driver.DriverInfoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Driver.DriverInfoDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Driver.DriverInfoDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/RuntimeDriver/DeleteDriver/{driverName}": {"delete": {"tags": ["RuntimeDriver"], "summary": "删除驱动", "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "path", "description": "", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/RuntimeDriver/UpdateDriver/{driverName}": {"put": {"tags": ["RuntimeDriver"], "summary": "更新驱动", "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "path", "description": "驱动名字", "required": true, "style": "simple", "schema": {"type": "string"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Driver.DriverInfoDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Driver.DriverInfoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Driver.DriverInfoDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Driver.DriverInfoDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/RuntimeDriver/GetDriverList": {"get": {"tags": ["RuntimeDriver"], "summary": "获取驱动列表", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Driver.DriverInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Driver.DriverInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Driver.DriverInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}}}}}}, "/RuntimeDriver/GetDriver/{driverName}": {"get": {"tags": ["RuntimeDriver"], "summary": "获取驱动信息", "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "path", "description": "驱动名字", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Driver.DriverInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Driver.DriverInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Driver.DriverInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/RuntimeDriver/GetDriverOverviewList": {"get": {"tags": ["RuntimeDriver"], "summary": "获取驱动概述列表", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Project.ProjectDriverOverviewDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Project.ProjectDriverOverviewDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Project.ProjectDriverOverviewDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}}}}}}, "/RuntimeDriver/StartDriver/{driverName}": {"post": {"tags": ["RuntimeDriver"], "summary": "启动驱动", "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/RuntimeDriver/StopDriver/{driverName}": {"post": {"tags": ["RuntimeDriver"], "summary": "停止驱动", "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/RuntimeDriver/RestartDriver/{driverName}": {"post": {"tags": ["RuntimeDriver"], "summary": "重启驱动", "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/RuntimeDriver/StartAllDriver": {"post": {"tags": ["RuntimeDriver"], "summary": "启动所有驱动", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/RuntimeDriver/StopAllDriver": {"post": {"tags": ["RuntimeDriver"], "summary": "停止所有驱动", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/RuntimeDriver/GetDriverTypesFromDll": {"get": {"tags": ["RuntimeDriver"], "summary": "获取指定dll文件中的驱动类型", "parameters": [{"name": "dll<PERSON><PERSON><PERSON><PERSON>", "in": "query", "description": "dll文件路径", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}}}}}}, "/Test/MQTTTest": {"post": {"tags": ["Test"], "parameters": [{"name": "message", "in": "query", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/Test/CreatProject": {"post": {"tags": ["Test"], "parameters": [{"name": "projectId", "in": "query", "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectInfoDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectInfoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectInfoDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectInfoDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/Test/DeleteProject": {"post": {"tags": ["Test"], "parameters": [{"name": "projectId", "in": "query", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/Test/UpdateProject": {"post": {"tags": ["Test"], "parameters": [{"name": "projectId", "in": "query", "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectInfoDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectInfoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectInfoDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectInfoDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/Test/UpdateProjectProperty": {"post": {"tags": ["Test"], "parameters": [{"name": "projectId", "in": "query", "style": "form", "schema": {"type": "string"}}, {"name": "propName", "in": "query", "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {}}, "application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}, "/Test/GetProject": {"post": {"tags": ["Test"], "parameters": [{"name": "projectId", "in": "query", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Project.ProjectInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Project.ProjectInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Project.ProjectInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/Test/RunFunction": {"post": {"tags": ["Test"], "parameters": [{"name": "componentName", "in": "query", "style": "form", "schema": {"type": "string"}}, {"name": "functionName", "in": "query", "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentArgDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentArgDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentArgDto"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentArgDto"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.CommonModel.OpResult"}}}}}}}}, "components": {"schemas": {"RH.Product.DCP.DomainModel.Alarm.AlarmEventData": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "remark": {"type": "string", "nullable": true}, "needUpdateFields": {"type": "object", "additionalProperties": {}, "nullable": true}, "sourceSystem": {"type": "string", "nullable": true}, "alarmId": {"type": "string", "nullable": true}, "severity": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Alarm.AlarmSeverity"}, "triggeredAt": {"type": "string", "format": "date-time"}, "acknowledgedAt": {"type": "string", "format": "date-time", "nullable": true}, "acknowledgedBy": {"type": "string", "nullable": true}, "status": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Alarm.AlarmStatus"}, "parameters": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.DomainModel.Alarm.AlarmInfoInProjectAlarmManualDto": {"type": "object", "properties": {"index": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "level": {"type": "string", "nullable": true}, "processingMethod": {"type": "array", "items": {"type": "string"}, "description": "可用处理方式", "nullable": true}, "iDs": {"type": "array", "items": {"type": "string"}, "description": "这个报警下面的所有报警ID", "nullable": true}}, "additionalProperties": false, "description": "在项目报警手册中的报警信息"}, "RH.Product.DCP.DomainModel.Alarm.AlarmQueryDto": {"type": "object", "properties": {"isAdvancedQuery": {"type": "boolean"}, "isPaged": {"type": "boolean"}, "autoQueryType": {"$ref": "#/components/schemas/RH.Utils.Core.RhExtension.AutoQueryType"}, "maxRecords": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "pageIndex": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "orderBys": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.OrderByClause"}, "nullable": true}, "needField": {"type": "array", "items": {"type": "string"}, "nullable": true}, "queryDescriptor": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.QueryDescriptor"}, "sourceSystem": {"type": "string", "description": "报警来源系统", "nullable": true}, "alarmId": {"type": "string", "description": "报警ID", "nullable": true}, "severity": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Alarm.AlarmSeverity"}, "startDate": {"type": "string", "description": "查询开始日期", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "description": "查询结束日期", "format": "date-time", "nullable": true}, "acknowledgedAt": {"type": "string", "description": "报警确认时间", "format": "date-time", "nullable": true}, "acknowledgedBy": {"type": "string", "description": "报警确认人", "nullable": true}, "status": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Alarm.AlarmStatus"}}, "additionalProperties": false}, "RH.Product.DCP.DomainModel.Alarm.AlarmSeverity": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "RH.Product.DCP.DomainModel.Alarm.AlarmStatus": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "RH.Product.DCP.DomainModel.Alarm.ProjectAlarmManualDto": {"type": "object", "properties": {"componentName": {"type": "string", "description": "零件类型", "nullable": true}, "componentVersion": {"type": "string", "description": "零件版本", "nullable": true}, "componentID": {"type": "string", "description": "零件ID", "nullable": true}, "alarmInfos": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Alarm.AlarmInfoInProjectAlarmManualDto"}, "description": "报警信息", "nullable": true}}, "additionalProperties": false, "description": "项目报警手册"}, "RH.Product.DCP.DomainModel.Component.Args.ComponentFlowNodeTemplateArgDesDto": {"type": "object", "properties": {"name": {"type": "string", "description": "名称", "nullable": true}, "type": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Platform.Enums.PlatformValueType"}, "description": {"type": "string", "description": "描述", "nullable": true}}, "additionalProperties": false, "description": "零件节点模板输入参数"}, "RH.Product.DCP.DomainModel.Component.ComponentAlarmDto": {"type": "object", "properties": {"index": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "level": {"type": "string", "nullable": true}, "processingMethod": {"type": "array", "items": {"type": "string"}, "description": "可用处理方式", "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.DomainModel.Component.ComponentArgDto": {"type": "object", "properties": {"name": {"type": "string", "description": "参数名称", "nullable": true}, "type": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Platform.Enums.PlatformValueType"}, "value": {"description": "值", "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.DomainModel.Component.ComponentAttributeDto": {"type": "object", "properties": {"name": {"type": "string", "description": "名字", "nullable": true}, "displayName": {"type": "string", "description": "显示名", "nullable": true}, "type": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Platform.Enums.PlatformValueType"}, "description": {"type": "string", "description": "描述", "nullable": true}, "unit": {"type": "string", "description": "单位", "nullable": true}, "defaultValue": {"type": "string", "description": "默认值", "nullable": true}, "isPersistent": {"type": "boolean", "description": "是否可持久化"}, "limitRule": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Platform.AttributeLimitRuleDto"}}, "additionalProperties": false, "description": "组件属性"}, "RH.Product.DCP.DomainModel.Component.ComponentBasicInfoDto": {"type": "object", "properties": {"id": {"type": "string", "description": "获取或设置项目的唯一标识符。", "nullable": true}, "name": {"type": "string", "description": "获取或设置项目的名称。", "nullable": true}, "version": {"type": "string", "description": "获取或设置项目的版本号。", "nullable": true}, "versionDescription": {"type": "string", "description": "获取或设置项目版本的描述信息。", "nullable": true}, "description": {"type": "string", "description": "获取或设置项目的描述信息。", "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "description": "获取或设置与项目相关的标签列表。", "nullable": true}, "createTime": {"type": "string", "description": "获取或设置项目创建的时间。", "format": "date-time", "nullable": true}, "author": {"type": "string", "description": "获取或设置项目的作者信息。", "nullable": true}}, "additionalProperties": false, "description": "组件元数据信息"}, "RH.Product.DCP.DomainModel.Component.ComponentExtendedInfoDto": {"type": "object", "properties": {"statusTriggerMap": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ExtendedInfo.StatusTriggerMapDto"}}, "additionalProperties": false}, "RH.Product.DCP.DomainModel.Component.ComponentFlowNodeConfigDto": {"type": "object", "properties": {"name": {"type": "string", "description": "名字", "nullable": true}, "paramType": {"type": "string", "description": "参数类型，为枚举ComponentFlowNodeParamType的字符串表示", "nullable": true}, "valueType": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Platform.Enums.PlatformValueType"}, "value": {"description": "值", "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.DomainModel.Component.ComponentFlowNodeDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "description": {"type": "string", "description": "描述", "nullable": true}, "templateName": {"type": "string", "description": "模板名字", "nullable": true}, "basicConfig": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentFlowNodeConfigDto"}, "description": "节点基础配置列表", "nullable": true}, "nextNodeIds": {"type": "array", "items": {"type": "string"}, "description": "下节点ID列表", "nullable": true}, "previousNodeIds": {"type": "array", "items": {"type": "string"}, "description": "上节点ID列表", "nullable": true}}, "additionalProperties": false, "description": "功能编辑使用的节点数据传输对象"}, "RH.Product.DCP.DomainModel.Component.ComponentFunctionDto": {"type": "object", "properties": {"name": {"type": "string", "description": "名称", "nullable": true}, "description": {"type": "string", "description": "描述", "nullable": true}, "inputArgs": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.Args.ComponentFlowNodeTemplateArgDesDto"}, "description": "输入参数描述", "nullable": true}, "outputArgs": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.Args.ComponentFlowNodeTemplateArgDesDto"}, "description": "输出参数描述", "nullable": true}, "flowNodes": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentFlowNodeDto"}, "description": "流程列表", "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.DomainModel.Component.ComponentQueryWithIDDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.DomainModel.Component.ComponentQueryWithNameDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.DomainModel.Component.Enums.ComponentRunStatus": {"enum": [0, 1, 2, 3, 4, 5, 6], "type": "integer", "description": "零件运行状态", "format": "int32"}, "RH.Product.DCP.DomainModel.Component.ExtendedInfo.StatusTriggerMapDto": {"type": "object", "properties": {"idleHandler": {"type": "string", "description": "使空闲时触发的函数名", "nullable": true}, "initHandler": {"type": "string", "description": "初始化时触发的函数名", "nullable": true}, "stopHandler": {"type": "string", "description": "停止时触发的函数名", "nullable": true}, "abortHandler": {"type": "string", "description": "中止时触发的函数名", "nullable": true}}, "additionalProperties": false, "description": "状态触发映射表"}, "RH.Product.DCP.DomainModel.Component.SubcomponentInfoDto": {"type": "object", "properties": {"name": {"type": "string", "description": "在主零件中子零件名称", "nullable": true}, "className": {"type": "string", "description": "子零件类型", "nullable": true}, "classVersion": {"type": "string", "description": "子零件版本", "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.DomainModel.Component.web.AttributeTransferRuleDto": {"type": "object", "properties": {"currentAttribute": {"type": "string", "nullable": true}, "subAttribute": {"type": "string", "nullable": true}, "transferRule": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Platform.Enums.TransferRuleEnum"}, "fixedValue": {"type": "string", "nullable": true}, "calculationFormula": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.DomainModel.Component.web.FullComponentInfoDto": {"type": "object", "properties": {"basicInfo": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentBasicInfoDto"}, "attributes": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentAttributeDto"}, "description": "属性信息", "nullable": true}, "subcomponents": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.web.fullSubcomponentInfoDto"}, "description": "子零件信息", "nullable": true}, "alarms": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentAlarmDto"}, "description": "报警信息", "nullable": true}, "functions": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.web.fullComponentFunctionDto"}, "description": "功能信息", "nullable": true}, "renderCondition": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.web.RenderConditionDto"}, "description": "渲染条件", "nullable": true}, "extendedInfoDto": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentExtendedInfoDto"}}, "additionalProperties": false}, "RH.Product.DCP.DomainModel.Component.web.RenderConditionDto": {"type": "object", "properties": {"attributeName": {"type": "string", "nullable": true}, "condition": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}, "valueType": {"type": "string", "nullable": true}, "renderResource": {"type": "string", "nullable": true}, "subConditions": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.web.RenderConditionDto"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.DomainModel.Component.web.fullComponentFunctionDto": {"type": "object", "properties": {"name": {"type": "string", "description": "名称", "nullable": true}, "description": {"type": "string", "description": "描述", "nullable": true}, "inputArgs": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.Args.ComponentFlowNodeTemplateArgDesDto"}, "description": "输入参数描述", "nullable": true}, "outputArgs": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.Args.ComponentFlowNodeTemplateArgDesDto"}, "description": "输出参数描述", "nullable": true}, "flowNodes": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentFlowNodeDto"}, "description": "流程列表", "nullable": true}, "flowData": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.DomainModel.Component.web.fullSubcomponentInfoDto": {"type": "object", "properties": {"name": {"type": "string", "description": "在主零件中子零件名称", "nullable": true}, "className": {"type": "string", "description": "子零件类型", "nullable": true}, "classVersion": {"type": "string", "description": "子零件版本", "nullable": true}, "inheritRule": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Platform.Enums.InheritRuleEnum"}, "attributeTransferRule": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.web.AttributeTransferRuleDto"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.DomainModel.Driver.DriverInfoDto": {"type": "object", "properties": {"index": {"type": "integer", "description": "唯一编号", "format": "int32"}, "name": {"type": "string", "description": "名字", "nullable": true}, "dllFilePath": {"type": "string", "description": "引用dll路径", "nullable": true}, "driverStartParam": {"description": "驱动启动参数", "nullable": true}, "driverStopParam": {"description": "驱动停止参数", "nullable": true}, "classTypeName": {"type": "string", "description": "选择加载的类名", "nullable": true}, "autoReadIntervalTimeMs": {"type": "integer", "description": "自动读取间隔时间", "format": "int32"}}, "additionalProperties": false}, "RH.Product.DCP.DomainModel.Driver.Enums.DriverRunStatus": {"enum": [0, 1, 2, 4, 8, 16], "type": "integer", "description": "驱动运行状态", "format": "int32"}, "RH.Product.DCP.DomainModel.Platform.ActiveUIAttributeInfoDto": {"type": "object", "properties": {"attributeName": {"type": "string", "description": "属性名", "nullable": true}}, "additionalProperties": false, "description": "切换UI事件信息"}, "RH.Product.DCP.DomainModel.Platform.ActiveUIElementsDto": {"type": "object", "properties": {"attributes": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Platform.ActiveUIAttributeInfoDto"}, "description": "需要监听的属性列表", "nullable": true}}, "additionalProperties": false, "description": "切换界面事件"}, "RH.Product.DCP.DomainModel.Platform.AttributeLimitRuleDto": {"type": "object", "properties": {"upperLimit": {"type": "string", "description": "上限", "nullable": true}, "lowerLimit": {"type": "string", "description": "下限", "nullable": true}, "lengthLimit": {"type": "string", "nullable": true}, "regex": {"type": "string", "nullable": true}, "decimalDigits": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.DomainModel.Platform.Enums.InheritRuleEnum": {"enum": [0, 1], "type": "integer", "format": "int32"}, "RH.Product.DCP.DomainModel.Platform.Enums.PlatformValueType": {"enum": [0, 1, 2, 3, 4, 5, 6], "type": "integer", "format": "int32"}, "RH.Product.DCP.DomainModel.Platform.Enums.TransferRuleEnum": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "RH.Product.DCP.DomainModel.Project.Enums.CommunicationType": {"enum": [0, 1, 2], "type": "integer", "description": "通讯类型枚举", "format": "int32"}, "RH.Product.DCP.DomainModel.Project.ProjectBasicInfoDto": {"type": "object", "properties": {"name": {"type": "string", "description": "项目名称", "nullable": true}, "description": {"type": "string", "description": "项目描述", "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "description": "项目标签", "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "createAuthor": {"type": "string", "description": "创建作者", "nullable": true}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time"}, "updateAuthor": {"type": "string", "description": "更新作者", "nullable": true}}, "additionalProperties": false, "description": "项目基本信息"}, "RH.Product.DCP.DomainModel.Project.ProjectBasicInfoWithIdDto": {"type": "object", "properties": {"name": {"type": "string", "description": "项目名称", "nullable": true}, "description": {"type": "string", "description": "项目描述", "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "description": "项目标签", "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "createAuthor": {"type": "string", "description": "创建作者", "nullable": true}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time"}, "updateAuthor": {"type": "string", "description": "更新作者", "nullable": true}, "id": {"type": "string", "description": "项目ID", "nullable": true}}, "additionalProperties": false, "description": "附带项目ID的项目基础信息"}, "RH.Product.DCP.DomainModel.Project.ProjectCommunicationConfigDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "communicationType": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.Enums.CommunicationType"}, "config": {"nullable": true}}, "additionalProperties": false, "description": "项目通讯配置"}, "RH.Product.DCP.DomainModel.Project.ProjectComponentAlarmInfoTreeDto": {"type": "object", "properties": {"index": {"type": "integer", "description": "索引", "format": "int32"}, "id": {"type": "string", "description": "报警唯一ID", "nullable": true}, "name": {"type": "string", "description": "报警名称", "nullable": true}}, "additionalProperties": false, "description": "报警信息"}, "RH.Product.DCP.DomainModel.Project.ProjectComponentAttributeConnectInfoDto": {"type": "object", "properties": {"name": {"type": "string", "description": "属性名称", "nullable": true}, "displayName": {"type": "string", "description": "显示名称", "nullable": true}, "writeDriverName": {"type": "string", "description": "写驱动名", "nullable": true}, "writeParam": {"description": "写参数", "nullable": true}, "readDriverName": {"type": "string", "description": "读驱动名", "nullable": true}, "readParam": {"description": "读参数", "nullable": true}, "monitoringInterval": {"type": "integer", "description": "监控间隔，小于等于0不监听", "format": "int32"}}, "additionalProperties": false, "description": "属性连接信息"}, "RH.Product.DCP.DomainModel.Project.ProjectComponentInfoDto": {"type": "object", "properties": {"name": {"type": "string", "description": "组件名字", "nullable": true, "readOnly": true}, "projectComponentInfoTree": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectComponentInfoTreeDto"}}, "additionalProperties": false}, "RH.Product.DCP.DomainModel.Project.ProjectComponentInfoTreeDto": {"type": "object", "properties": {"name": {"type": "string", "description": "在主零件中子零件名称", "nullable": true}, "className": {"type": "string", "description": "子零件类型", "nullable": true}, "classVersion": {"type": "string", "description": "子零件版本", "nullable": true}, "depthID": {"type": "string", "description": "深度，用各个层级的名字拼接而成", "nullable": true}, "attributeConnectInfos": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectComponentAttributeConnectInfoDto"}, "description": "属性连接信息", "nullable": true}, "alarmInfos": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectComponentAlarmInfoTreeDto"}, "description": "报警信息", "nullable": true}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectComponentInfoTreeDto"}, "description": "子组件信息", "nullable": true}}, "additionalProperties": false, "description": "零件信息树"}, "RH.Product.DCP.DomainModel.Project.ProjectComponentOverviewDto": {"type": "object", "properties": {"name": {"type": "string", "description": "在主零件中子零件名称", "nullable": true}, "className": {"type": "string", "description": "子零件类型", "nullable": true}, "classVersion": {"type": "string", "description": "子零件版本", "nullable": true}, "runStatus": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.Enums.ComponentRunStatus"}, "runStatusStr": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false, "description": "项目零件概要信息"}, "RH.Product.DCP.DomainModel.Project.ProjectDriverOverviewDto": {"type": "object", "properties": {"name": {"type": "string", "description": "名字", "nullable": true}, "dllFilePath": {"type": "string", "description": "引用dll路径", "nullable": true}, "classTypeName": {"type": "string", "description": "选择加载的类名", "nullable": true}, "runStatus": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Driver.Enums.DriverRunStatus"}, "runStatusStr": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "RH.Product.DCP.DomainModel.Project.ProjectGlobalAttributeDto": {"type": "object", "properties": {"name": {"type": "string", "description": "名字", "nullable": true}, "displayName": {"type": "string", "description": "显示名", "nullable": true}, "type": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Platform.Enums.PlatformValueType"}, "description": {"type": "string", "description": "描述", "nullable": true}, "unit": {"type": "string", "description": "单位", "nullable": true}, "defaultValue": {"type": "string", "description": "默认值", "nullable": true}, "isPersistent": {"type": "boolean", "description": "是否可持久化"}, "limitRule": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Platform.AttributeLimitRuleDto"}}, "additionalProperties": false, "description": "全局属性"}, "RH.Product.DCP.DomainModel.Project.ProjectInfoDto": {"type": "object", "properties": {"projectBasicInfo": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectBasicInfoDto"}, "globalAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectGlobalAttributeDto"}, "description": "全局属性", "nullable": true}, "componentsInfos": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectComponentInfoDto"}, "description": "所有零件信息", "nullable": true}, "driverInfos": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Driver.DriverInfoDto"}, "description": "驱动信息", "nullable": true}, "communicationConfigs": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectCommunicationConfigDto"}, "description": "通讯配置信息", "nullable": true}}, "additionalProperties": false, "description": "项目信息"}, "RH.Product.DCP.DomainModel.Project.ProjectOverviewDto": {"type": "object", "properties": {"projectBasicInfo": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectBasicInfoDto"}, "globalAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectGlobalAttributeDto"}, "description": "全局属性", "nullable": true}, "componentsInfos": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectComponentOverviewDto"}, "description": "所有零件信息", "nullable": true}, "driverInfos": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectDriverOverviewDto"}, "description": "驱动信息", "nullable": true}}, "additionalProperties": false, "description": "项目概览信息"}, "RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Component.ComponentAlarmDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"record": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int32", "nullable": true}, "skipCount": {"type": "integer", "format": "int32"}, "attach": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentAlarmDto"}, "dataHeadFields": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.RhData.DataFieldInfo"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Component.web.FullComponentInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"record": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int32", "nullable": true}, "skipCount": {"type": "integer", "format": "int32"}, "attach": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.web.FullComponentInfoDto"}, "dataHeadFields": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.RhData.DataFieldInfo"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Driver.DriverInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"record": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int32", "nullable": true}, "skipCount": {"type": "integer", "format": "int32"}, "attach": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Driver.DriverInfoDto"}, "dataHeadFields": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.RhData.DataFieldInfo"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Project.ProjectComponentInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"record": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int32", "nullable": true}, "skipCount": {"type": "integer", "format": "int32"}, "attach": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectComponentInfoDto"}, "dataHeadFields": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.RhData.DataFieldInfo"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Project.ProjectComponentInfoTreeDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"record": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int32", "nullable": true}, "skipCount": {"type": "integer", "format": "int32"}, "attach": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectComponentInfoTreeDto"}, "dataHeadFields": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.RhData.DataFieldInfo"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.DataResult`1[[RH.Product.DCP.DomainModel.Project.ProjectInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"record": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int32", "nullable": true}, "skipCount": {"type": "integer", "format": "int32"}, "attach": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectInfoDto"}, "dataHeadFields": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.RhData.DataFieldInfo"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Alarm.ProjectAlarmManualDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"record": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int32", "nullable": true}, "skipCount": {"type": "integer", "format": "int32"}, "attach": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Alarm.ProjectAlarmManualDto"}, "nullable": true}, "dataHeadFields": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.RhData.DataFieldInfo"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentAlarmDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"record": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int32", "nullable": true}, "skipCount": {"type": "integer", "format": "int32"}, "attach": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentAlarmDto"}, "nullable": true}, "dataHeadFields": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.RhData.DataFieldInfo"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentAttributeDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"record": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int32", "nullable": true}, "skipCount": {"type": "integer", "format": "int32"}, "attach": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentAttributeDto"}, "nullable": true}, "dataHeadFields": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.RhData.DataFieldInfo"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentBasicInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"record": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int32", "nullable": true}, "skipCount": {"type": "integer", "format": "int32"}, "attach": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentBasicInfoDto"}, "nullable": true}, "dataHeadFields": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.RhData.DataFieldInfo"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.ComponentFunctionDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"record": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int32", "nullable": true}, "skipCount": {"type": "integer", "format": "int32"}, "attach": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.ComponentFunctionDto"}, "nullable": true}, "dataHeadFields": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.RhData.DataFieldInfo"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.SubcomponentInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"record": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int32", "nullable": true}, "skipCount": {"type": "integer", "format": "int32"}, "attach": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.SubcomponentInfoDto"}, "nullable": true}, "dataHeadFields": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.RhData.DataFieldInfo"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Component.web.FullComponentInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"record": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int32", "nullable": true}, "skipCount": {"type": "integer", "format": "int32"}, "attach": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Component.web.FullComponentInfoDto"}, "nullable": true}, "dataHeadFields": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.RhData.DataFieldInfo"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Driver.DriverInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"record": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int32", "nullable": true}, "skipCount": {"type": "integer", "format": "int32"}, "attach": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Driver.DriverInfoDto"}, "nullable": true}, "dataHeadFields": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.RhData.DataFieldInfo"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Project.ProjectBasicInfoWithIdDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"record": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int32", "nullable": true}, "skipCount": {"type": "integer", "format": "int32"}, "attach": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectBasicInfoWithIdDto"}, "nullable": true}, "dataHeadFields": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.RhData.DataFieldInfo"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Project.ProjectComponentOverviewDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"record": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int32", "nullable": true}, "skipCount": {"type": "integer", "format": "int32"}, "attach": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectComponentOverviewDto"}, "nullable": true}, "dataHeadFields": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.RhData.DataFieldInfo"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Project.ProjectDriverOverviewDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"record": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int32", "nullable": true}, "skipCount": {"type": "integer", "format": "int32"}, "attach": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectDriverOverviewDto"}, "nullable": true}, "dataHeadFields": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.RhData.DataFieldInfo"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"record": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int32", "nullable": true}, "skipCount": {"type": "integer", "format": "int32"}, "attach": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dataHeadFields": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.Utility.RhData.DataFieldInfo"}, "nullable": true}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.OpResult": {"type": "object", "properties": {"message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "attach": {"nullable": true}, "record": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.OpResult`1[[RH.Product.DCP.DomainModel.Project.ProjectInfoDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "attach": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectInfoDto"}, "record": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.OpResult`1[[RH.Product.DCP.DomainModel.Project.ProjectOverviewDto, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "attach": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Project.ProjectOverviewDto"}, "record": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "RH.Product.DCP.Utility.CommonModel.OpResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "attach": {"nullable": true}, "record": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "RH.Product.DCP.Utility.RhData.DataFieldInfo": {"type": "object", "properties": {"fieldName": {"type": "string", "nullable": true}, "fieldDescription": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RH.Utils.Core.CommonModel.DataResult`1[[System.Collections.Generic.List`1[[RH.Product.DCP.DomainModel.Alarm.AlarmEventData, RH.Product.DCP.DomainModel, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "attach": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Product.DCP.DomainModel.Alarm.AlarmEventData"}, "nullable": true}, "record": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32", "nullable": true}, "skipCount": {"type": "integer", "format": "int32"}, "dataHeadFields": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Utils.Core.RhData.DataFieldInfo"}, "nullable": true}}, "additionalProperties": false}, "RH.Utils.Core.CommonModel.OpResult": {"type": "object", "properties": {"message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "attach": {"nullable": true}, "record": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "RH.Utils.Core.CommonModel.OrderByClause": {"type": "object", "properties": {"sort": {"type": "string", "nullable": true}, "order": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.OrderSequence"}}, "additionalProperties": false}, "RH.Utils.Core.CommonModel.OrderSequence": {"enum": [0, 1], "type": "integer", "format": "int32"}, "RH.Utils.Core.CommonModel.QueryCharacter": {"enum": [0, 1], "type": "integer", "format": "int32"}, "RH.Utils.Core.CommonModel.QueryCondition": {"type": "object", "properties": {"fieldName": {"type": "string", "nullable": true}, "operator": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.QueryOperator"}, "fieldValue": {"nullable": true}, "character": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.QueryCharacter"}, "propertyFullName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RH.Utils.Core.CommonModel.QueryDescriptor": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "pageIndex": {"type": "integer", "format": "int32"}, "orderBys": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.OrderByClause"}, "nullable": true}, "conditions": {"type": "array", "items": {"$ref": "#/components/schemas/RH.Utils.Core.CommonModel.QueryCondition"}, "nullable": true}}, "additionalProperties": false}, "RH.Utils.Core.CommonModel.QueryOperator": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], "type": "integer", "format": "int32"}, "RH.Utils.Core.RhData.DataFieldInfo": {"type": "object", "properties": {"fieldName": {"type": "string", "nullable": true}, "fieldDescription": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RH.Utils.Core.RhExtension.AutoQueryType": {"enum": [0, 1], "type": "integer", "format": "int32"}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationAuthConfigurationDto": {"type": "object", "properties": {"grantedPolicies": {"type": "object", "additionalProperties": {"type": "boolean"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto": {"type": "object", "properties": {"localization": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationConfigurationDto"}, "auth": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationAuthConfigurationDto"}, "setting": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationSettingConfigurationDto"}, "currentUser": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentUserDto"}, "features": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationFeatureConfigurationDto"}, "globalFeatures": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationGlobalFeatureConfigurationDto"}, "multiTenancy": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.MultiTenancy.MultiTenancyInfoDto"}, "currentTenant": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.MultiTenancy.CurrentTenantDto"}, "timing": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimingDto"}, "clock": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ClockDto"}, "objectExtensions": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ObjectExtensionsDto"}, "extraProperties": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationFeatureConfigurationDto": {"type": "object", "properties": {"values": {"type": "object", "additionalProperties": {"type": "string", "nullable": true}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationGlobalFeatureConfigurationDto": {"type": "object", "properties": {"enabledFeatures": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationConfigurationDto": {"type": "object", "properties": {"values": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"type": "string"}}, "nullable": true}, "resources": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationResourceDto"}, "nullable": true}, "languages": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.Localization.LanguageInfo"}, "nullable": true}, "currentCulture": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentCultureDto"}, "defaultResourceName": {"type": "string", "nullable": true}, "languagesMap": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.NameValue"}}, "nullable": true}, "languageFilesMap": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.NameValue"}}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto": {"type": "object", "properties": {"resources": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationResourceDto"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationResourceDto": {"type": "object", "properties": {"texts": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "baseResources": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationSettingConfigurationDto": {"type": "object", "properties": {"values": {"type": "object", "additionalProperties": {"type": "string", "nullable": true}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ClockDto": {"type": "object", "properties": {"kind": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentCultureDto": {"type": "object", "properties": {"displayName": {"type": "string", "nullable": true}, "englishName": {"type": "string", "nullable": true}, "threeLetterIsoLanguageName": {"type": "string", "nullable": true}, "twoLetterIsoLanguageName": {"type": "string", "nullable": true}, "isRightToLeft": {"type": "boolean"}, "cultureName": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "nativeName": {"type": "string", "nullable": true}, "dateTimeFormat": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.DateTimeFormatDto"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentUserDto": {"type": "object", "properties": {"isAuthenticated": {"type": "boolean"}, "id": {"type": "string", "format": "uuid", "nullable": true}, "tenantId": {"type": "string", "format": "uuid", "nullable": true}, "impersonatorUserId": {"type": "string", "format": "uuid", "nullable": true}, "impersonatorTenantId": {"type": "string", "format": "uuid", "nullable": true}, "impersonatorUserName": {"type": "string", "nullable": true}, "impersonatorTenantName": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "surName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "emailVerified": {"type": "boolean"}, "phoneNumber": {"type": "string", "nullable": true}, "phoneNumberVerified": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.DateTimeFormatDto": {"type": "object", "properties": {"calendarAlgorithmType": {"type": "string", "nullable": true}, "dateTimeFormatLong": {"type": "string", "nullable": true}, "shortDatePattern": {"type": "string", "nullable": true}, "fullDateTimePattern": {"type": "string", "nullable": true}, "dateSeparator": {"type": "string", "nullable": true}, "shortTimePattern": {"type": "string", "nullable": true}, "longTimePattern": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.IanaTimeZone": {"type": "object", "properties": {"timeZoneName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.EntityExtensionDto": {"type": "object", "properties": {"properties": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyDto"}, "nullable": true}, "configuration": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumDto": {"type": "object", "properties": {"fields": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumFieldDto"}, "nullable": true}, "localizationResource": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumFieldDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "value": {"nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiCreateDto": {"type": "object", "properties": {"isAvailable": {"type": "boolean"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiDto": {"type": "object", "properties": {"onGet": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiGetDto"}, "onCreate": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiCreateDto"}, "onUpdate": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiUpdateDto"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiGetDto": {"type": "object", "properties": {"isAvailable": {"type": "boolean"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiUpdateDto": {"type": "object", "properties": {"isAvailable": {"type": "boolean"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyAttributeDto": {"type": "object", "properties": {"typeSimple": {"type": "string", "nullable": true}, "config": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyDto": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "typeSimple": {"type": "string", "nullable": true}, "displayName": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.LocalizableStringDto"}, "api": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiDto"}, "ui": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiDto"}, "attributes": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyAttributeDto"}, "nullable": true}, "configuration": {"type": "object", "additionalProperties": {}, "nullable": true}, "defaultValue": {"nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiDto": {"type": "object", "properties": {"onTable": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiTableDto"}, "onCreateForm": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiFormDto"}, "onEditForm": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiFormDto"}, "lookup": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiLookupDto"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiFormDto": {"type": "object", "properties": {"isVisible": {"type": "boolean"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiLookupDto": {"type": "object", "properties": {"url": {"type": "string", "nullable": true}, "resultListPropertyName": {"type": "string", "nullable": true}, "displayPropertyName": {"type": "string", "nullable": true}, "valuePropertyName": {"type": "string", "nullable": true}, "filterParamName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiTableDto": {"type": "object", "properties": {"isVisible": {"type": "boolean"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.LocalizableStringDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "resource": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ModuleExtensionDto": {"type": "object", "properties": {"entities": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.EntityExtensionDto"}, "nullable": true}, "configuration": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ObjectExtensionsDto": {"type": "object", "properties": {"modules": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ModuleExtensionDto"}, "nullable": true}, "enums": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumDto"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimeZone": {"type": "object", "properties": {"iana": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.IanaTimeZone"}, "windows": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.WindowsTimeZone"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimingDto": {"type": "object", "properties": {"timeZone": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimeZone"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.WindowsTimeZone": {"type": "object", "properties": {"timeZoneId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.MultiTenancy.CurrentTenantDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "name": {"type": "string", "nullable": true}, "isAvailable": {"type": "boolean"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.MultiTenancy.MultiTenancyInfoDto": {"type": "object", "properties": {"isEnabled": {"type": "boolean"}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.ActionApiDescriptionModel": {"type": "object", "properties": {"uniqueName": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "httpMethod": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}, "supportedVersions": {"type": "array", "items": {"type": "string"}, "nullable": true}, "parametersOnMethod": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.MethodParameterApiDescriptionModel"}, "nullable": true}, "parameters": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ParameterApiDescriptionModel"}, "nullable": true}, "returnValue": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ReturnValueApiDescriptionModel"}, "allowAnonymous": {"type": "boolean", "nullable": true}, "implementFrom": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.ApplicationApiDescriptionModel": {"type": "object", "properties": {"modules": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ModuleApiDescriptionModel"}, "nullable": true}, "types": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.TypeApiDescriptionModel"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.ControllerApiDescriptionModel": {"type": "object", "properties": {"controllerName": {"type": "string", "nullable": true}, "controllerGroupName": {"type": "string", "nullable": true}, "isRemoteService": {"type": "boolean"}, "isIntegrationService": {"type": "boolean"}, "apiVersion": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "interfaces": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ControllerInterfaceApiDescriptionModel"}, "nullable": true}, "actions": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ActionApiDescriptionModel"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.ControllerInterfaceApiDescriptionModel": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "methods": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.InterfaceMethodApiDescriptionModel"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.InterfaceMethodApiDescriptionModel": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "parametersOnMethod": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.MethodParameterApiDescriptionModel"}, "nullable": true}, "returnValue": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ReturnValueApiDescriptionModel"}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.MethodParameterApiDescriptionModel": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "typeAsString": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "typeSimple": {"type": "string", "nullable": true}, "isOptional": {"type": "boolean"}, "defaultValue": {"nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.ModuleApiDescriptionModel": {"type": "object", "properties": {"rootPath": {"type": "string", "nullable": true}, "remoteServiceName": {"type": "string", "nullable": true}, "controllers": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ControllerApiDescriptionModel"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.ParameterApiDescriptionModel": {"type": "object", "properties": {"nameOnMethod": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "jsonName": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "typeSimple": {"type": "string", "nullable": true}, "isOptional": {"type": "boolean"}, "defaultValue": {"nullable": true}, "constraintTypes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "bindingSourceId": {"type": "string", "nullable": true}, "descriptorName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.PropertyApiDescriptionModel": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "jsonName": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "typeSimple": {"type": "string", "nullable": true}, "isRequired": {"type": "boolean"}, "minLength": {"type": "integer", "format": "int32", "nullable": true}, "maxLength": {"type": "integer", "format": "int32", "nullable": true}, "minimum": {"type": "string", "nullable": true}, "maximum": {"type": "string", "nullable": true}, "regex": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.ReturnValueApiDescriptionModel": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "typeSimple": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.TypeApiDescriptionModel": {"type": "object", "properties": {"baseType": {"type": "string", "nullable": true}, "isEnum": {"type": "boolean"}, "enumNames": {"type": "array", "items": {"type": "string"}, "nullable": true}, "enumValues": {"type": "array", "items": {}, "nullable": true}, "genericArguments": {"type": "array", "items": {"type": "string"}, "nullable": true}, "properties": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.PropertyApiDescriptionModel"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Localization.LanguageInfo": {"type": "object", "properties": {"cultureName": {"type": "string", "nullable": true}, "uiCultureName": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "twoLetterISOLanguageName": {"type": "string", "nullable": true, "readOnly": true}, "flagIcon": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.NameValue": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}